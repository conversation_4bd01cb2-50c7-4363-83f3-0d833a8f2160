/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: LanguageUtilTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.base.utils

import android.content.Context
import android.os.Build
import android.text.TextUtils
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.core.classloader.annotations.PrepareForTest
import org.robolectric.annotation.Config
import java.util.Locale

@RunWith(AndroidJUnit4::class)
@PrepareForTest(Locale::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class LanguageUtilTest {

    private lateinit var context: Context

    @Before
    fun setUp() {
        context = InstrumentationRegistry.getInstrumentation().targetContext
    }

    @Test
    fun should_empty_when_getCurrentLanguageFromSystem() {
        val language = LanguageUtil.getCurrentLanguageFromSystem()
        Assert.assertTrue(!TextUtils.isEmpty(language))
    }

    @Test
    fun should_empty_when_isZHCN() {
        val isChina = LanguageUtil.isZHCN()
        Assert.assertFalse(isChina)
    }

    @Test
    fun should_empty_when_isZHTW() {
        val isZHTW = LanguageUtil.isZHTW()
        Assert.assertFalse(isZHTW)
    }

    @Test
    fun should_empty_when_isZHHK() {
        val isZHHK = LanguageUtil.isZHHK()
        Assert.assertFalse(isZHHK)
    }

    @Test
    fun should_empty_when_isUG() {
        val isUG = LanguageUtil.isUG()
        Assert.assertFalse(isUG)
    }

    @Test
    fun should_empty_when_isBO() {
        val isUG = LanguageUtil.isBO()
        Assert.assertFalse(isUG)
    }

    /**
     * 验证getLanguageDisplayName方法的正确性
     */
    @Test
    fun test_get_language_display_name() {
        val languageCode = "zh"
        val displayName = LanguageUtil.getLanguageDisplayName(context, languageCode)
        Assert.assertNotNull(displayName)

        val languageCode2 = "123"
        val displayName2 = LanguageUtil.getLanguageDisplayName(context, languageCode2)
        Assert.assertNull(displayName2)
    }

    /**
     * 验证getRegionSupportedLanguages读取出来的语种code是否都有对应的词条映射
     * 这个测试确保配置文件中的所有语言代码都能通过getLanguageDisplayName方法获取到对应的显示名称
     */
    @Test
    fun test_get_region_supported_languages_have_display_name_mapping_domestic() {
        should_all_region_supported_languages_have_display_name_mapping(false)
    }
    @Test
    fun test_get_region_supported_languages_have_display_name_mapping_export() {
        should_all_region_supported_languages_have_display_name_mapping(true)
    }

    private fun should_all_region_supported_languages_have_display_name_mapping(isEXP: Boolean) {
        // 获取区域支持的语言列表
        val supportedLanguages = LanguageUtil.getRegionSupportedLanguages(isEXP)

        // 验证不为空
        Assert.assertFalse("支持的语言列表不应为空", supportedLanguages.isEmpty())

        // 记录没有映射的语言代码
        val unmappedLanguages = mutableListOf<String>()
        val mappedLanguages = mutableMapOf<String, String>()

        // 遍历每个语言代码，检查是否有对应的显示名称
        supportedLanguages.forEach { languageCode ->
            val displayName = LanguageUtil.getLanguageDisplayName(context, languageCode)
            if (displayName == null) {
                unmappedLanguages.add(languageCode)
            } else {
                mappedLanguages[languageCode] = displayName
            }
        }

        // 断言：所有语言代码都应该有对应的显示名称
        Assert.assertTrue(
            "以下语言代码缺少显示名称映射: $unmappedLanguages\n" +
            "请在LanguageUtil.getLanguageDisplayName方法中添加对应的映射，" +
            "或在strings.xml中添加对应的字符串资源",
            unmappedLanguages.isEmpty()
        )

        // 验证映射的显示名称不为空
        mappedLanguages.forEach { (code, name) ->
            Assert.assertFalse(
                "语言代码 $code 的显示名称不应为空字符串",
                name.isBlank()
            )
        }
    }

    /**
     * 测试getAsrLangMap方法，验证它能正确处理所有支持的语言
     */
    @Test
    fun test_get_asr_lang_map_handle_all_supported_languages_domestic() {
        should_get_asr_lang_map_handle_all_supported_languages(false)
    }
    @Test
    fun test_get_asr_lang_map_handle_all_supported_languages_export() {
        should_get_asr_lang_map_handle_all_supported_languages(true)
    }

    private fun should_get_asr_lang_map_handle_all_supported_languages(isEXP: Boolean) {
        // 获取区域支持的语言列表
        val supportedLanguages = LanguageUtil.getRegionSupportedLanguages(isEXP).toList()

        // 调用getAsrLangMap方法
        val langMap = LanguageUtil.getAsrLangMap(context, supportedLanguages)

        // 验证返回的映射表不为空
        Assert.assertFalse("语言映射表不应为空", langMap.isEmpty())

        // 验证映射表的大小与输入语言列表大小一致
        Assert.assertEquals(
            "映射表大小应与输入语言列表大小一致",
            supportedLanguages.size,
            langMap.size
        )

        // 验证每个语言代码都在映射表中
        supportedLanguages.forEach { languageCode ->
            Assert.assertTrue(
                "语言代码 $languageCode 应该在映射表中",
                langMap.containsKey(languageCode)
            )

            val displayName = langMap[languageCode]
            Assert.assertNotNull(
                "语言代码 $languageCode 的显示名称不应为null",
                displayName
            )

            Assert.assertFalse(
                "语言代码 $languageCode 的显示名称不应为空字符串",
                displayName!!.isBlank()
            )
        }
    }
}