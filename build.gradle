// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        maven {
            url prop_oppoMavenUrlRelease
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }
        maven {
            url prop_oppoMavenUrlMaven
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }
        maven {
            url prop_oapmMavenUrl
            allowInsecureProtocol = true
        }

        maven {
            url prop_mavenUrlCloudKit
            allowInsecureProtocol = true
            credentials {
                username MAVEN_USERNAME
                password MAVEN_PASSWORD
            }
        }

        maven() {
            url prop_mavenUrlCloudKitRelease
            allowInsecureProtocol = true
            credentials {
                username MAVEN_USERNAME
                password MAVEN_PASSWORD
            }
        }

        maven {
            url prop_oppoSmallCardMavenUrlRelease
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }

        maven {
            allowInsecureProtocol = true
            url prop_oppoMavenUrlSnapshot

            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
    }
    dependencies {
        classpath libs.android.gradle.plugin
        classpath libs.kotlin.gradle.plugin
        classpath libs.oppo.oapm
        classpath libs.oppo.buildplugin
        classpath libs.detekt.gradle.plugin
        classpath libs.protobuf.gradle.plugin
        classpath libs.oplus.autotest
    }
}

allprojects {
    repositories {
        maven {
            url prop_oppoMavenUrlRelease
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }
        maven {
            url prop_oppoCloudSdkMavenUrl
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }
        maven {
            url prop_oapmMavenUrl
            allowInsecureProtocol = true
        }
        maven {
            url prop_mavenUrlCloudKit
            allowInsecureProtocol = true
            credentials {
                username MAVEN_USERNAME
                password MAVEN_PASSWORD
            }
        }
        maven() {
            url prop_mavenUrlCloudKitRelease
            allowInsecureProtocol = true
            credentials {
                username MAVEN_USERNAME
                password MAVEN_PASSWORD
            }
        }

        maven {
            allowInsecureProtocol = true
            //need use oppo maven,due to google repo is not available in services
            url prop_sdkMavenUrlRelease
        }


        maven {
            url prop_oppoSmallCardMavenUrlRelease
            allowInsecureProtocol = true
            credentials {
                username sonatypeUsername
                password sonatypeUsername
            }
        }

        maven {
            allowInsecureProtocol = true
            url prop_oppoMavenUrlSnapshot

            credentials {
                username sonatypeUsername
                password sonatypePassword
            }
        }
    }

    configurations {
        all*.exclude group: "com.squareup.okhttp3", module: "okhttp"
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

apply plugin: 'ocoverage'
apply from: rootProject.projectDir.path + '/testLibConfig.gradle'
//代码扫描依赖
apply plugin: "io.gitlab.arturbosch.detekt"

// 在这里为每个模块引入detekt扫描
subprojects {
    apply plugin: "io.gitlab.arturbosch.detekt"
    tasks.detekt.jvmTarget = JavaVersion.VERSION_17
    detekt {
        config=files("$rootDir/config/detekt/detekt_config.yml")
        reports {
            html.enabled=true
        }
        autoCorrect = true // 自动格式化代码的总开关
        setSource(files("$projectDir/src/main"))
//        setSource(files("$projectDir/src/main/java/oplus/multimedia/soundrecorder/views/"))//需要针对某个文件夹修改可以使用指定路径
    }

    dependencies {
        detektPlugins libs.detekt.gradle.formatting
        //需要使用maven：http://mirror-maven.myoas.com/repository/ars-sdk-release/
        detektPlugins libs.detekt.check
    }

    // 专门用于format的task
    task detektFormat(type: io.gitlab.arturbosch.detekt.Detekt) {
        description = "Reformat kotlin code."
        config.setFrom(files("$rootDir/config/detekt/detekt_config.yml"))
        setSource(files("$projectDir/src/main"))

//        setSource(files("$projectDir/src/main/java/oplus/multimedia/soundrecorder/views/"))//需要针对某个文件夹修改可以使用指定路径
        autoCorrect = true
        include("**/*.kt")
    }
}