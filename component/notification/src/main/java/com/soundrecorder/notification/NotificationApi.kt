/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonNotificationManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification

import android.app.KeyguardManager
import android.app.Service
import android.content.Context.KEYGUARD_SERVICE
import android.content.IntentFilter
import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.modulerouter.notification.NotificationInterface
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.base.BaseNotification
import com.soundrecorder.notification.base.cancelAllNotificationByForce
import com.soundrecorder.notification.common.CommonFastPlayNotification
import com.soundrecorder.notification.common.CommonPlaybackNotification
import com.soundrecorder.notification.common.CommonRecordNotification
import com.soundrecorder.notification.third.ThirdPartyFastPlayNotification
import com.soundrecorder.notification.third.ThirdPartyPlaybackNotification
import com.soundrecorder.notification.third.ThirdPartyRecordNotification

object NotificationApi : NotificationInterface {

    private const val NUM_10 = 10
    @VisibleForTesting
    internal val notificationMap = mutableMapOf<Int, BaseNotification>()

    override fun showNotification(mode: Int, page: Int, notificationModel: NotificationModel?, service: Service?) {
        val notificationId = getNotificationIdByModeAndPage(mode, page)
        var notification = notificationMap[notificationId]
        if (notification == null) {
            notification = createNotificationById(mode, page, notificationId)
        }
        notification?.let {
            it.setNotificationModelFromOrigin(notificationModel)
            cancelGroupOtherView(notification.getGroupId(), notificationId)
            it.showNotification(service)
            notificationMap[notificationId] = it
        }
    }

    @JvmStatic
    internal fun cancelGroupOtherView(groupId: Int, notificationId: Int) {
        val iterator = notificationMap.entries.iterator()
        var item: MutableMap.MutableEntry<Int, BaseNotification>?
        while (iterator.hasNext()) {
            item = iterator.next()
            if (groupId == item.value.getGroupId()) {
                if (notificationId == item.value.getNotificationId()) {
                    item.value.onRelease()
                    iterator.remove()
                } else {
                    item.value.cancelNotification()
                    iterator.remove()
                }
            }
        }
    }

    @JvmStatic
    private fun createNotificationById(mode: Int, page: Int, notificationId: Int): BaseNotification? {
        val groupId = getGroupIdByModeAndPage(mode, page)
        return when (page) {
            NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY -> {
                when (mode) {
                    NotificationUtils.NOTIFICATION_MODE_COMMON ->
                        CommonFastPlayNotification(groupId, notificationId)
                    NotificationUtils.NOTIFICATION_MODE_THIRD ->
                        ThirdPartyFastPlayNotification(groupId, notificationId)
                    else -> null
                }
            }
            NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK -> {
                when (mode) {
                    NotificationUtils.NOTIFICATION_MODE_COMMON ->
                        CommonPlaybackNotification(groupId, notificationId)
                    NotificationUtils.NOTIFICATION_MODE_THIRD ->
                        ThirdPartyPlaybackNotification(groupId, notificationId)
                    else -> null
                }
            }
            NotificationUtils.NOTIFICATION_PAGE_RECORD -> {
                when (mode) {
                    NotificationUtils.NOTIFICATION_MODE_COMMON ->
                        CommonRecordNotification(groupId, notificationId)
                    NotificationUtils.NOTIFICATION_MODE_THIRD ->
                        ThirdPartyRecordNotification(groupId, notificationId)
                    else -> null
                }
            }
            else -> null
        }
    }

    override fun getGroupIdByModeAndPage(mode: Int?, page: Int?): Int {
        return when (page) {
            NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY,
            NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK,
            NotificationUtils.NOTIFICATION_PAGE_EDIT_RECORD ->
                NotificationUtils.NOTIFICATION_PLAY_ID
            NotificationUtils.NOTIFICATION_PAGE_RECORD -> {
                when (mode) {
                    NotificationUtils.NOTIFICATION_MODE_COMMON ->
                        NotificationUtils.NOTIFICATION_COMMON_RECORDE_ID
                    NotificationUtils.NOTIFICATION_MODE_THIRD ->
                        NotificationUtils.NOTIFICATION_THIRDPARTY_RECORDE_ID
                    else -> -1
                }
            }
            else -> -1
        }
    }

    override fun getNotificationIdByModeAndPage(mode: Int?, page: Int?): Int {
        return if ((mode != null) && (mode >= 0)
            && (page != null) && (page >= 0)) {
            NUM_10 * mode + page
        } else {
            -1
        }
    }

    override fun getModeAndPageByNotificationId(notificationId: Int): IntArray {
        val mode = notificationId / NUM_10
        val page = notificationId % NUM_10
        return intArrayOf(mode, page)
    }

    override fun getNotificationMode(isFromOtherApp: Boolean): Int {
        return when {
            isFromOtherApp || !OS12FeatureUtil.isSuperSoundRecorderEpicEffective() ->
                NotificationUtils.NOTIFICATION_MODE_THIRD
            else ->
                NotificationUtils.NOTIFICATION_MODE_COMMON
        }
    }

    /**
     * 取消通知，要求page相同
     * 目前用于录制页面取消掉与当前页面相同的播放通知
     * @param page 页面pageId
     */
    override fun cancelNotificationByPage(page: Int) {
        val iterator = notificationMap.entries.iterator()
        var item: MutableMap.MutableEntry<Int, BaseNotification>? = null
        while (iterator.hasNext()) {
            item = iterator.next()
            if (page == getModeAndPageByNotificationId(item.value.getNotificationId())[1]) {
                item.value.cancelNotification()
                iterator.remove()
            }
        }
    }

    /**
     * 取消通知，要求groupId相同&mode相同
     * 目前用于裁切页面取消掉与当前页面相同入口栈的播放通知
     * @param mode common/third
     * @param page 页面pageId
     */
    override fun cancelNotificationModeAndGroup(mode: Int, page: Int) {
        val groupId = getGroupIdByModeAndPage(mode, page)
        val iterator = notificationMap.entries.iterator()
        var item: MutableMap.MutableEntry<Int, BaseNotification>?
        while (iterator.hasNext()) {
            item = iterator.next()
            if ((groupId == item.value.getGroupId()) &&
                (mode == getModeAndPageByNotificationId(item.value.getNotificationId())[0])) {
                item.value.cancelNotification()
                iterator.remove()
            }
        }
    }

    override fun cancelAllNotification() {
        if (notificationMap.isNotEmpty()) {
            val iterator = notificationMap.entries.iterator()
            while (iterator.hasNext()) {
                iterator.next().value.cancelNotification()
                iterator.remove()
            }
        } else {
            //关闭权限导致重建时map中数据不存在
            cancelAllNotificationByForce()
        }
    }

    /**
     * 取消通知
     * @param mode common/third
     * @param page 页面pageId
     */
    override fun cancelNotification(mode: Int, page: Int) {
        val notificationType = getNotificationIdByModeAndPage(mode, page)
        notificationMap[notificationType]?.cancelNotification()
        notificationMap.remove(notificationType)
    }

    override fun getIntentFilter(): IntentFilter {
        return IntentFilter().also {
            it.addAction(NotificationUtils.PLAY_STATUS_CHANGED_ACTION)
            it.addAction(NotificationUtils.MARK_CHANGED_ACTION)
            it.addAction(NotificationUtils.SAVE_CHANGED_ACTION)
        }
    }

    override fun isLockScreen(): Boolean {
        val manager = BaseApplication.getApplication()
            .getSystemService(KEYGUARD_SERVICE) as KeyguardManager
        val isLockScreen = manager.isKeyguardLocked
        DebugUtil.d("NotificationApi", "isLockScreen == $isLockScreen")
        return isLockScreen
    }
}