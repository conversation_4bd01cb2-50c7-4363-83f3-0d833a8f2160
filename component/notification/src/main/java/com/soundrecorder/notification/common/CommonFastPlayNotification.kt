/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonFastPlayNotification
 * * Description :  首页快速播放锁屏标记notification
 * * Version     : 1.0
 * * Date        : 2022/07/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.common

import android.app.PendingIntent
import android.content.Intent
import com.soundrecorder.common.utils.AppCardUtils.launchDisplay
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.notification.R

class CommonFastPlayNotification(groupId: Int, notificationId: Int) :
    CommonNotification(groupId, notificationId) {

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    override var logTag: String
        get() = "CommonFastPlayNotification"
        set(value) {}

    override fun getOldChannelId(): String {
        return NotificationUtils.BROWSFILE_OLD_CID
    }

    override fun getChannelId(): String {
        return NotificationUtils.BROWSFILE_CID
    }

    override fun getChannelName(): String {
        return defaultContext.resources.getString(R.string.play_shortcut_channel_name)
    }

    override fun getJumpIntent(): Intent? {
        return browseFileApi?.createBrowseFileIntent(defaultContext)?.also {
            it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
    }

    override fun getOtherDisplayContentIntent(): PendingIntent? {
        return getJumpIntent()?.run {
            PendingIntent.getActivity(
                defaultContext,
                PENDING_INTENT_REQUEST_CODE,
                this,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE,
                DisplayUtils.mainId.launchDisplay()
            )
        }
    }
}