/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: QuestionnaireApi
 Description:
 Version: 1.0
 Date: 2023/05/30 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/30 1.0 create
 */

package com.oplus.recorder.questionnaire

import android.app.Application
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.coloros.feedback.env.EnvironmentApi
import com.oplus.questionnaire.CDPCallback
import com.oplus.questionnaire.CDPSDK
import com.oplus.questionnaire.CdpView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.questionnaire.QuestionCDPAnimCallback
import com.soundrecorder.modulerouter.questionnaire.QuestionCDPCallback
import com.soundrecorder.modulerouter.questionnaire.QuestionnaireInterface
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object QuestionnaireApi : QuestionnaireInterface {

    private const val TAG = "QuestionnaireApi"

    /**
     * 初始化CDP SDK
     */
    override fun initSDK(context: Application) {
        DebugUtil.d(TAG, "initSDK  :$context  ")
        CDPSDK.initSdk(context, BuildConfig.QUESTIONNAIRE_ID, BuildConfig.QUESTIONNAIRE_KEY, BuildConfig.QUESTIONNAIRE_CODE)
        if (BuildConfig.DEBUG) {
            CDPSDK.setEnv(EnvironmentApi.ENV.TEST)
        } else {
            CDPSDK.setEnv(EnvironmentApi.ENV.RELEASE)
            //表示可下发已审核通过数据
            CDPSDK.setMode(EnvironmentApi.CDPMode.PROD)
        }
        CDPSDK.setListCardAnimation(false)
    }

    /**
     * 获取CdpView
     */
    override fun initView(context: Context?, rootView: ViewGroup?): View? {
        DebugUtil.d(TAG, "getCdpView  :$rootView")
        return LayoutInflater.from(context).inflate(R.layout.questionnaire_cdp_view, rootView, false)
    }

    override fun setCDPCallBack(cdpView: View, callback: QuestionCDPCallback?, animCallback: QuestionCDPAnimCallback?) {
        (cdpView as? CdpView)?.setCdpCallback(object : CDPCallback {
            override fun callback(code: Int, msg: String, data: String?) {
                DebugUtil.d(TAG, "code == $code, msg $msg")
                callback?.callback(code, msg, data)
            }
        })
        //先传null，后续SDK动效方案实施再传回调接口
        (cdpView as? CdpView)?.setAnimCallBack(null)
    }

    /**
     * 请求问卷数据并显示
     */
    override fun updateSpace(coroutineScope: CoroutineScope, cdpView: View) {
        DebugUtil.d(TAG, "update space :$cdpView")
        if (cdpView is CdpView) {
            coroutineScope.launch {
                withContext(Dispatchers.IO) {
                    CDPSDK.onlyGetSpaceData()
                }
                cdpView.updateSpaceByCacheData()
            }
        }
    }

    /**
     * 判断当前mCdpView是否在显示
     */
    override fun cdpViewIsVisible(cdpView: View): Boolean {
        DebugUtil.d(TAG, "cdpViewIsVisible  :$cdpView")
        return cdpView.isVisible
    }

    /**
     * 释放资源
     */
    override fun releaseSpace(cdpView: View?) {
        DebugUtil.d(TAG, "releaseSpace  :$cdpView")
        if (cdpView != null && cdpView is CdpView) {
            cdpView.releaseSpace()
            cdpView.setCdpCallback(null)
            cdpView.setAnimCallBack(null)
        }
    }
}