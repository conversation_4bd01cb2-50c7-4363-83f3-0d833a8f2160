/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForSellMode.kt
 * * Description : AutoDiForSellMode
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.sellmode.di

import com.soundrecorder.modulerouter.sellmode.SellModeInterface
import com.soundrecorder.sellmode.SellModeApi
import org.koin.dsl.module

object AutoDiForSellMode {
    val sellModeModule = module {
        single<SellModeInterface>(createdAtStart = true) {
            SellModeApi
        }
    }
}