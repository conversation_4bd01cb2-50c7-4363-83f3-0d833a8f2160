/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SellModeApi
 Description:
 Version: 1.0
 Date: 2022/12/13
 Author: W9013333(v-zhengt<PERSON><PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/12/13 1.0 create
 */

package com.soundrecorder.sellmode

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import com.soundrecorder.modulerouter.sellmode.SellModeInterface
import com.soundrecorder.sellmode.SellModeService.Companion.checkAndStartSellModeService

object SellModeApi : SellModeInterface {
    /**
     * 卖场模式初始化,每次初始都会去检查是否需要预置数据，同时注册预置数据任务触发时机
     */
    override fun checkAndStartSellModeService(context: Context) {
        context.checkAndStartSellModeService()
    }

    /**
     * 卖场模式屏幕监听
     */
    override fun createSellModeScreenStateListener(lifecycleOwner: LifecycleOwner, screenChangedCheck: (() -> Bo<PERSON>an)?) {
        SellModeScreenStateLiveData(lifecycleOwner, screenChangedCheck)
    }
}