/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForPhotoViewer.kt
 * * Description : AutoDiForPhotoViewer
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.photoviewer.di

import com.photoviewer.PhotoViewerApi
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerInterface
import org.koin.dsl.module

object AutoDiForPhotoViewer {
    val photoViewerModule = module {
        single<PhotoViewerInterface>(createdAtStart = true) {
            PhotoViewerApi
        }
    }
}