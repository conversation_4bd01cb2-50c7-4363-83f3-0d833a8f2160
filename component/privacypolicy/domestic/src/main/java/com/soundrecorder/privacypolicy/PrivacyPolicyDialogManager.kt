/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyInfoManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.statement.COUIIndividualStatementDialog
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.coui.appcompat.statement.PrivacyItem
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.PrefUtil.KEY_RECORD_FEEDBACK_PERMISSION
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity.Companion.syncPrivacyDialogShowingType
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.permission.PermissionUtils.setAllFuncTypePermission
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.VerifyCallBack
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_CLOUD_SYNC
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_FEEDBACK
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SMART_GENERATION
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SUMMARY_MIND
import com.soundrecorder.modulerouter.smartname.IPluginDownloadCallback
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

open class PrivacyPolicyDialogManager(
    private val context: AppCompatActivity,
    private val resultCallback: IPrivacyPolicyResultListener?
) {

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    companion object {
        private const val TAG = "PrivacyPolicyDialogManager"

        @JvmStatic
        fun newInstance(
            type: Int = IPrivacyPolicyDelegate.POLICY_TYPE_COMMON,
            context: AppCompatActivity,
            resultListener: IPrivacyPolicyResultListener?
        ): PrivacyPolicyDialogManager {
            return when (type) {
                IPrivacyPolicyDelegate.POLICY_TYPE_COMMON -> PrivacyPolicyDialogManager(context, resultListener)
                IPrivacyPolicyDelegate.POLICY_TYPE_MINI -> MiniPrivacyPolicyDialogManager(context, resultListener)
                else -> PrivacyPolicyDialogManager(context, resultListener)
            }
        }
    }

    private val dialogMap = mutableMapOf<Int, COUIBottomSheetDialog>()

    /*记录当前最后一次显示的用户弹窗Type*/
    private var currentShowingDialogType: Int? = null
    private val showDialogKeys = arrayListOf<Int>()
    private val statementView by lazy {
        View(context).apply {
            setBackgroundColor(
                ContextCompat.getColor(
                    context,
                    com.support.appcompat.R.color.coui_color_white
                )
            )
        }
    }
    private var isReCreate = false
    var hasConvertPermission = false
    private var disableDialog: AlertDialog? = null

    private fun addStatementView() {
        if (statementView.parent == null) {
            context.addContentView(
                statementView, ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            )
        }
    }


    private fun isRegionAndSupportCloud(): Boolean = cloudKitApi?.isSupportCloudArea() == true && cloudKitApi?.isSupportSwitch() == true

    private fun removeStatementView() {
        (statementView.parent as? ViewGroup)?.removeView(statementView)
    }

    /**
     * 是否支持撤销个保法权限
     */
    private fun canShowWithdrawnPermissionConvert(context: Context): Boolean {
        return PermissionUtils.isStatementConvertGranted(context) ||
                (isRegionAndSupportCloud() && cloudKitApi?.isStatementCloudGranted(context) == true)
    }

    @Suppress("LongParameterList")
    protected open fun createDialog(
        title: String,
        message: CharSequence,
        protocolTxt: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit,
        cancelable: Boolean = false
    ): COUIBottomSheetDialog {
        return COUIUserStatementDialog(context).apply {
            this.titleText = title
            this.bottomButtonText = ok
            this.exitButtonText = cancel
            this.statement = message
            this.protocolText = protocolTxt
            setIsShowInMaxHeight(false)
            this.onButtonClickListener =
                object : COUIUserStatementDialog.OnButtonClickListener {
                    override fun onBottomButtonClick() {
                        clickOk.invoke()
                    }

                    override fun onExitButtonClick() {
                        clickCancel.invoke()
                    }
                }
            bottomDialog(this, cancelable, clickBack, clickCancel)
        }
    }

    @Suppress("LongParameterList")
    protected open fun createUserNoticeDialog(
        title: String,
        message: CharSequence,
        protocolTxt: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit,
        isUserNoticeMain: Boolean = false
    ): COUIBottomSheetDialog {
        val dialog = COUIUserStatementDialog(context).apply {
            titleText = title
            bottomButtonText = ok
            exitButtonText = cancel
            statement = message
            protocolText = protocolTxt
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onBottomButtonClick() {
                    clickOk.invoke()
                }

                override fun onExitButtonClick() {
                    clickCancel.invoke()
                }
            }
            setOnCancelListener { clickBack.invoke() }
        }
        if (isUserNoticeMain) {
            dialog.logoDrawable = context.getDrawable(com.soundrecorder.common.R.drawable.ic_user_notice)
            dialog.appMessage = context.resources.getString(com.soundrecorder.common.R.string.audio_recording_playback)
            dialog.appName = context.getString(com.soundrecorder.common.R.string.app_name_main)
        } else {
            dialog.setIsShowInMaxHeight(false)
        }
        return dialog
    }

    private fun bottomDialog(dialog: COUIBottomSheetDialog, cancelable: Boolean, clickBack: () -> Unit, clickCancel: () -> Unit) {
        dialog.apply {
            setCanceledOnTouchOutside(cancelable)
            setCancelable(cancelable)
            behavior.isDraggable = false
            dragableLinearLayout?.dragView?.visibility = View.INVISIBLE
            setOnKeyListener { _, keyCode, event ->
                val isBack =
                    keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP
                if (isShowing && isBack) {
                    clickBack.invoke()
                }
                false
            }
            if (cancelable) {
                setOnCancelListener {
                    clickCancel.invoke()
                }
            }
        }
    }

    private fun doDismiss(type: Int, isPerformAnim: Boolean = true, remove: Boolean = false) {
        dialogMap[type]?.dismiss(isPerformAnim)
        /*重建、每次onresume隐藏再显示都是用的该map,所以弹窗消失不代表没有弹窗，不能随便remove*/
        if (remove) {
            dialogMap.remove(type)
        }
    }

    private fun COUIBottomSheetDialog.addAndShowDialog(type: Int) {
        show()
        DebugUtil.d(TAG, "addAndShowDialog: dialog type = $type, show() has been called!")
        doDismiss(type)
        dialogMap[type] = this
        syncPrivacyDialogShowingType = type
    }

    /**
     * 用户须知-总概览-欢迎使用
     */
    private fun doShowDialogUserNotice() {
        val title = context.resources.getString(com.soundrecorder.common.R.string.welcome_use)
        val message = createSpan(
            PrivacyPolicyConstant.TYPE_USER_NOTICE,
            if (BaseUtil.isAndroidTOrLater) {
                R.string.privacy_policy_user_notice_all_t_v5
            } else {
                R.string.privacy_policy_user_notice_all_v2
            },
            R.string.privacy_policy_basic,
        )

        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_USER_NOTICE,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                //onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                BuryingPoint.doClickOkOnUserNoticeDialog()
                setAllFuncTypePermission(context, true)
                doShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_AI_ENGINE, PrivacyPolicyConstant.TYPE_USER_NOTICE)
            },
            clickCancel = {
                onClickCancel(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL)
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE)
            },
            clickBack = {
                context.finish()
            },
            isUserNoticeMain = true
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE)
    }

    /**
     * 用户须知-使用基本功能
     */
    private fun doShowDialogUserNoticeBasic() {
        val title = context.resources.getString(R.string.privacy_policy_user_notice_basic_title)
        val message = context.resources.getString(
            if (BaseUtil.isAndroidTOrLater) {
                R.string.privacy_policy_user_notice_basic_t_v4
            } else {
                R.string.privacy_policy_user_notice_basic_v3
            }
        )
        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_start_use)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.back)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                //onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
                removeStatementView()
                BuryingPoint.doClickOkOnUserNoticeBasicDialog()
                onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC, emptyList())
            },
            clickCancel = {
                doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
            },
            clickBack = {
                doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
            }
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
    }

    /**
     * 用户须知-仍然使用基本功能
     * 仍可以使用基本功能
     */
    private fun doShowDialogUserNoticeStill() {
        val title = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_deny_title)
        val message = context.resources.getString(
            if (BaseUtil.isAndroidTOrLater) {
                R.string.privacy_policy_user_notice_basic_still_t_v4
            } else {
                R.string.privacy_policy_user_notice_basic_still_v3
            }
        )
        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_start_use)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                //onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL)
                BuryingPoint.doClickOkOnUserNoticeBasicStillDialog()
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL)
                removeStatementView()
                onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL, emptyList())
            },
            clickCancel = {
                removeStatementView()
                context.finish()
            },
            clickBack = {
                doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL)
            }
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL)
    }

    /**
     * 用户须知-轻量
     */
    private fun doShowDialogUserNoticeLight() {
        val title = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_statement_title)
        val message = context.resources.getString(
            if (BaseUtil.isAndroidTOrLater) {
                R.string.privacy_policy_user_notice_for_light_t_v2
            } else {
                R.string.privacy_policy_user_notice_for_light
            }
        )
        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_start_use)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                //onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS)
                doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS)
                setAllFuncTypePermission(context, true)
                doShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_AI_ENGINE, PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS)
            },
            clickCancel = {
                context.finish()
            },
            clickBack = {
                context.finish()
            },
            isUserNoticeMain = true
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS)
    }

    /**
     * 用户须知-升级弹窗
     */
    private fun doShowDialogUserNoticeUpdate() {
        val title = context.resources.getString(R.string.user_notice_update_title)
        val message = context.resources.getString(R.string.user_notice_update_content_for_ck_v3)
        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
                //开启全量功能+网络权限且弹窗消失
                onClickFromPrivacyPolicy(
                    PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE,
                    arrayListOf(FUNC_TYPE_SMART_GENERATION, FUNC_TYPE_CLOUD_SYNC, FUNC_TYPE_SUMMARY_MIND, FUNC_TYPE_FEEDBACK)
                )
            },
            clickCancel = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
                //进入应用   关闭云服务
                onClickCancel(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
            },
            clickBack = {
                context.finish()
            }
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
    }

    /**
     * 转文本声明-转文本/云同步
     * 同意此功能对信息的处理
     */
    private fun doShowDialogPermissionConvert(type: Int, pageFrom: Int?) {
        val title = context.resources.getString(
            R.string.privacy_policy_convert_permission_notice_title
        )
        val message =
            context.resources.getString(R.string.privacy_policy_convert_permission_notice_v2)
        val protocolText = createSpan(
            type,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(type)
                if (type == PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD) {
                    cloudKitApi?.setCloudGrantedStatus(context)
                } else {
                    // 转文本的同意此功能更对信息的处理
                    PermissionUtils.setConvertGrantedStatus(BaseApplication.getAppContext())
                    PermissionUtils.setNetWorkGrantedStatus(BaseApplication.getAppContext(), true)
                    BuryingPoint.doClickOkOnConvertPermissionDialog()
                }
                PermissionUtils.setNetWorkGrantedStatus(context, true)
                resultCallback?.onPrivacyPolicySuccess(type, pageFrom)
            },
            clickCancel = {
                doDismiss(type)
                resultCallback?.onPrivacyPolicyFail(type, pageFrom)
            },
            clickBack = {
                doDismiss(type)
                resultCallback?.onPrivacyPolicyFail(type, pageFrom)
            },
            cancelable = type != PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD
        )
        dialog.addAndShowDialog(type)
    }

    /**
     * 撤销转文本
     */
    private fun doShowDialogPermissionConvertWithdrawn(isRegionAndSupportCloud: Boolean = false) {
        DebugUtil.d(TAG, "doShowDialogPermissionConvertWithdrawn, supportCloud=$isRegionAndSupportCloud")
        val title = context.resources.getString(
            R.string.privacy_policy_privacy_policy_close
        )
        val message = context.resources.getString(
            if (isRegionAndSupportCloud) {
                R.string.privacy_policy_privacy_policy_close_notice_v2_with_ck_v3
            } else {
                R.string.privacy_policy_privacy_policy_close_notice_v2
            }
        )
        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(R.string.privacy_policy_privacy_policy_not_withdrawn)
        val cancel = context.resources.getString(R.string.privacy_policy_privacy_policy_withdrawn)
        val dialog = createDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
            },
            clickCancel = {
                resultCallback?.onPrivacyPolicyFail(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
                setAllFuncTypePermission(BaseApplication.getAppContext(), false)
                //清除
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
                BuryingPoint.doClickOkOnConvertPermissionWithdrawnDialog()
            },
            clickBack = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
            },
            cancelable = true
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
    }

    /**
     * 转文本声明-关键词提取信息收集
     */
    private fun doShowDialogPermissionConvertSearch() {
        val title = context.resources.getString(
            R.string.privacy_policy_convert_permission_notice_title
        )
        val message = context.resources.getString(com.soundrecorder.common.R.string.extract_keywords_statement_content_v2)
        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.refuse)
        val dialog = createDialog(
            title = title,
            message = message,
            protocolTxt = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH)
                resultCallback?.onPrivacyPolicySuccess(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH)
            },
            clickCancel = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH)
            },
            clickBack = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH)
            },
            cancelable = true
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH)
    }

    /**
     * 同意以下功能对信息的处理
     */
    private fun doShowDialogFunctionInfo(fromType: Int) {
        val activity = context
        val protocolText = createSpan(
            PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION,
            com.soundrecorder.common.R.string.dialog_notice_function_title_desc,
            R.string.privacy_policy_privacy_policy
        )

        val privacyList = getPrivacyList()
        val dialog = createFunctionDialog(
            titleResId = R.string.privacy_policy_convert_permission_notice_title,
            protocolText = protocolText,
            privacyList = privacyList,
            clickOk = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION, remove = true)
                var hasConvertPerm = false
                var hasCloudPerm = false
                var hasFeedback = false
                if (it.isNotEmpty()) {
                    for (privacyItem in it) {
                        DebugUtil.d(TAG, "createFunctionDialog, privacyItem: ${privacyItem.titleText}")
                        when (privacyItem.titleText) {
                            activity.getString(com.soundrecorder.common.R.string.transfer_dialog_statement_title_new) -> hasConvertPerm = true
                            activity.getString(com.soundrecorder.common.R.string.sync_record) -> hasCloudPerm = true
                            activity.getString(com.soundrecorder.common.R.string.feed_back_title) -> {
                                hasFeedback = true
                                PrefUtil.putBoolean(activity, KEY_RECORD_FEEDBACK_PERMISSION, true)
                            }
                        }
                    }
                }

//                onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION, hasConvertPerm, hasCloudPerm, hasFeedback)
                resultCallback?.onPrivacyPolicySuccess(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION)
            },
            clickCancel = {
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION, remove = true)
//                onClickFromPrivacyPolicy(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION)
            },
            clickBack = {
                when (fromType) {
                    PrivacyPolicyConstant.TYPE_USER_NOTICE -> doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                    PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL -> doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL)
                    PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC -> doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
                    PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS -> doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS)
                    else -> activity.finish()
                }
                doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION)
            }
        )
        dialog.addAndShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION)
    }

    private fun getPrivacyList(): ArrayList<PrivacyItem> {
        return ArrayList<PrivacyItem>().apply {
            /*轻量OS没有转文本*/
            if (!BaseUtil.isLightOS()) {
                add(
                    PrivacyItem(
                        context.getString(com.soundrecorder.common.R.string.transfer_dialog_statement_title_new),
                        context.getString(com.soundrecorder.common.R.string.oplus_new_secret_5)
                    )
                )
            }
            val appName = context.getString(com.soundrecorder.common.R.string.app_name_main)
            val cloudDesc = String.format(
                context.getString(com.soundrecorder.common.R.string.dialog_notice_cloud_switch_desc),
                appName
            )
            add(
                PrivacyItem(
                    context.getString(com.soundrecorder.common.R.string.sync_record),
                    cloudDesc
                )
            )
            val feedbackDesc = String.format(
                context.getString(com.soundrecorder.common.R.string.dialog_notice_feedback_desc),
                appName
            )
            add(
                PrivacyItem(
                    context.getString(com.soundrecorder.common.R.string.feed_back_title),
                    feedbackDesc
                )
            )
        }
    }

    private fun createFunctionDialog(
        titleResId: Int,
        protocolText: CharSequence,
        privacyList: ArrayList<PrivacyItem>,
        clickOk: (ArrayList<PrivacyItem>) -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit,
    ): COUIIndividualStatementDialog {
        return COUIIndividualStatementDialog(context).apply {
            setTitleText(titleResId)
            setBottomButtonText(com.soundrecorder.common.R.string.full_page_statement_button_text)
            setExitButtonText(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
            setCanceledOnTouchOutside(false)
            statement = protocolText
            addPrivacyList(privacyList)

            onButtonClickListener = object : COUIIndividualStatementDialog.OnButtonClickListener {
                override fun onBottomButtonClick(checkedFunctionList: ArrayList<PrivacyItem>) {
                    clickOk.invoke(checkedFunctionList)
                }

                override fun onExitButtonClick() {
                    clickCancel.invoke()
                }
            }
            setOnCancelListener {
                clickBack.invoke()
            }
        }
    }

    private fun doShowDialogAIEngine(fromType: Int, pageFrom: Int? = null) {
        val unifiedSummaryManager = smartNameAction?.newUnifiedSummaryManager()
        unifiedSummaryManager?.showAiUnitPluginsDialog(context, PrivacyPluginDownloadCallback(this, fromType), isOpenSwitch = true)
    }

    /**
     * onAgreeClick
     */
    private fun onClickFromPrivacyPolicy(type: Int, grantedTypeList: List<Int>) {
        DebugUtil.i(TAG, "onClickFromPrivacyPolicy type: $type, grantedTypeList:${grantedTypeList.toList()}")
        hasConvertPermission = FUNC_TYPE_SMART_GENERATION in grantedTypeList || (type == PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
        if (hasConvertPermission) {
            PermissionUtils.setConvertGrantedStatus(context)
            BuryingPoint.doClickOkOnConvertPermissionDialog()
        }
        //勾选了云同步/转文本/帮助与反馈其中一个，则授权联网权限
        if (PermissionUtils.shouldNetworkGrantedByFunc(grantedTypeList)) {
            PermissionUtils.setNetWorkGrantedStatus(context, true)
        }

        PermissionUtils.setStatementUpdateStatus(context)
        removeStatementView()

        // 更新用户须知SP值
        val updateSpRequestPermission = if (BaseUtil.isAndroidROrLater) {
            PermissionUtils.hasAllFilePermission()
        } else {
            true
        }
        DebugUtil.d(TAG, "onClickFromPrivacyPolicy, updateSpRequestPermission:$updateSpRequestPermission")
        if (updateSpRequestPermission) {
            PermissionUtils.setNextActionForRequestPermission(context)
        } else {
            PermissionUtils.setNextActionForShowAllFileDialog(context)
        }

        if (FUNC_TYPE_CLOUD_SYNC !in grantedTypeList) {
            doCloseCloudSwitchByDisagree()
        } else {
            cloudKitApi?.setCloudGrantedStatus(context)
            switchCloudOn()
        }

        resultCallback?.onPrivacyPolicySuccess(type)
    }

    /**
     * 默认打开云同步
     */
    private fun switchCloudOn() {
        if (!NetworkUtils.isNetworkInvalid(context) && isRegionAndSupportCloud() && OS12FeatureUtil.isColorOS14OrLater()) {
            context.lifecycleScope.launch(Dispatchers.IO) {
                if (cloudKitApi?.checkAccountIsVerified() == true) {
                    val callback = object : VerifyCallBack {
                        override fun onSuccess() {
                            //校验成功
                            switchCloudOnAfterVerify()
                        }

                        override fun onFail() {
                            DebugUtil.v(TAG, "switchCloudOn openSync onFail")
                        }
                    }
                    context.let { cloudKitApi?.checkLoginAndVerify(it, callback) }
                } else {
                    switchCloudOnAfterVerify()
                }
            }
        }
    }

    private fun switchCloudOnAfterVerify() {
        context.lifecycleScope.launch(Dispatchers.IO) {
            if (!isRegionAndSupportCloud()) {
                DebugUtil.d(TAG, "switchCloudOnAfterVerify return by not support")
                return@launch
            }
            val isSuccess = cloudKitApi?.setSyncSwitch(CloudSwitchState.OPEN_ONLY_WIFI, false) ?: false
            DebugUtil.d(TAG, "switchCloudOn, isSuccess:$isSuccess")
            if (isSuccess) {
                cloudKitApi?.clearLastUserData(context)
            }
        }
    }

    /**
     * 弹窗点击不同意
     */
    private fun onClickCancel(type: Int) {
        when (type) {
            // 类型：版本更新弹窗
            PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE -> {
                //设置update
                PermissionUtils.setStatementUpdateStatus(context)
                //清除转文本
                PermissionUtils.clearConvertGrantedStatus()
                doCloseCloudSwitchByDisagree()
                removeStatementView()
            }
        }

        resultCallback?.onPrivacyPolicyFail(type)
    }

    private fun doCloseCloudSwitchByDisagree() {
        if (isRegionAndSupportCloud()) {
            context.lifecycleScope.launch(Dispatchers.IO) {
                if ((cloudKitApi?.queryCloudSwitchState(false) ?: -1) > CloudSwitchState.CLOSE) {
                    //开关关闭
                    val isSuccess = cloudKitApi?.setSyncSwitch(CloudSwitchState.CLOSE, false) ?: false
                    if (isSuccess) {
                        //清除云同步权限已授予状态
                        cloudKitApi?.clearCloudGrantedStatus()
                        //清除云同步DB数据
                        CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(BaseApplication.getAppContext())
                    }
                    withContext(Dispatchers.Main) {
                        if (isSuccess) {
                            resultCallback?.onPrivacyPolicySuccess(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
                        }
                    }
                }
            }
        }
    }

    private fun doShowDialog(type: Int, fromType: Int = PrivacyPolicyConstant.TYPE_USER_NOTICE_DEFAULT, pageFrom: Int? = null) {
        currentShowingDialogType = type
        DebugUtil.d(TAG, "doShowDialog $type: $fromType")
        when (type) {
            PrivacyPolicyConstant.TYPE_USER_NOTICE -> doShowDialogUserNotice()
            PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC -> doShowDialogUserNoticeBasic()
            PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL -> doShowDialogUserNoticeStill()
            PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS -> doShowDialogUserNoticeLight()
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT -> doShowDialogPermissionConvert(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT, pageFrom)
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD -> {
                // 通过摘要卡点击显示的对话框无法点击外部取消
                doShowDialogPermissionConvert(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD, pageFrom)
            }

            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN -> {
                doShowDialogPermissionConvertWithdrawn(cloudKitApi?.isSupportCloudArea() == true)
            }
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH -> doShowDialogPermissionConvertSearch()
            // 版本升级弹窗
            PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE -> doShowDialogUserNoticeUpdate()
            //云同步
            PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD -> doShowDialogPermissionConvert(PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD, pageFrom)
            // AI服务引擎
            PrivacyPolicyConstant.TYPE_PERMISSION_AI_ENGINE -> doShowDialogAIEngine(fromType, pageFrom)
        }
    }

    @Suppress("SpreadOperator")
    private fun createSpan(
        type: Int,
        message: Int,
        vararg links: Int
    ): SpannableStringBuilder {
        val args = links.map { context.resources.getString(it) }
        val value = context.resources.getString(message, *args.toTypedArray())
        val builder = SpannableStringBuilder(value)
        args.forEach { link ->
            val start = value.indexOf(link)
            val end = start + link.length
            if (start >= 0) {
                builder.setSpan(object : COUIStatementClickableSpan(context) {
                    override fun onClick(widget: View) {
                        onClickSpan(type, link)
                    }
                }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
        return builder
    }

    protected open fun onClickSpan(type: Int, link: String) {
        DebugUtil.d(TAG, "onClickSpan, type:$type, link:$link")
        when (link) {
            context.resources.getString(R.string.privacy_policy_basic) -> {
                doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC)
                doDismiss(type)
            }

            context.resources.getString(R.string.privacy_policy_privacy_policy) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }

                settingApi?.launchRecordPrivacy(
                    context,
                    com.soundrecorder.common.R.string.privacy_policy_settings_policy_key
                )
            }

            context.resources.getString(com.soundrecorder.common.R.string.setting_user_info_proctol_privacy) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                settingApi?.launchBootRegPrivacy(context) {
                    disableDialog = it
                }
            }
        }
    }

    fun isShowing(type: Int): Boolean = dialogMap[type]?.isShowing == true

    fun getShowingPolicyDialogType(): Int? = if (dialogMap[currentShowingDialogType]?.isShowing == true) currentShowingDialogType else null

    /**
     * onSaveInstance时获取showing的弹窗key
     */
    fun onSaveShowingDialog(): ArrayList<Int> {
        val keys = arrayListOf<Int>()
        dialogMap.map {
            if (isShowing(it.key)) {
                keys.add(it.key)
            }
        }
        showDialogKeys.clear()

        return keys
    }

    /**
     * onRestoreInstance时还原showing的key
     */
    fun onRestoreShowingDialog(dialogKeys: ArrayList<Int>?) {
        isReCreate = true
        if (dialogKeys != null) {
            showDialogKeys.addAll(dialogKeys)
        }
    }

    /**
     * 用户须知等弹窗activity走configurationChange时重建逻辑；
     * 单独抽取方法，若有页面需要在用户须知弹窗重建前执行，可在该方法super之前调用；
     * 目前这里主要针对RecorderActivity onConfigurationChanged 需要在setContentView后执行弹窗重建
     */
    fun reCreateStateDialog() {
        dialogMap.forEach {
            if (isShowing(it.key)) {
                doDismiss(it.key, false)
                doShowDialog(it.key)
            }
        }
    }

    fun clearAll() {
        dialogMap.forEach {
            if (isShowing(it.key)) {
                doDismiss(it.key, false)
            }
        }
        dialogMap.clear()
        showDialogKeys.clear()
        disableDialog.dismissWhenShowing()
        disableDialog = null
    }

    /**
     * 取消所有用户须知弹窗
     */
    fun checkAndDismissDialog() {
        if (PermissionUtils.getNextAction() != PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE, false)
            doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC, false)
            doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL, false)
            doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION, false)
            doDismiss(PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS, false)
            doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION, false)
            //已经授予过更新弹窗权限
            if (PermissionUtils.checkPermissionUpdateAlreadyApply(context)) {
                removeStatementView()
            }
        }

        if (cloudKitApi?.isStatementCloudGranted(context) == true) {
            doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD, false)
        }

        if (PermissionUtils.isStatementConvertGranted(context)) {
            doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT, false)
            doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH, false)
        }

        if (!canShowWithdrawnPermissionConvert(context)) {
            doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN, false)
        }
    }

    fun resumeShowDialog(checkType: Int, forceShow: Boolean, pageFrom: Int?) {
        DebugUtil.d(TAG, "resumeShowDialog, checkType:$checkType, forceShow:$forceShow")
        when (checkType) {
            PrivacyPolicyConstant.TYPE_USER_NOTICE_DEFAULT,
            PrivacyPolicyConstant.TYPE_USER_NOTICE,
            PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC,
            PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL,
            PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION,
            PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS -> resumeShowUserNotice(checkType)

            PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE -> resumeShowUserNoticeUpdate()
            PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD,
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT,
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD -> resumeShowPermissionConvert(checkType, forceShow, pageFrom)

            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH -> resumeShowPermissionConvertSearch(forceShow)
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN -> resumeShowWithdrawnPermissionConvert(forceShow)
        }
    }

    /**
     * show用户须知弹窗
     */
    private fun resumeShowUserNotice(checkType: Int) {
        addStatementView()
        if (isReCreate) {
            DebugUtil.d(TAG, "resumeShowUserNotice:$checkType, isReCreate,showDialogKeys=$showDialogKeys")
            if (showDialogKeys.contains(checkType)) {
                showDialogKeys.forEach {
                    doShowDialog(it)
                }
            } else {
                doShowDialog(checkType)
            }
            isReCreate = false
        } else {
            DebugUtil.d(TAG, "resumeShowUserNotice:$checkType, dialogMap=${dialogMap.keys}")
            if (checkType == PrivacyPolicyConstant.TYPE_USER_NOTICE_DEFAULT) {
                // 默认情况，根据内销、轻量显示对应第一个基本弹窗
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    if (BaseUtil.isLightOS()) {
                        doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS)
                    } else {
                        doShowDialog(PrivacyPolicyConstant.TYPE_USER_NOTICE)
                    }
                }
            } else if (dialogMap.containsKey(checkType) && isShowing(checkType)) {
                // 当前显示的dialog正在显示
                DebugUtil.d(TAG, "target dialog is showing")
            } else {
                // 正在显示的dialog同目标dialog不匹配
                dialogMap.forEach {
                    doDismiss(it.key, false)
                }
                doShowDialog(checkType)
            }
        }
    }

    /**
     * 显示个保法升级声明
     */
    private fun resumeShowUserNoticeUpdate() {
        addStatementView()
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it)
            }
            isReCreate = false
        } else {
            val none = dialogMap.none { isShowing(it.key) }
            if (none) {
                doShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)
            }
        }
    }

    /**
     * 恢复展示转文本权限弹窗
     */
    private fun resumeShowPermissionConvert(checkType: Int, forceShow: Boolean = false, pageFrom: Int?) {
        DebugUtil.i(TAG, "isReCreate = $isReCreate , forceShow = $forceShow")
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it, pageFrom = pageFrom)
            }
            isReCreate = false
        } else {
            if (forceShow) {
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    //doShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT)
                    doShowDialog(checkType, pageFrom = pageFrom)
                }
            }
        }
    }

    /**
     * 恢复展示转文本关键词提取信息弹窗
     */
    private fun resumeShowPermissionConvertSearch(forceShow: Boolean = false) {
        DebugUtil.i(TAG, "isReCreate = $isReCreate , forceShow = $forceShow")
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it)
            }
            isReCreate = false
        } else {
            if (forceShow) {
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    doShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH)
                }
            }
        }
    }

    private fun resumeShowWithdrawnPermissionConvert(forceShow: Boolean) {
        DebugUtil.i(TAG, "isReCreate = $isReCreate , forceShow = $forceShow")
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it)
            }
            isReCreate = false
        } else {
            if (forceShow) {
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    doShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN)
                }
            }
        }
    }

    private class PrivacyPluginDownloadCallback(privacyPolicyManager: PrivacyPolicyDialogManager, val fromType: Int) : IPluginDownloadCallback {
        private val weakPolicyManager = WeakReference(privacyPolicyManager)

        override fun onDownLoadResult(result: Boolean) {
            DebugUtil.i(TAG, "onDownLoadResult result= $result")
            val privacyPolicyManager = weakPolicyManager.get() ?: return
            if (privacyPolicyManager.context.isDestroyed) return
            DebugUtil.i(TAG, "onDownLoadResult in")
            privacyPolicyManager.context.runOnUiThread {
                privacyPolicyManager.doDismiss(PrivacyPolicyConstant.TYPE_PERMISSION_AI_ENGINE, false, true)
                privacyPolicyManager.removeStatementView()
                privacyPolicyManager.onClickFromPrivacyPolicy(
                    fromType,
                    arrayListOf(FUNC_TYPE_SMART_GENERATION, FUNC_TYPE_SUMMARY_MIND, FUNC_TYPE_CLOUD_SYNC, FUNC_TYPE_FEEDBACK)
                )
            }
        }
    }
}