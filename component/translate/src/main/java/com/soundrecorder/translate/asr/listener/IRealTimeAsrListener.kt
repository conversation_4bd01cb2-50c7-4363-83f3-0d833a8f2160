/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IFileAsrListener
 * Description:
 * Version: 1.0
 * Date: 2025/3/24
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/24 1.0 create
 */
package com.soundrecorder.translate.asr.listener

import com.soundrecorder.translate.asr.bean.AsrResult

interface IRealTimeAsrListener {
    /**
     * 实时ASR状态回调
     */
    fun onStatus(channelId: String?, bizType: String?, code: Int?, msg: String?)

    /**
     * 实时ASR识别结果回调
     */
    fun onAsrResult(channelId: String?, asrResult: AsrResult)

    /**
     * 实时ASR所支持的语种，错误回调
     */
    fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?, isInnerInInvoke: Boolean)

    /**
     * 实时ASR所支持的语种，成功回调
     */
    fun onTranslationCfgSuccess(channelId: String?, parseData: Map<String, String>, isInnerInInvoke: Boolean)
}