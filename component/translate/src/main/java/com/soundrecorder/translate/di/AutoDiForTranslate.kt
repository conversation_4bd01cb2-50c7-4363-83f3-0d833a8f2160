/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDiForTranslate
 ** Description : AutoDiForTranslate
 ** Version     : 1.0
 ** Date        : 2025/06/06
 ** Author      : renjiahao
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  renjiahao       2025/06/06       1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.di

import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.translate.api.AIAsrManagerApi
import org.koin.dsl.module

object AutoDiForTranslate {
    val translateModule = module {
        single<AIAsrManagerAction>(createdAtStart = true) {
            AIAsrManagerApi
        }
    }
}