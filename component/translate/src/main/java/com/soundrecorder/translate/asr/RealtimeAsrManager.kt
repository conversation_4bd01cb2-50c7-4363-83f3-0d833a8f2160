/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealtimeConvertManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/6 1.0 create
 */

package com.soundrecorder.translate.asr

import android.os.Bundle
import android.os.SystemClock
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.translate.asr.bean.AsrResult
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.STATUS_NET_CONNECTED
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap

class RealtimeAsrManager {
    private val logTag = "RealtimeAsrManager"
    private var asrClient: AIRealTimeAsrClient? = null
    private var listener: IRealTimeAsrListener? = null
    private val asrResultMap = LinkedHashMap<String, ConvertContentItem>()

    var asrCallback: RealtimeAsrCallback? = null
    var offsetManager: RealtimeOffsetManager? = null
    /* 用于切换语言时stopAsr的结果通知 */
    var asrStopCallback: ((Boolean) -> Unit)? = null
    /*监听ASR结果*/
    private var rtAsrListener: OnRealtimeListener? = null
    /*ASR转写缓存*/
    private var rtAsrCache: RealtimeSubtitleCacheImpl? = null

    /*记录startAsr的成功结果、调用stop后，该值修改为false*/
    private val startAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()
    /*记录initAsr结果，true：成功 false：失败*/
    private val initAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()
    /*记录调用stopAsr，true：已经调用过了，false：还未调用*/
    private val stopAsrFlagMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    private var isDestroy = false

    @Volatile
    var isError = false

    /*ASR的当前状态*/
    private var asrStatus: RealTimeAsrStatus = RealTimeAsrStatus.ASR_DEFAULT

    /*讲话人ID到名字的映射表*/
    private val speakerIdToNameMap = ConcurrentHashMap<Int, String>()

    init {
        runCatching {
            val startTime = SystemClock.elapsedRealtime()
            asrClient = AIRealTimeAsrClient(BaseApplication.getAppContext())
            DebugUtil.d(logTag, "new client spend ${SystemClock.elapsedRealtime() - startTime}")
        }.onFailure {
            DebugUtil.e(logTag, "init error $it")
        }
        listener = object : IRealTimeAsrListener {
            override fun onStatus(channelId: String?, bizType: String?, code: Int?, msg: String?) {
                /**流程结束，忽略状态回调*/
                if (isDestroy) {
                    DebugUtil.i(logTag, "on status return destory")
                    return
                }

                when (bizType) {
                    RealTimeAsrParser.BIZ_TYPE_START_ASK -> {
                        if (code == 0) { // startAsr 成功回调
                            channelId?.let {
                                startAsrResultMap[it] = true
                                asrStatus = RealTimeAsrStatus.ASR_RUNNING
                            }
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_END_ASK -> {
                        asrCallback?.onStopAsrResult(code == 0, channelId)
                        asrStopCallback?.invoke(code == 0)
                        if (code == 0) { // stopAsr 成功回调
                            return
                        }
                    }
                    RealTimeAsrParser.BIZ_TYPE_AUDIO -> { // 发送音频数据错误
                        if (code == RealTimeAsrParser.ERROR_NOT_FOUND_CHANNEL_ID && stopAsrFlagMap[channelId] == true) {
                            /*忽略发送数据，通道被关闭的场景*/
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_SERVER_END_ASK -> { // 服务端主动断开链路标志
                        DebugUtil.w(logTag, "on status server end")
                        channelId?.let {
                            if (startAsrResultMap[it] == true) {
                                startAsrResultMap[it] = false
                            }
                            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        }
                    }
                }

                when (code) {
                    RealTimeAsrParser.STATUS_INIT_SUCCESS -> {
                        initAsrResultMap[channelId] = true
                        asrStatus = RealTimeAsrStatus.ASR_INITIALIZING
                        asrCallback?.onInitResult(channelId, true)
                        return
                    }

                    RealTimeAsrParser.STATUS_INIT_ERROR -> {
                        initAsrResultMap[channelId] = false
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        asrCallback?.onInitResult(channelId, false)
                        return
                    }
                    /**网络已连接*/
                    STATUS_NET_CONNECTED -> return
                    else -> {
                        if (channelId?.isNotBlank() == true && stopAsrFlagMap[channelId] == true) {
                            DebugUtil.d(logTag, "ignore error by stop")
                            return
                        }
                        isError = true
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        asrCallback?.onError(channelId, code, msg)
                        rtAsrListener?.onAsrError(code ?: -1)
                    }
                }
            }

            override fun onAsrResult(channelId: String?, asrResult: AsrResult) {
                asrResult.msgId?.let { msgID ->
                    val emptyOffset = offsetManager?.emptyOffsetTime ?: 0
                    val mainRecordingTime = offsetManager?.mainRecordingTime ?: 0
                    val realOffset = mainRecordingTime - emptyOffset
                    if (asrResultMap[msgID] == null) {
                        asrResultMap[msgID] = ConvertContentItem().apply {
                            startTime = asrResult.startOffset.toLong() + realOffset
                            endTime = asrResult.endOffset.toLong() + realOffset
                            textContent = asrResult.text
                            textType = asrResult.type
                            roleId = asrResult.speakId ?: -1
                            roleName = getRoleNameByRoleId(roleId)
                        }
                    } else {
                        asrResultMap[msgID]?.apply {
                            startTime = asrResult.startOffset.toLong() + realOffset
                            endTime = asrResult.endOffset.toLong() + realOffset
                            textContent = asrResult.text
                            textType = asrResult.type
                            roleId = asrResult.speakId ?: -1
                            roleName = getRoleNameByRoleId(roleId)
                        }
                    }
                    rtAsrCache?.let { rtAsrListener?.onSubtitleUpdated(it) }
                }
            }

            override fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?, isInnerInInvoke: Boolean) {
                rtAsrListener?.onTranslationCfgError(errorCode, errorMsg)
                DebugUtil.d(logTag, "onTranslationCfgError rtAsrListener=$rtAsrListener")
                if (isInnerInInvoke) {
                    asrCallback?.onTranslationCfgError(channelId, errorCode, errorMsg)
                }
            }

            override fun onTranslationCfgSuccess(channelId: String?, parseData: Map<String, String>, isInnerInInvoke: Boolean) {
                rtAsrListener?.onTranslationCfgSuccess(parseData)
                DebugUtil.d(logTag, "onTranslationCfgSuccess rtAsrListener=$rtAsrListener")
                if (isInnerInInvoke) {
                    asrCallback?.onTranslationCfgSuccess(channelId, parseData)
                }
            }
        }
        listener?.let {
            asrClient?.registerAsrListener(it)
        }
        rtAsrCache = RealtimeSubtitleCacheImpl()
    }

    fun initAsr(param: Bundle?) {
        DebugUtil.i(logTag, "init asr")
        runCatching {
            asrClient?.initAsr(param)
            asrStatus = RealTimeAsrStatus.ASR_INITIALIZING
        }.onFailure {
            DebugUtil.e(logTag, "initAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun startAsr(channelId: String, param: Bundle?) {
        /*已经调用过stopAsr了,避免init回来之前用户点击了暂停*/
        if (stopAsrFlagMap[channelId] == true) {
            DebugUtil.w(logTag, "startAsr return by stop")
            return
        }
        runCatching {
            asrClient?.startAsr(channelId, param)
            asrStatus = RealTimeAsrStatus.ASR_STARTING
        }.onFailure {
            DebugUtil.e(logTag, "startAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun getStartAsrResult(channelId: String): Boolean {
        return startAsrResultMap[channelId] ?: false
    }

    fun stopAsr(channelId: String) {
        /*已经调用过stopAsr了*/
        if (stopAsrFlagMap[channelId] == true) {
            return
        }
        runCatching {
            asrClient?.stopAsr(channelId)
            stopAsrFlagMap[channelId] = true
            startAsrResultMap[channelId] = false
            asrStatus = RealTimeAsrStatus.ASR_STOPPING
        }.onFailure {
            DebugUtil.e(logTag, "stopAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun processAudioData(channelId: String, data: ByteArray?) {
        runCatching {
            asrClient?.processRealTimeData(channelId, data)
        }.onFailure {
            DebugUtil.e(logTag, "processAudioData error $it")
        }
    }

    fun releaseChannel(channelId: String) {
        runCatching {
            asrClient?.releaseChannel(channelId)
            asrStatus = RealTimeAsrStatus.ASR_RELEASING
        }.onFailure {
            DebugUtil.e(logTag, "release error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun release() {
        isDestroy = true
        runCatching {
            asrClient?.release()
            asrStatus = RealTimeAsrStatus.ASR_RELEASING
        }.onFailure {
            DebugUtil.e(logTag, "release error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
        listener = null
        asrClient = null
        asrStopCallback = null
        rtAsrListener = null
        rtAsrCache = null
        offsetManager = null
        speakerIdToNameMap.clear()
    }

    fun isStopAsr(channelId: String): Boolean {
        return stopAsrFlagMap[channelId] ?: false
    }

    fun getAsrContent(): List<ConvertContentItem> {
        return asrResultMap.values.toList()
    }

    fun registerRtAsrListener(listener: OnRealtimeListener) {
        rtAsrListener = listener
        DebugUtil.d(logTag, "registerRtAsrListener rtAsrListener=$rtAsrListener")
    }

    fun unregisterRtAsrListener(listener: OnRealtimeListener) {
        if (rtAsrListener == listener) {
            rtAsrListener = null
            DebugUtil.d(logTag, "unregisterRtAsrListener rtAsrListener cleared")
        }
    }

    /**
     * 获取ASR支持的语种
     */
    fun getTranslationConfig(channelId: String, isInnerInInvoke: Boolean = false) {
        asrClient?.getTranslationConfig(channelId, isInnerInInvoke)
    }

    /**
     * 获取ASR当前的状态
     */
    fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        return asrStatus
    }

    /**
     * 修改讲话人名字
     * @param roleId 讲话人ID
     * @param newName 新的讲话人名字
     * @return 是否修改成功
     */
    fun updateSpeakerName(roleId: Int, newName: String): Boolean {
        if (newName.isBlank()) return false

        speakerIdToNameMap[roleId] = newName

        // 更新已有结果中的讲话人名字
        asrResultMap.values.forEach { item ->
            if (item.roleId == roleId) {
                item.roleName = newName
                item.changeRoleNameForTimeDivider(newName)
            }
        }

        DebugUtil.i(logTag, "Updated speaker name: roleId=$roleId, newName=$newName")
        return true
    }

    /**
     * 获取ASR缓存对象
     */
    fun getRealtimeSubtitleCache(): IRealtimeSubtitleCache? {
        return rtAsrCache
    }

    /**
     * 根据讲话人ID获取讲话人名字
     *
     * 1. 如果id为-1，则表示不支持讲话人功能，则返回Null
     * 2. 如果id为0，则表示正在处理，则返回空字符串
     * 3. 根据id查询是否存在讲话人名字，如果有则返回，如果没有则使用讲话人 %d,的形式去默认命名
     */
    private fun getRoleNameByRoleId(roleId: Int): String? {
        return if (roleId == -1) {
            null
        } else if (roleId == 0) {
            ""
        } else {
            speakerIdToNameMap[roleId] ?: genRoleNameByRoleId(roleId)
        }
    }

    /**
     * 根据id生成默认的名字，格式如 讲话人 %d
     */
    private fun genRoleNameByRoleId(roleId: Int): String {
        val text = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.convert_speaker, roleId)
        return text
    }

    inner class RealtimeSubtitleCacheImpl : IRealtimeSubtitleCache {
        override fun getGeneratedSubtitles(): List<ConvertContentItem> {
            return getAsrContent().filter { it.textType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL }
        }

        override fun getTemporySubtitles(): List<ConvertContentItem> {
            return getAsrContent().filter { it.textType == RealTimeAsrParser.ASR_RESULT_TYPE_INTERMEDIATE }
        }
    }
}

interface RealtimeAsrCallback {

    fun onInitResult(channelId: String?, success: Boolean)
    fun onError(channelId: String?, code: Int?, msg: String?)

    fun onStopAsrResult(result: Boolean, channelId: String?)

    fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?)

    fun onTranslationCfgSuccess(channelId: String?, data: Map<String, String>)
}

