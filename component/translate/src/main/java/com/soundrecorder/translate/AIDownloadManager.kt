/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AIDownloadManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/24
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/24 1.0 create
 */

package com.soundrecorder.translate

import android.content.Context
import com.oplus.aiunit.core.data.DownloadType
import com.oplus.aiunit.core.data.SceneData
import com.oplus.aiunit.core.data.UnitState
import com.oplus.aiunit.download.api.AIDownload
import com.oplus.aiunit.download.api.CustomTerms
import com.oplus.aiunit.download.api.DownloadRequest
import com.oplus.aiunit.download.core.DownloadInfo
import com.oplus.aiunit.download.core.DownloadListener
import com.oplus.aiunit.download.core.DownloadState
import com.oplus.aiunit.toolkits.AISettings
import com.oplus.aiunit.toolkits.callback.SettingsCallback
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.modulerouter.translate.IAsrDownloadCallback
import com.soundrecorder.modulerouter.translate.IAsrPluginDownloadDelegate

class AIDownloadManager : IAsrPluginDownloadDelegate {
    companion object {
        const val TAG = "AIDownloadManager"
        const val AIUNIT_STATE_GUIDE_CODE = 906
        private const val DOWNLOAD_MODEL_ERROR = "privacy_aiunit_guide_error"
        private const val DOWNLOAD_MODEL_SWITCH_CLOSE = "privacy_aiunit_guide_switch_close"
        private const val DOWNLOAD_MODEL_NOT_SUPPORT = "download_aiunit_not_support"
        private const val DOWNLOAD_MODEL_SCENE_NONE = "download_aiunit_scene_data_is_null"
        const val DOWNLOAD_MODEL_CANCEL = "download_aiunit_cancel"
        private const val DOWNLOAD_MODEL_FAIL = "download_aiunit_fail"

        /**
         * 插件是否下载成功
         */
        @JvmStatic
        fun isAiModeDownloadSuccess(detectName: String, sceneName: String): Boolean {
            var downloadSuccess = false
            kotlin.runCatching {
                val state = AISettings.getDetectData(
                    BaseApplication.getAppContext(), detectName
                ).state
                DebugUtil.d(TAG, "isAiModeDownloadSuccess(),detectName=$detectName state = $state")
                if (state == AIUNIT_STATE_GUIDE_CODE) {
                    return false
                }
                val sceneData = AISettings.getSceneData(
                    BaseApplication.getAppContext(), sceneName
                )
                downloadSuccess = sceneData?.support == 1 && sceneData.downloadType == DownloadType.DOWNLOAD_NONE
                DebugUtil.d(
                    TAG, "isAiModeDownloadSuccess(),sceneName=$sceneName, ${sceneData?.support}, ${sceneData?.downloadType}"
                )
            }.onFailure {
                DebugUtil.e(TAG, "isAiModeDownloadSuccess(), $detectName, error ${it.message}")
            }
            DebugUtil.d(TAG, "downloadSuccess-$detectName:= $downloadSuccess")
            return downloadSuccess
        }
    }

    override fun isAsrPluginDownload(): Boolean {
        return isAiModeDownloadSuccess(AIAsrManager.DETECT_NAME_ASR, AIAsrManager.SCENE_NAME_ASR)
    }

    override fun checkAndDownloadAsrPlugin(context: Context, isSummaryTab: Boolean, callback: IAsrDownloadCallback?) {
        checkAiUnitPrivacyGuide(context, isSummaryTab, AIAsrManager.DETECT_NAME_ASR, AIAsrManager.SCENE_NAME_ASR, callback)
    }

    override fun isSmartNamePluginDownload(): Boolean {
        return isAiModeDownloadSuccess(AIAsrManager.SCENENAME_UNIFIED_ASR_SUMMARY, AIAsrManager.SCENENAME_UNIFIED_ASR_SUMMARY)
    }

    override fun checkAndDownloadSmartNamePlugin(
        context: Context,
        isSummaryTab: Boolean,
        callback: IAsrDownloadCallback?
    ) {
        checkAiUnitPrivacyGuide(context, isSummaryTab, AIAsrManager.DETECT_UNIFIED_SUMMARY, AIAsrManager.SCENE_UNIFIED_SUMMARY, callback)
    }

    override fun checkAndDownloadAsrUnifiedPlugins(context: Context, isSummaryTab: Boolean, callback: IAsrDownloadCallback?) {
        checkAiUnitPrivacyGuide(
            context,
            isSummaryTab,
            AIAsrManager.SCENENAME_UNIFIED_ASR_SUMMARY,
            AIAsrManager.SCENENAME_UNIFIED_ASR_SUMMARY,
            callback
        )
    }

    private fun checkAiUnitPrivacyGuide(
        context: Context,
        isSummaryTab: Boolean,
        detectName: String,
        sceneName: String,
        callback: IAsrDownloadCallback?
    ) {
        val applicationContext = context.applicationContext
        runCatching {
            val state = AISettings.getDetectData(applicationContext, detectName).state
            DebugUtil.i(TAG, "checkAiUnitPrivacyGuide: state:= $state")
            if (state == UnitState.STATE_UNAVAILABLE_PRIVACY_REJECT
                || state == UnitState.STATE_UNAVAILABLE_NEED_DOWNLOAD
                || state == UnitState.STATE_AVAILABLE_AND_NEW_DOWNLOAD
            ) {
                AISettings.startPrivacyGuide(applicationContext, object : SettingsCallback {
                    override fun onError(code: Int) {
                        DebugUtil.d(TAG, "downloadAiData: detectName:$detectName, onError $code")
                        callback?.downloadFail(DOWNLOAD_MODEL_ERROR)
                    }

                    override fun onSwitch(status: Int) {
                        DebugUtil.d(TAG, "downloadAiData: onSwitch detectName:$detectName, status:= $status")
                        if (status == 1) {
                            download(applicationContext, isSummaryTab, sceneName, callback)
                        } else {
                            callback?.downloadFail(DOWNLOAD_MODEL_SWITCH_CLOSE)
                        }
                    }

                    override fun onUI(status: Int) {
                        DebugUtil.d(TAG, "downloadAiData: onUI")
                    }
                }, true)
            } else {
                download(applicationContext, isSummaryTab, sceneName, callback)
            }
        }.onFailure {
            DebugUtil.e(TAG, "checkAiUnitPrivacyGuide error $it")
            callback?.downloadFail(DOWNLOAD_MODEL_ERROR)
        }
    }

    private fun download(context: Context, isSummaryTab: Boolean, sceneName: String, callback: IAsrDownloadCallback?) {
        AISettings.getSceneData(context, sceneName)?.let {
            DebugUtil.d(TAG, "download: ${it.support}, ${it.downloadType}")
            if (it.support == 1) {
                doDownload(context, isSummaryTab, sceneName, it, callback)
            } else {
                checkUpdateConfig(context) {
                    if (!it) {
                        callback?.downloadFail(DOWNLOAD_MODEL_NOT_SUPPORT)
                    } else {
                        doDownload(context, isSummaryTab, sceneName, null, callback)
                    }
                }
            }
        }
    }

    /**
     * 下载插件
     * 1.录音APK在第一次点击命名功能时，需要下载插件，需要同时下载以上3个插件：端侧asr插件+云侧asr插件+云侧摘要插件，需要配置scenename
     * 2.高端机即部署了端侧插件的机器上，当录音应用调用asr功能时，需要优先使用端侧插件，需要强制指定插件unit id,参考接入文档
     */
    private fun doDownload(context: Context, isSummaryTab: Boolean, sceneName: String, sceneData: SceneData?, callback: IAsrDownloadCallback?) {
        (sceneData ?: AISettings.getSceneData(context, sceneName))?.let {
            DebugUtil.d(TAG, "doDownload: ${it.support}, ${it.downloadType}")
            if (it.support == 1) {
                if (it.downloadType != DownloadType.DOWNLOAD_NONE) {
                    /*下载过程中，查询下载信息*/
                    AIDownload.queryByName(context, sceneName, object : AIDownloadListener("queryByName") {
                        override fun onQuery(info: DownloadInfo?) {
                            DebugUtil.d(TAG, "onQuery(), $label, ${info?.state}")
                            if (info?.state == DownloadState.STATE_DOWNLOADING) { // 下载中
                                ToastManager.showShortToast(context, R.string.tip_plugin_is_downloading)
                            } else {
                                registerDownloadListener(context, sceneName, callback)
                                AIDownload.start(context, DownloadRequest().apply {
                                    this.sceneName = sceneName
                                    this.enableProgressUI = true
                                    this.enableProgressCallback = true
                                    terms = CustomTerms().apply {
                                        title = context.applicationContext.getString(R.string.dialog_title_download_ai_plugin)
                                        description = if (isSummaryTab) {
                                            context.applicationContext.getString(
                                                com.soundrecorder.common.R.string.summary_download_plus_message,
                                                context.applicationContext.getString(com.soundrecorder.common.R.string.summary)
                                            )
                                        } else {
                                            context.applicationContext.getString(R.string.dialog_content_download_ai_plugin)
                                        }
                                    }
                                })
                            }
                        }
                    }, 0, true)
                } else {
                    callback?.downloadSuccess(sceneName)
                }
            } else {
                callback?.downloadFail(DOWNLOAD_MODEL_NOT_SUPPORT)
            }
        } ?: kotlin.run {
            DebugUtil.e(TAG, "download: getSceneData is null")
            callback?.downloadFail(DOWNLOAD_MODEL_SCENE_NONE)
        }
    }

    private fun checkUpdateConfig(context: Context, resultFunc: (isAgree: Boolean) -> Unit) {
        DebugUtil.d(TAG, "checkUpdateConfig")
        AISettings.checkUpdate(context, true, object : SettingsCallback {
            override fun onError(code: Int) {
                DebugUtil.e(TAG, "onError: $code")
                resultFunc.invoke(false)
            }

            override fun onSwitch(status: Int) {
                DebugUtil.i(TAG, "onSwitch: $status")
                resultFunc.invoke(status == SettingsCallback.SWITCH_OPEN)
            }

            override fun onUI(status: Int) {
                DebugUtil.d(TAG, "onUI: $status")
            }
        })
    }

    private fun registerDownloadListener(context: Context, sceneName: String, callback: IAsrDownloadCallback?) {
        AIDownload.register(context, sceneName, object : AIDownloadListener("download") {
            override fun onCancel() {
                callback?.downloadFail(DOWNLOAD_MODEL_CANCEL)
                AIDownload.removeOnly(sceneName)
            }

            override fun onFail(err: Int) {
                callback?.downloadFail(DOWNLOAD_MODEL_FAIL)
                AIDownload.removeOnly(sceneName)
            }

            override fun onInstall() {
                DebugUtil.d(TAG, "onInstall")
                callback?.downloadSuccess(sceneName)
                AIDownload.removeOnly(sceneName)
            }

            override fun onSuccess(fullSize: Long, downloadSize: Long, fromBreakpoint: Boolean) {
                DebugUtil.d(TAG, "onSuccess: sceneName:$sceneName")
                callback?.downloadSuccess(sceneName)
                AIDownload.removeOnly(sceneName)
            }
        })
    }
}

open class AIDownloadListener(val label: String) : DownloadListener {
    private val logTag = "MyDownloadListener"

    override fun onCancel() {
        DebugUtil.d(logTag, "onCancel(), $label")
    }

    override fun onFail(err: Int) {
        DebugUtil.d(logTag, "onFail(), $label, $err")
    }

    override fun onInstall() {
        DebugUtil.d(logTag, "onInstall(), $label")
    }

    override fun onPrepare(fullSize: Long, offsetSize: Long) {
        DebugUtil.d(logTag, "onPrepare(), $label")
    }

    override fun onProgress(fullSize: Long, offsetSize: Long, speed: Long) {
        DebugUtil.d(logTag, "onProgress(), $label, fullSize:$fullSize, offsetSize:$offsetSize")
    }

    override fun onStart(fullSize: Long, offsetSize: Long) {
        DebugUtil.d(logTag, "onStart(), $label")
    }

    override fun onSuccess(fullSize: Long, downloadSize: Long, fromBreakpoint: Boolean) {
        DebugUtil.d(logTag, "onSuccess(), $label")
    }
}
