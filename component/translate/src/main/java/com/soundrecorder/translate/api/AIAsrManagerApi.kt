/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AIAsrManagerApi
 * Description:
 * Version: 1.0
 * Date: 2025/3/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/26 1.0 create
 */

package com.soundrecorder.translate.api

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.translate.IAsrPluginCallBack
import com.soundrecorder.modulerouter.translate.IAsrPluginDownloadDelegate
import com.soundrecorder.translate.AIAsrManager
import com.soundrecorder.translate.AIDownloadManager
import com.soundrecorder.translate.asr.AsrPluginManger

object AIAsrManagerApi : AIAsrManagerAction {

    override fun loadSupportAIAsr(context: Context, forceUpdate: Boolean): Boolean {
        return AIAsrManager.isSupportAIAsr(context, forceUpdate)
    }

    override fun newAsrPluginDownloadDelegate(): IAsrPluginDownloadDelegate {
        return AIDownloadManager()
    }
    override fun newAsrPluginManager(): IAsrPluginCallBack {
        return AsrPluginManger()
    }

    override fun isDetectSupported(context: Context, detectName: String): Boolean {
        return AIAsrManager.isDetectSupported(context, detectName)
    }

    override fun checkUpdateAIUnitConfigBackground(context: Context) {
        return AIAsrManager.checkUpdateAIUnitConfigBackground(context)
    }

    override fun getSupportLanguage(callback: (Map<String, String>?) -> Unit) {
        AIAsrManager.getSupportLanguage(BaseApplication.getAppContext(), callback)
    }
}