/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForCloudKit.kt
 * * Description : AutoDiForCloudKit
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.recorder.cloudkit.di

import androidx.annotation.Keep
import com.recorder.cloudkit.api.CloudKitApi
import com.recorder.cloudkit.api.CloudTipManagerApi
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import org.koin.dsl.module

@Keep
object AutoDiForCloudKit {
    val cloudKitModule = module {
        single<CloudKitInterface>(createdAtStart = true) {
            CloudKitApi
        }
        single<CloudTipManagerAction>(createdAtStart = true) {
            CloudTipManagerApi
        }
    }
}