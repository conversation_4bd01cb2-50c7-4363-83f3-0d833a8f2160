/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  CloudKitApi.kt
 * * Description : CloudKitApi
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.recorder.cloudkit.api

import android.app.Activity
import android.content.Context
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.recorder.cloudkit.SyncTriggerManager
import com.recorder.cloudkit.account.AccountBean
import com.recorder.cloudkit.account.AccountManager
import com.recorder.cloudkit.account.AccountPref
import com.recorder.cloudkit.account.IAccountInfoCallback
import com.recorder.cloudkit.account.VerifyConfirmHelper
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.recorder.cloudkit.operation.CloudConfigManager
import com.recorder.cloudkit.push.CloudPushAgent
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.CloudSyncAgent
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.recorder.cloudkit.tipstatus.dialog.CloudSyncStatusDialog
import com.recorder.cloudkit.tipstatus.dialog.CloudSyncStatusDialogHelper
import com.recorder.cloudkit.tipstatus.dialog.CloudUpgradeHelper
import com.recorder.cloudkit.utils.CloudPermissionUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.sync.db.RecordDataSync
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_BACKUP
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_RECOVERY_MANUAL
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_RECOVERY_START_APP
import com.soundrecorder.modulerouter.cloudkit.VerifyCallBack
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudSyncStatusDialog
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudUpgradeHelper

object CloudKitApi : CloudKitInterface {
    private const val TAG = "CloudKitApi"

    /**
     * 停止当前同步且清楚锚点
     */
    override fun doMediaCompare(trigCloudSyncRightNow: Boolean, syncType: Int) {
        DebugUtil.i(TAG, "doMediaCompare, trigCloudSyncRightNow:$trigCloudSyncRightNow,syncType $syncType ")
        RecordDataSync.getInstance().syncAllRecordDataFromMedia(BaseApplication.getAppContext(), true, trigCloudSyncRightNow, syncType)
    }

    override fun doStopMediaCompare(stop: Boolean) {
        DebugUtil.i(TAG, "doStopMediaCompare:$stop")
        RecordDataSync.getInstance().setStopDiff(stop)
    }

    /**
     * 若未获取一体化配置信息则去获取一次：
     * 初始化时机：
     * 1.应用启动
     * 2.账号登录成功
     * 3.录音后台切前台--无登录成功广播，所以通过onResume
     */
    override fun initCloudGlobalState(callback: ICloudGlobalStateCallBack?) {
        if (!CloudGlobalStateManager.isInitSuccess()) {
            getCloudGlobalState(false, callback)
        }
    }

    override fun getCloudGlobalState(showErrorTip: Boolean, callback: ICloudGlobalStateCallBack?) {
        CloudGlobalStateManager.judgeOCloudGlobalState(showErrorTip, callback)
    }

    override fun registerGlobalStateCallback(callback: ICloudGlobalStateCallBack) {
        CloudGlobalStateManager.registerStateChangeCallBack(callback)
    }

    override fun unregisterGlobalStateCallback(callback: ICloudGlobalStateCallBack) {
        CloudGlobalStateManager.unRegisterStateChangeCallBack(callback)
    }

    override fun showGlobalDisableDialog(activity: Activity?, state: String?, buttonListener: (() -> Unit)?): AlertDialog? {
        return CloudGlobalStateManager.showErrorDialog(activity, state, buttonListener)
    }

    override fun showGlobalLoadingDialog(activity: Activity?): AlertDialog? {
        return CloudGlobalStateManager.showLoadingDialog(activity)
    }

    /**
     * 设置云服务权限开启
     */
    override fun setCloudGrantedStatus(context: Context) {
        DebugUtil.i(TAG, "setCloudGrantedStatus")
        return CloudPermissionUtils.setCloudGrantedStatus(context)
    }

    /**
     * 关闭开关状态
     */
    override fun clearCloudGrantedStatus() {
        DebugUtil.i(TAG, "clearCloudGrantedStatus")
        return CloudPermissionUtils.clearCloudGrantedStatus()
    }

    /**
     * 判断云服务权限已授予
     */
    override fun isStatementCloudGranted(context: Context): Boolean {
        val isCloudGranted = CloudPermissionUtils.isStatementCloudGranted(context)
        DebugUtil.i(TAG, "isStatementCloudGranted:$isCloudGranted")
        return isCloudGranted
    }

    override fun hasCloudRequiredPermissions(): Boolean {
        return CloudPermissionUtils.hasCloudRequirePermission()
    }

    override fun isNetWorkGranted(context: Context): Boolean {
        return CloudPermissionUtils.isNetWorkNoticeGranted(context)
    }

    /**
     * 设置同步过程中是否忽略高温条件
     * true：检测到高温，也不会停止同步
     * false：检测到高温，停止同步
     */
    override fun setIgnoreHighTemperature(ignore: Boolean) {
        DebugUtil.i(TAG, "setIgnoreHighTemperature $ignore")
        CloudSyncAgent.getInstance().ignoreCheckHighTemperature = ignore
    }

    /**
     * 是否支持云同步
     */
    override fun isSupportCloudArea(): Boolean {
        return CloudSynStateHelper.isRegionCloudSupport() && CloudGlobalStateManager.canShowSyncSwitch()
    }

    override fun queryCloudSwitchState(checkLogin: Boolean): Int {
        return CloudSynStateHelper.getSwitchState(checkLogin)
    }

    /**
     * 注册push
     */
    override fun registerPushIfNeed(context: Context) {
        CloudPushAgent.checkPushRegister(context)
    }

    /**
     * 停止当前同步且清楚锚点
     */
    override fun stopSyncForClearAnchor(context: Context) {
        DebugUtil.i(TAG, "stopSyncForClearAnchor")
        SyncTriggerManager.getInstance(context).trigStopSyncForClearAnchor()
    }


    /**
     * 触发全量同步后触发云同步
     */
    override fun trigMediaDBSync(context: Context, syncType: Int) {
        DebugUtil.i(TAG, "trigMediaDBSync  $syncType, isCloudOn ${TipStatusManager.isCloudOn()}", true)
        if (queryCloudSwitchState(false) <= CloudSwitchState.CLOSE) {
            // 云同步未打开，只走全量
            SyncTriggerManager.getInstance(context).scheduleWorkerJobWithoutCheckCloud({
                doMediaCompare(false, syncType)
            }, 0)
            return
        }
        when (syncType) {
            SYNC_TYPE_RECOVERY_START_APP ->
                SyncTriggerManager.getInstance(context).trigRecoveryNow(true, SyncTriggerManager.RECOVERY_FROM_START_APP)

            SYNC_TYPE_RECOVERY_MANUAL ->
                SyncTriggerManager.getInstance(context).trigRecoveryNow(true, SyncTriggerManager.RECOVERY_FROM_MANUAL)

            SYNC_TYPE_BACKUP ->
                SyncTriggerManager.getInstance(context).trigBackupNow()

            else ->
                DebugUtil.e(TAG, "trigMediaDBSyncDelayed not support syncType $syncType, true")
        }
    }

    /**
     * 触发云同步或云备份
     * @param syncType @see SYNC_TYPE_BACKUP,SYNC_TYPE_RECOVERY_MANUAL,SYNC_TYPE_RECOVERY_START_APP
     */
    override fun trigCloudSync(context: Context, syncType: Int) {
        DebugUtil.i(TAG, "trigCloudSync, syncType = $syncType", true)
        val syncTriggerManager = SyncTriggerManager.getInstance(context)
        when (syncType) {
            SYNC_TYPE_BACKUP -> syncTriggerManager.trigBackupNow()
            SYNC_TYPE_RECOVERY_START_APP -> syncTriggerManager.trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_START_APP)
            SYNC_TYPE_RECOVERY_MANUAL -> syncTriggerManager.trigRecoveryNow(SyncTriggerManager.RECOVERY_FROM_MANUAL)
            else -> DebugUtil.e(TAG, "not support syncType = $syncType")
        }
    }


    /**
     * 触发备份
     */
    override fun trigBackupNow(context: Context) {
        DebugUtil.i(TAG, "trigBackupNow", true)
        trigCloudSync(context, SYNC_TYPE_BACKUP)
    }

    /**
     *  停止当前同步流程，且会清空同步锚点
     *  @param context
     *  @param deleteData 是否删除本地已同步数据
     *  true: 删除已同步过记录的相关音频、转文本等相关信息
     *  false： 删除已同步记录的 云端id以及同步状态信息
     */
    override fun stopSyncForLoginOut(context: Context, deleteData: Boolean) {
        SyncTriggerManager.getInstance(context).trigStopSyncForLoginOut(deleteData)
    }

    override fun release() {
        DebugUtil.i(TAG, "release", true)
        SyncTriggerManager.release()
        TipStatusManager.release()
        /*未使用录音，解注册push，不拉起*/
        CloudPushAgent.unregister()
        /*重置一体化开关状态*/
        CloudGlobalStateManager.reset()
    }

    /**
     * 获取是否支持开关版本
     */
    override fun isSupportSwitch(): Boolean {
        DebugUtil.i(TAG, "isSupportSwitch")
        return CloudSynStateHelper.isSupportSwitch()
    }

    /**
     * 设置开关状态
     */
    override fun setSyncSwitch(state: Int, report: Boolean): Boolean {
        DebugUtil.i(TAG, "setSyncSwitch")
        return CloudSynStateHelper.setSyncSwitch(state, report)?.bizErrorCode == 0
    }

    override fun scheduleSyncRunnable(context: Context, runnable: Runnable, delayTime: Long): Boolean {
        DebugUtil.i(TAG, "scheduleSyncRunnable")
        return SyncTriggerManager.getInstance(context).scheduleWorkerJob(runnable, delayTime)
    }

    override fun isCloudOn(): Boolean {
        return TipStatusManager.isCloudOn()
    }

    /**
     * 获取云服务 顶部提示配置
     */
    override fun getCloudSettings(dataStr: MutableLiveData<String>) {
        return CloudConfigManager.getOperationData(dataStr)
    }

    override fun clearLastUserData(context: Context) {
        // 当前登录账号同SP中存的非空值不相同，执行一次关闭开关操作，更新Sp中的值
        if (checkUserIdChanged(context)) {
            CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(context)
            CloudSyncManager.getInstance().clearUserDataOnLogout()
            AccountPref.setAccountUId(context, AccountManager.getInstance().getLoginIdFromCache(context))
        }
    }

    /**
     * 验证SP中的用户不为null，且跟当前登录用户不一致
     */
    private fun checkUserIdChanged(context: Context): Boolean {
        val oldId = AccountPref.getAccountUId(context)
        return (!oldId.isNullOrBlank()) && (oldId != AccountManager.getInstance().getLoginIdFromCache(context))
    }

    /**
     * 云同步SDK二次校验
     */
    override fun checkLoginAndVerify(activity: Activity, callback: VerifyCallBack) {
        VerifyConfirmHelper.checkLoginAndVerify(activity, callback)
    }

    /**
     * 云同步SDK二次校验
     */
    override fun checkAccountIsVerified(): Boolean {
        return AccountManager.getInstance().checkAccountIsNeedVerify()
    }

    override fun newCloudStatusDialog(mContext: Context, owner: LifecycleOwner?): ICloudSyncStatusDialog {
        return CloudSyncStatusDialog(mContext, owner)
    }

    override fun newCloudUpgradeHelper(): ICloudUpgradeHelper {
        return CloudUpgradeHelper()
    }

    /**
     * 请求登录
     */
    override fun requestSignLogin(context: Context) {
        AccountManager.sAccountManager.apply {
            reqSignInAccount(context, object : IAccountInfoCallback {
                /*这个account不靠谱，可能是null*/
                override fun onComplete(account: AccountBean) {
                    DebugUtil.i(TAG, "reqSignInAccount  $account")
                    val accountId = getLoginIdFromCache(BaseApplication.getAppContext())
                    if (accountId.isBlank().not()) {
                        // 登录成功，不同的账号，需要先执行退出登录逻辑，再执行同步逻辑
                        if (checkUserIdChanged(context, accountId)) {
                            saveUserId(context, accountId)
                            stopSyncForLoginOut(BaseApplication.getAppContext(), false)
                            trigCloudSync(BaseApplication.getAppContext(), SYNC_TYPE_RECOVERY_MANUAL)
                        } else {
                            trigCloudSync(BaseApplication.getAppContext(), SYNC_TYPE_RECOVERY_START_APP)
                        }
                    }
                }
            })
        }
    }

    /**
     * 打开云服务快应用或浏览器H5
     */
    override fun launchCloudApp(context: Context) {
        CloudSyncStatusDialogHelper.startToCloudApp(context)
    }

    override fun isMediaComparing(): Boolean {
        return RecordDataSync.getInstance().isMediaComparing() ?: false
    }
}