/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryProcess
 * Description:
 * Version: 1.0
 * Date: 2025/5/13
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/13      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import com.soundrecorder.summary.model.SummaryRequestModel

interface IAISummaryProcess {

    fun startAISummary(model: SummaryRequestModel): Boolean

    fun cancelAISummary(mediaId: Long): Boolean

    fun releaseAISummary(mediaId: Long) {}

    fun registerAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {}

    fun unregisterAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {}
}