/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryContentView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.animation.Animator
import android.animation.ValueAnimator
import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.scrollview.COUIScrollView
import com.coui.appcompat.snackbar.COUISnackBar
import com.coui.appcompat.textview.COUITextView
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.share.ShareSummaryCopy
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.utils.Injector
import androidx.core.content.FileProvider
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.Intent.FLAG_ACTIVITY_CLEAR_TOP
import android.content.Intent.FLAG_ACTIVITY_NEW_TASK
import android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import com.soundrecorder.base.StorageManager
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.removableapp.RemovableAppManager
import com.soundrecorder.common.removableapp.InstallResultCallback
import java.io.File
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.exportfile.ExportDoc
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryModel
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.model.SummaryTrace
import com.soundrecorder.summary.exportfile.ExportPdf
import com.soundrecorder.summary.exportfile.ExportSummaryData
import com.soundrecorder.summary.exportfile.SummaryContent
import com.soundrecorder.summary.ui.content.SummaryAnimateTextView.TextAnimationListener
import com.soundrecorder.summary.ui.content.callback.ISummaryFunctionCallback
import com.soundrecorder.summary.ui.content.plugin.AgentClickPlugin
import com.soundrecorder.summary.ui.content.plugin.SummaryLinkResolver
import com.soundrecorder.share.normal.note.ExportNoteUtil
import io.noties.markwon.Markwon
import io.noties.markwon.ext.tasklist.TaskListPlugin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import android.view.ViewGroup
import androidx.annotation.VisibleForTesting
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.content.ContextCompat
import java.text.SimpleDateFormat
import java.util.*
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.oplus.vfxsdk.rsview.COEShadowDrawable
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.summary.request.AISummaryProcess.Companion.IS_OVER_SIZE

class SummaryContentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUIScrollView(context, attrs, defStyleAttr), OnClickListener {

    companion object {
        private const val TAG = "SummaryContentView"
        private const val LOADING_TIPS_DELAY = 2000L

        private const val ITEM_POSITION_0 = 0
        private const val ITEM_POSITION_1 = 1
        private const val ITEM_POSITION_2 = 2

        private const val SIZE_TWO = 2

        private const val TAG_NEXT = "next"
        private const val TAG_REFRESH = "refresh"
        private const val DOC_SUFFIX = "doc"
        private const val PDF_SUFFIX = "pdf"
        private const val DURATION_TIME = 5000
        private const val JUMP_TO_NOTE_DETAIL_ACTION = "action.nearme.note.textnote"
        private const val JUMP_TO_NOTE_HOME_ACTION = "action.nearme.note.allnote"
        private const val DOC_PACKAGE_NAME = "andes.oplus.documentsreader"
        private const val YOZO_CONST = "com.yozo.DispatchActivity"
        private const val APPRECOVER_DISPLAY_LIST = "com.oplus.apprecover.APPRECOVER_DISPLAY_LIST"
        private const val JUMP_TO_SYSTEM_APP = "com.oplus.apprecover"
        private const val GUID = "guid"
        private const val KEY_SHOW_AI_PANEL = "key_show_ai_panel"
        private const val FILE_PATH = "File_Path"

        private const val BG_FILE = "bg_flow.coz"

        private const val DURATION = 300L
        private const val OPAQUE = 255
    }

    private lateinit var container: ConstraintLayout
    private lateinit var loadingView: EffectiveAnimationView
    private lateinit var content: SummaryAnimateTextView
    private lateinit var cardContainer: LinearLayout
    private lateinit var copyRight: COUITextView
    private lateinit var divider: View
    private lateinit var toolBar: RelativeLayout
    private lateinit var copy: AppCompatImageView
    private lateinit var export: AppCompatImageView
    private lateinit var scene: COUITextView
    private lateinit var previous: AppCompatImageView
    private lateinit var refresh: AppCompatImageView
    private lateinit var errorView: ConstraintLayout
    private lateinit var errorMsgText: COUITextView
    private lateinit var retry: COUITextView
    private lateinit var loadTip: COUIDefaultTopTips

    private val agentDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(
            context.resources,
            com.soundrecorder.summary.R.drawable.ic_agent_un_check,
            context.theme
        )
    }
    private val agentUnCheckDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(
            context.resources,
            com.soundrecorder.summary.R.drawable.ic_agent_check,
            context.theme
        )
    }

    private val shadowDrawable: COEShadowDrawable? by lazy {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            COEShadowDrawable(context, BG_FILE, isZip = true)
        } else {
            null
        }
    }

    private val contentMarginLoadTip: Int by lazy {
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp16)
    }

    private var scenePopList: COUIPopupListWindow? = null
    private var exportPopList: COUIPopupListWindow? = null

    private val activity = context as Activity
    private val lifecycle = (context as? AppCompatActivity)?.lifecycleScope
    private var loadingJob: Job? = null
    private var summaryFunctionCallback: ISummaryFunctionCallback? = null
    private var summaryContentText: String = ""        // 渲染后的文本内容
    private var summaryOriginText: String = ""         // 原始摘要文本

    private val notePackageName = AppUtil.getNotesPackageName()

    private var currentTheme: SummaryTheme = SummaryTheme()
    private var identifiedTheme: SummaryTheme = SummaryTheme()
    private var agent = mutableListOf<SummaryAgentEvent>()

    private var isLoadingFinish = false
    private var bgAnimator: ValueAnimator? = null
    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    private fun initChild() {
        initContainer()
        initLoading()
        initContent()
        initTools()
        initCardContainer()
        initOther()
        initErrorView()
        initLoadTip()
    }

    private fun initContainer() {
        LayoutInflater.from(context)
            .inflate(com.soundrecorder.summary.R.layout.layout_summary_container_view, this, true)
        container = findViewById(com.soundrecorder.summary.R.id.container_view)
    }

    private fun initLoading() {
        loadingView = findViewById(com.soundrecorder.summary.R.id.summary_loading)
    }

    private fun showLoadingTips() {
        copyRight.setText(com.soundrecorder.base.R.string.summary_loading_tips)
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_loading
        copyRight.visible()
    }

    private fun initContent() {
        content = findViewById(com.soundrecorder.summary.R.id.summary_content)
        content.movementMethod = LinkMovementMethod.getInstance()
        content.highlightColor = Color.TRANSPARENT
    }

    private fun initTools() {
        toolBar = findViewById(com.soundrecorder.summary.R.id.summary_tool_bar)
        copy = findViewById(com.soundrecorder.summary.R.id.copy)
        export = findViewById(com.soundrecorder.summary.R.id.export)
        scene = findViewById(com.soundrecorder.summary.R.id.summary_scene)
        previous = findViewById(com.soundrecorder.summary.R.id.previous)
        previous.gone()
        refresh = findViewById(com.soundrecorder.summary.R.id.refresh)

        copy.setOnClickListener(this)
        export.setOnClickListener(this)
        scene.setOnClickListener(this)
        previous.setOnClickListener(this)
        refresh.setOnClickListener(this)
    }

    private fun initCardContainer() {
        cardContainer = findViewById(com.soundrecorder.summary.R.id.card_container)
    }

    private fun initOther() {
        copyRight = findViewById(com.soundrecorder.summary.R.id.copyright)
        divider = findViewById(com.soundrecorder.summary.R.id.divider_line)
    }

    private fun initErrorView() {
        errorView = findViewById(com.soundrecorder.summary.R.id.layout_error)
        errorMsgText = findViewById(com.soundrecorder.summary.R.id.error_msg_text)
        retry = findViewById(com.soundrecorder.summary.R.id.retry)
        retry.setOnClickListener(this)
        COUITextViewCompatUtil.setPressRippleDrawable(retry)
    }

    private fun initLoadTip() {
        loadTip = findViewById(com.soundrecorder.summary.R.id.load_tips)
        loadTip.setCloseDrawable(ContextCompat.getDrawable(context, com.support.tips.R.drawable.coui_ic_toptips_close))
        loadTip.setStartIcon(ContextCompat.getDrawable(context, com.soundrecorder.summary.R.drawable.ic_detail_floating_layer))
        loadTip.setAnimatorDismissListener(object : Animator.AnimatorListener {
            override fun onAnimationEnd(animation: Animator) {
                loadTipsGone()
            }

            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }
        })
        loadTip.setCloseBtnListener {
            loadTip.dismissWithAnim()
        }
    }

    init {
        initChild()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        onStopLoading()
        content.cancelAnimation()
        scenePopList?.dismiss()
        bgAnimator?.cancel()
    }

    override fun onClick(v: View?) {
        v ?: return
        if (isLoadingFinish.not()) {
            return
        }
        if (ClickUtils.isQuickClick()) {
            return
        }
        when (v) {
            copy -> copySummary()
            export -> showExportPopMenu()
            scene -> showScenePopMenu()
            previous -> clickPrevious()
            refresh -> clickRefreshOrNext()
            retry -> clickRetry()
            else -> DebugUtil.w(TAG, "click what ? v $v")
        }
    }

    private fun copySummary() {
        summaryFunctionCallback?.onClickCopy()
        lifecycle ?: return
        val shareTextContent = summaryOriginText
        if (summaryOriginText.isEmpty()) {
            DebugUtil.w(TAG, "why is empty? Check again carefully")
            return
        }
        shareAction?.share(
            activity,
            ShareTextContent(-1, false, "", 0, emptyList()),
            ShareSummaryCopy(shareTextContent),
            lifecycle,
            null
        )
    }

    private fun showExportPopMenu() {
        exportPopList?.dismiss()
        lifecycle?.launch {
            val isSupportExportDoc = withContext(Dispatchers.Default) {
                ExportDoc.isSupportExport(context)
            }
            val exportItemList = mutableListOf<PopupListItem>().apply {
                val builder = PopupListItem.Builder()
                if (isSupportExportDoc) {
                    builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_doc)
                        .setTitle(
                            context.getString(
                                com.soundrecorder.common.R.string.summary_export_to,
                                context.getString(com.soundrecorder.common.R.string.word)
                            )
                        )
                        .setGroupId(com.soundrecorder.common.R.id.group1)
                    add(builder.build())
                }
                builder.reset()

                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_pdf)
                    .setTitle(
                        context.getString(
                            com.soundrecorder.common.R.string.summary_export_to,
                            context.getString(com.soundrecorder.common.R.string.pdf)
                        )
                    )
                    .setGroupId(com.soundrecorder.common.R.id.group2)
                add(builder.build())


                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_note)
                    .setTitle(context.getString(com.soundrecorder.common.R.string.summary_export_to_note))
                    .setGroupId(com.soundrecorder.common.R.id.group3)
                add(builder.build())
            }

            exportPopList = COUIPopupListWindow(context).apply {
                this.itemList = exportItemList
                this.anchorView = export
                this.resetOffset()
                this.setOnItemClickListener { _, _, pos, _ ->
                    when {
                        itemList.size > SIZE_TWO -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToDoc()
                                ITEM_POSITION_1 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }

                        else -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }
                    }
                    this.dismiss()
                }
                this.show()
            }
        }
    }

    private fun exportToDoc() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_WORD)
        lifecycle?.launch(Dispatchers.IO) {
            runCatching {
                // 准备导出数据
                val exportData = prepareExportData()

                // 生成文件路径
                val targetPath = generateExportFilePath(DOC_SUFFIX)

                if (targetPath.isEmpty()) {
                    showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    DebugUtil.e(TAG, "export File Failed exportData: $exportData, targetPath: $targetPath")
                    return@launch
                }

                // 执行导出
                val success  = ExportDoc.saveToWord(context, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    //导出成功 弹窗 button 点击跳转文档预览文件
                    if (success) {
                        showDocumentSnackBar(
                            context.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                            context.getString(com.soundrecorder.common.R.string.export_view_look),
                            targetPath
                        )
                    } else {
                        showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportFile error: ${it.message}")
            }
        }
    }

    private fun exportToPdf() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_PDF)
        lifecycle?.launch(Dispatchers.IO) {
            runCatching {
                // 准备导出数据
                val exportData = prepareExportData()

                // 生成文件路径
                val targetPath = generateExportFilePath(PDF_SUFFIX)

                if (targetPath.isEmpty()) {
                    showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    DebugUtil.e(TAG, "export File Failed exportData: $exportData, targetPath: $targetPath")
                    return@launch
                }

                // 执行导出
                val success = ExportPdf.saveToPdf(context, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    if (success) {
                        showDocumentSnackBar(
                            context.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                            context.getString(com.soundrecorder.common.R.string.export_view_look),
                            targetPath
                        )
                    } else {
                        showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportToPdf error: ${it.message}")
            }
        }
    }

    private fun exportToNote() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_NOTE)

        lifecycle?.launch(Dispatchers.IO) {
            runCatching {
                // 准备便签内容
                val noteContent = prepareNoteContent()

                // 生成便签标题
                val noteTitle = generateExportTitle()

                // 构建 ContentValues，参考 ExportNoteHelper 的实现
                val values = ContentValues().apply {
                    put("package_name", context.packageName)
                    put("title", noteTitle)
                    put("content", noteContent)
                }
                val install = isNoteAppInstalled(activity)

                // 直接插入到系统便签应用，复用 ExportNoteUtil 的便签保存机制
                val insertUri = ExportNoteUtil.insertToNote(context, values)

                withContext(Dispatchers.Main) {
                    if (install && insertUri != null) {
                        DebugUtil.d(TAG, "exportToNote success: insertUri=$insertUri")
                        //复用已有逻辑，弹出COUISnackBar 点击查看 跳转便签
                        showNoteSnackBar(
                            context.getString(com.soundrecorder.common.R.string.summary_export_to_note),
                            context.getString(com.soundrecorder.common.R.string.export_view_look),
                            insertUri)
                    } else {
                        DebugUtil.e(TAG, "exportToNote failed: insertUri is null")
                        //插入便签失败，便签被删除，跳转应用找回页面，引导用户安装系统应用便签
                        showInstallNotesDialog(activity, notePackageName)
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportToNote error: ${it.message}")
            }
        }
    }

    private fun showScenePopMenu() {
        scenePopList?.dismiss()
        scenePopList = COUIPopupListWindow(context).apply {
            this.itemList = getSceneList()
            this.anchorView = scene
            this.resetOffset()
            this.setOnItemClickListener { _, _, position, _ ->
                val theme = when {
                    itemList.size > SIZE_TWO -> {
                        when (position) {
                            ITEM_POSITION_0 -> identifiedTheme
                            ITEM_POSITION_1 -> SummaryTheme("", SummaryTheme.SAMPLE)
                            else -> SummaryTheme("", SummaryTheme.DETAIL)
                        }
                    }

                    else -> {
                        when (position) {
                            ITEM_POSITION_0 -> SummaryTheme("", SummaryTheme.SAMPLE)
                            else -> SummaryTheme("", SummaryTheme.DETAIL)
                        }
                    }
                }
                currentTheme = theme
                summaryFunctionCallback?.onClickScene(theme)
                scene.text = getThemeTitle(currentTheme)
                this.dismiss()
            }
        }
        scenePopList?.show()
    }

    private fun getSceneList(): List<PopupListItem> {
        return mutableListOf<PopupListItem>().apply {
            val builder = PopupListItem.Builder()
            if (identifiedTheme.theme.isNotEmpty() || identifiedTheme.style != -1) {
                builder.setTitle(getThemeTitle(identifiedTheme))
                    .setId(com.soundrecorder.summary.R.id.current_scene)
                    .setGroupId(com.soundrecorder.common.R.id.group1)
                    .setIsChecked(identifiedTheme == currentTheme)
                add(builder.build())
            }

            builder.reset()
            builder.setTitle(SummaryTheme.getTitle(context, SummaryTheme.SAMPLE))
                .setId(com.soundrecorder.summary.R.id.sample_scene)
                .setGroupId(com.soundrecorder.common.R.id.group2)
                .setIsChecked(currentTheme.style == SummaryTheme.SAMPLE)
            add(builder.build())

            builder.reset()
            builder.setTitle(SummaryTheme.getTitle(context, SummaryTheme.DETAIL))
                .setId(com.soundrecorder.summary.R.id.detail_scene)
                .setGroupId(com.soundrecorder.common.R.id.group3)
                .setIsChecked(currentTheme.style == SummaryTheme.DETAIL)
            add(builder.build())
        }
    }

    private fun clickPrevious() {
        summaryFunctionCallback?.onClickPrevious()
        refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
    }

    private fun clickRefreshOrNext() {
        DebugUtil.d(TAG, "clickRefreshOrNext ${refresh.tag}")
        if (refresh.tag == TAG_REFRESH) {
            summaryFunctionCallback?.onClickRefresh()
        } else {
            summaryFunctionCallback?.onClickNext()
        }
    }

    private fun clickRetry() {
        summaryFunctionCallback?.onClickRetry()
    }

    fun setSummaryFunctionCallback(callback: ISummaryFunctionCallback) {
        summaryFunctionCallback = callback
    }

    fun setSummaryContent(summaryModel: SummaryModel) {
        onStopLoading()
        showCopyright()
        divider.visible()
        toolBar.visible()
        content.visible()
        loadTipsGone()
        agent.clear()
        agent.addAll(summaryModel.agentEvents)
        updateScene(summaryModel.theme)
        val markdown = buildMarkDown()
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            summaryModel.summary,
            summaryModel.summaryTrace,
            summaryModel.entities,
            summaryModel.agentEvents
        )
        val spanned = markdown.toMarkdown(formatStream)
        content.text = spanned
        summaryContentText = spanned.toString()
        summaryOriginText = summaryModel.summary
        isLoadingFinish = true
    }

    /**
     * 检测是否是最新的摘要，如果是，视觉上会有一些不同的UI
     */
    fun checkCurrentState(isLastSummary: Boolean, isOnly: Boolean, isFirstSummary: Boolean) {
        DebugUtil.d(
            TAG,
            "isLastSummary = $isLastSummary, isOnly = $isOnly, isFirstSummary = $isFirstSummary"
        )
        previous.clearFocus()
        refresh.clearFocus()
        when {
            isOnly -> {
                previous.gone()
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_refresh)
                refresh.tag = TAG_REFRESH
            }

            isLastSummary -> {
                previous.visible()
                previous.isEnabled = true
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious)
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_refresh)
                refresh.tag = TAG_REFRESH
            }

            isFirstSummary -> {
                previous.visible()
                previous.isEnabled = false
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious_disable)
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }

            else -> {
                previous.visible()
                previous.isEnabled = true
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious)
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }
        }
    }

    fun onStartLoading() {
        startLoadingBgAnim()
        val hasContent = summaryOriginText.isNotEmpty()
        DebugUtil.d(TAG, "onStartLoading hasContent = $hasContent")
        if (hasContent) {
            return
        }
        loading()
        if (copyRight.isVisible.not()) {
            loadingJob = lifecycle?.launch {
                delay(LOADING_TIPS_DELAY)
                showLoadingTips()
            }
        }
    }

    private fun startLoadingBgAnim() {
        DebugUtil.e(TAG, "startLoadingBgAnim isLoadingFinish = $isLoadingFinish, ${BaseUtil.isLightOS()}")
        if (BaseUtil.isLightOS().not() && isLoadingFinish.not()) {
            kotlin.runCatching {
                val parent = this.parent as? ViewGroup ?: return@runCatching
                shadowDrawable?.let { drawable ->
                    bgAnimator?.cancel()
                    drawable.alpha = 0
                    parent.background = drawable
                    bgAnimator = ValueAnimator.ofInt(0, OPAQUE).apply {
                        duration = DURATION
                        interpolator = COUIEaseInterpolator()
                        doOnStart {
                            parent.background?.alpha = 0
                            drawable.start(parent)
                        }
                        doOnEnd {
                            parent.background?.alpha = OPAQUE
                        }
                        addUpdateListener {
                            parent.background?.alpha = it.animatedValue as Int
                        }
                    }
                    bgAnimator?.start()
                }
            }.onFailure {
                DebugUtil.e(TAG, "startLoadingBgAnim it = ${it.message}")
            }
        }
    }

    fun onStopLoading() {
        stopLoading()
        stopLoadingBgAnim()
        loadingJob?.cancel()
        loadingJob = null
    }

    private fun loading() {
        if (loadingView.isVisible.not()) {
            loadingView.visible()
            loadingView.playAnimation()
        }
    }

    private fun stopLoading() {
        loadingView.cancelAnimation()
        loadingView.gone()
    }

    fun onStartSummary() {
        reset()
        loadTipsGone()
        content.gone()
        cardContainer.gone()
        copyRight.gone()
        divider.gone()
        toolBar.gone()
        errorView.gone()
        onStartLoading()
    }

    private fun stopLoadingBgAnim() {
        if (BaseUtil.isLightOS().not()) {
            kotlin.runCatching {
                val parent = this.parent as? ViewGroup ?: return@runCatching
                bgAnimator?.cancel()
                bgAnimator = ValueAnimator.ofInt(OPAQUE, 0).apply {
                    duration = DURATION
                    interpolator = COUIEaseInterpolator()
                    doOnStart {
                        parent.background?.alpha = OPAQUE
                    }
                    doOnEnd {
                        parent.background?.alpha = 0
                        shadowDrawable?.stop()
                        parent.background = null
                    }
                    addUpdateListener {
                        parent.background?.alpha = it.animatedValue as Int
                    }
                }
                bgAnimator?.start()
            }.onFailure {
                DebugUtil.e(TAG, "stopLoadingBgAnim ${it.message}")
            }
        }
    }

    private fun reset() {
        summaryContentText = ""
        summaryOriginText = ""
        agent.clear()
        isLoadingFinish = false
        currentTheme = SummaryTheme("", -1)
        identifiedTheme = SummaryTheme("", -1)
    }

    fun updateStream(stream: String, animator: Boolean = true, extra: Map<String, Any>? = null) {
        DebugUtil.d(TAG, "updateStream stream = $stream, animator = $animator")
        if (stream.isEmpty()) {
            return
        }
        stopLoading()
        toolBar.gone()
        divider.gone()
        errorView.gone()
        showCopyright()
        val isOverSize = (extra?.get(IS_OVER_SIZE) as? Boolean) ?: false
        updateLoadTips(isOverSize)
        content.visible()
        content.cancelAnimation()
        val markdown = buildMarkDown()
        val summaryAgent = SummaryDataParser.parseAgent(context, stream)
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            stream,
            emptyList(),
            emptyList(),
            summaryAgent
        )
        val spanned = markdown.toMarkdown(formatStream)
        summaryContentText = formatStream
        summaryOriginText = stream
        if (animator) {
            content.setAnimateText(spanned, false)
            content.setAnimationListener(object : TextAnimationListener {
                override fun onAnimationEnd() {
                    content.text = spanned
                }

                override fun onAnimationUpdate(curReadyPos: Int) {
                    super.onAnimationUpdate(curReadyPos)
                    smoothScrollToVisibleText(curReadyPos)
                }
            })
        } else {
            content.text = spanned
        }
    }

    private fun smoothScrollToVisibleText(curReadyPos: Int) {
        val layout = content.layout ?: return
        val startLine = layout.getLineForOffset(0)
        val endLine = layout.getLineForOffset(curReadyPos)
        val height = layout.getLineBottom(endLine) - layout.getLineTop(startLine)
        val offset = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp12)
        val scrollDistance = height + paddingTop - measuredHeight
        if (scrollDistance > 0) {
            smoothCOUIScrollTo(0, scrollDistance + offset)
        }
    }

    private fun buildMarkDown(): Markwon {
        return Markwon.builder(context)
            .usePlugin(
                if (agentDrawable != null && agentUnCheckDrawable != null) {
                    TaskListPlugin.create(agentDrawable, agentUnCheckDrawable)
                } else {
                    TaskListPlugin.create(context)
                }
            )
            .usePlugin(AgentClickPlugin(context) { isDone, task ->
                agentClick(isDone, task)
            }).applyLinkResolver(SummaryLinkResolver(context) { view, entry ->
                linkClick(view, entry)
            })
            .build()
    }

    private fun agentClick(isDone: Boolean, task: String) {
        agent.forEach {
            if (it.agent == task) {
                it.isDone = isDone
            }
        }
        summaryFunctionCallback?.onClickAgent(agent)
        summaryContentText =
            SummaryStyleHelper.updateStyleAfterTaskStateChange(summaryContentText, isDone, task)
    }

    private fun linkClick(view: View, entity: SummaryEntity) {
        summaryFunctionCallback?.onClickEntity(view, entity)
    }

    private fun showCopyright() {
        loadingJob?.cancel()
        copyRight.visible()
        copyRight.setText(com.soundrecorder.base.R.string.summary_copyright)
        copyRight.setCompoundDrawablesRelativeWithIntrinsicBounds(
            com.soundrecorder.summary.R.drawable.ic_copyright,
            0,
            0,
            0
        )
        copyRight.compoundDrawablePadding =
            context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp4)
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_content
    }

    fun onFinishSummary(summaryModel: SummaryModel) {
        val fullContent = summaryModel.summary
        DebugUtil.d(TAG, "onFinishSummary = $fullContent, ${summaryModel.agentEvents}")
        val theme = summaryModel.theme
        val summaryEntity = summaryModel.entities
        copyRight.visible()
        errorView.gone()
        val isOverSize = (summaryModel.extra?.get(IS_OVER_SIZE) as? Boolean) ?: false
        updateLoadTips(isOverSize)
        agent.clear()
        agent.addAll(summaryModel.agentEvents)
        updateScene(theme)
        val markdown = buildMarkDown()
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context = context,
            originText = fullContent,
            trace = emptyList(),
            entities = summaryEntity,
            agents = summaryModel.agentEvents
        )
        DebugUtil.i(TAG, "formatStream : $formatStream")
        val spanned = markdown.toMarkdown(formatStream)
        summaryContentText = formatStream
        summaryOriginText = fullContent
        content.setAnimateText(spanned, false)
        content.setAnimationListener(object : TextAnimationListener {
            override fun onAnimationEnd() {
                DebugUtil.d(TAG, "onFinishSummary onAnimationEnd")
                content.text = spanned
                divider.visible()
                toolBar.visible()
                isLoadingFinish = true
                summaryFunctionCallback?.onFinishAnimator()
                stopLoadingBgAnim()
                scrollToBottom()
            }

            override fun onAnimationUpdate(curReadyPos: Int) {
                super.onAnimationUpdate(curReadyPos)
                smoothScrollToVisibleText(curReadyPos)
            }
        })
    }

    private fun updateLoadTips(isOverSize: Boolean) {
        DebugUtil.i(TAG, "updateLoadTips isOverSize = $isOverSize")
        val needShowLoadTips = isOverSize
        val msg = when {
            isOverSize -> resources.getString(com.soundrecorder.common.R.string.summary_tips_content_over_size)
            else -> ""
        }
        loadTip.setTipsText(msg)
        when {
            needShowLoadTips && loadTip.isVisible.not() -> loadTipsVisible()
            needShowLoadTips.not() && loadTip.isVisible -> loadTipsGone()
            else -> DebugUtil.d(TAG, "do nothing")
        }
    }

    private fun loadTipsVisible() {
        val constraintSet = ConstraintSet()
        constraintSet.clone(container)
        constraintSet.clear(loadTip.id, ConstraintSet.TOP)
        constraintSet.connect(loadTip.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0)
        constraintSet.clear(content.id, ConstraintSet.TOP)
        constraintSet.connect(content.id, ConstraintSet.TOP, loadTip.id, ConstraintSet.BOTTOM, contentMarginLoadTip)
        constraintSet.applyTo(container)
        loadTip.visible()
    }

    private fun loadTipsGone() {
        val constraintSet = ConstraintSet()
        constraintSet.clone(container)
        constraintSet.clear(loadTip.id, ConstraintSet.TOP)
        constraintSet.clear(content.id, ConstraintSet.TOP)
        constraintSet.connect(content.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0)
        constraintSet.applyTo(container)
        loadTip.gone()
    }

    private fun scrollToBottom() {
        if (getChildAt(0).height > height) {
            post { fullScroll(View.FOCUS_DOWN) }
        }
    }

    fun onError(canRetry: Boolean, errorMsg: String) {
        DebugUtil.d(TAG, "onError canRetry")
        onStopLoading()
        loadTipsGone()
        toolBar.gone()
        divider.gone()
        errorView.gone()
        copyRight.gone()
        content.gone()
        errorView.visible()
        errorMsgText.text = errorMsg
        if (canRetry) {
            retry.visible()
        } else {
            retry.gone()
        }
        isLoadingFinish = true
    }

    fun updateSummaryTrace(summaryTrace: List<SummaryTrace>) {
        DebugUtil.d(TAG, "updateSummaryTrace summaryTrace = $summaryTrace:")
    }

    fun isSummaryAnimator(): Boolean {
        return content.isAnimating
    }

    fun stopAnimator() {
        onStopLoading()
        content.cancelAnimation()
    }

    private fun updateScene(theme: SummaryTheme) {
        DebugUtil.d(TAG, "updateScene theme = $theme")
        identifiedTheme = theme
        currentTheme = if (identifiedTheme.theme.isEmpty() && identifiedTheme.style == -1) {
            SummaryTheme("", SummaryTheme.DETAIL)
        } else {
            theme
        }
        val title = getThemeTitle(currentTheme)
        scene.text = title
    }

    private fun getThemeTitle(theme: SummaryTheme): String {
        return when {
            (theme.style != -1) -> SummaryTheme.getTitle(context, theme.style)
            theme.theme.isNotEmpty() -> theme.theme
            else -> SummaryTheme.getTitle(context, SummaryTheme.DETAIL)
        }
    }

    private fun getSummaryText(): String {
        return summaryContentText
    }

    /**
     * 准备导出数据
     */
    @VisibleForTesting
    private fun prepareExportData(): ExportSummaryData {
        val title = generateExportTitle()
        val summaryContent = SummaryContent(content = getSummaryText(), htmlContent = emptyList())
        val bitmapList = prepareBitmapList()
        return ExportSummaryData(
            title = title,
            summary = summaryContent,
            bitmap = bitmapList
        )
    }

    /**
     * 准备便签内容
     */
    @VisibleForTesting
    private fun prepareNoteContent(): String {
        val timestamp = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
        return buildString {
            appendLine(context.getString(com.soundrecorder.common.R.string.time) + "$timestamp")
            appendLine()
            appendLine(context.getString(com.soundrecorder.common.R.string.summary_with_colon))
            appendLine(getSummaryText())
            //将当前录音文件也传过去
            appendLine()
        }
    }

    /**
     * 生成导出标题
     */
    private fun generateExportTitle(): String {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        return "录音摘要_$timestamp"
    }

    /**
     * 生成导出文件路径
     * 参考项目中录音文件存储逻辑，导出到Music/Recordings/Export目录下
     */
    private fun generateExportFilePath(extension: String): String {
        val fileName = "${generateExportTitle()}.$extension"

        // 获取存储前缀路径（与录音文件存储方式一致）
        val storagePrefix = StorageManager.getInstance(context).storagePrefix

        // 构建Music/Recordings/Export路径
        val recordingsPath = storagePrefix + File.separator +
                Environment.DIRECTORY_MUSIC + File.separator +
                Constants.RECORDINGS
        val exportPath = recordingsPath + File.separator + "Export"

        // 创建目录
        val exportDir = File(exportPath)
        if (!exportDir.exists()) {
            exportDir.mkdirs()
        }
        val targetFile = File(exportDir, fileName)
        // 尝试使用Music/Recordings/Export目录（与录音文件存储方式一致）
        return targetFile.absolutePath
    }

    /**
     * 准备图片列表
     */
    private fun prepareBitmapList(): List<String> {
        return emptyList()
    }

    /**
     * 显示导出成功消息
     */
    private fun showDocumentSnackBar(message: String, actionText: String, fileAbsPath: String) {
        COUISnackBar.make(this, message, DURATION_TIME).apply {
            (parent as? ViewGroup)?.clipChildren = false
            setOnAction(actionText) {
                openDocumentFile(fileAbsPath)
            }
            show()
        }
    }

    /**
     * 显示便签导出成功的SnackBar
     */
    private fun showNoteSnackBar(message: String, actionText: String, insertUri: Uri) {
        COUISnackBar.make(this, message, DURATION_TIME).apply {
            (parent as? ViewGroup)?.clipChildren = false
            setOnAction(actionText) {
                jumpToNote(insertUri, activity)
            }
            show()
        }
    }

    /**
     * 显示导出错误消息
     */
    private fun showExportError(message: String) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), message)
    }

    /**
     * 打开Word文档文件
     */
    private fun openDocumentFile(filePath: String) {
        try {
            val intent = Intent().apply {
                action = Intent.ACTION_VIEW
                addFlags(FLAG_ACTIVITY_NEW_TASK or FLAG_GRANT_READ_URI_PERMISSION)
                setPackage(DOC_PACKAGE_NAME)
                putExtra(KEY_SHOW_AI_PANEL, true)
                // 传入文件的绝对路径，因为文件在公共目录下
                putExtra(FILE_PATH, filePath)
                setClassName(DOC_PACKAGE_NAME, YOZO_CONST)
            }

            context.startActivity(intent)
            DebugUtil.d(TAG,  "Successfully opened document with OPPO reader: $filePath")
        } catch (e: ActivityNotFoundException) {
            DebugUtil.w(TAG, "OPPO document reader not found, trying fallback: $filePath")
            // 如果OPPO文档阅读器不可用，尝试使用系统的应用选择器
            showAppChooser(filePath)
        }
    }

    /**
     * 显示系统应用选择器
     */
    private fun showAppChooser(filePath: String) {
            val file = File(filePath)
            val uri = FileProvider.getUriForFile(context, "${context.packageName}.fileProvider", file)
            val intent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, "*/*")
                addFlags(FLAG_GRANT_READ_URI_PERMISSION or FLAG_ACTIVITY_NEW_TASK)
            }
            val chooserIntent = Intent.createChooser(intent, "")
            chooserIntent.addFlags(FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(chooserIntent)
    }

    /**
     * 跳转到便签应用
     */
    private fun jumpToNote(insertUri: Uri, context: Activity) {
        val localId = ExportNoteUtil.getLocalId(insertUri)
        val intent = Intent(JUMP_TO_NOTE_DETAIL_ACTION).apply {
            setPackage(notePackageName)
            putExtra(GUID, localId)
            addFlags(FLAG_ACTIVITY_NEW_TASK or FLAG_ACTIVITY_CLEAR_TOP)
        }
        context.startActivity(intent)
        DebugUtil.d(TAG, "Successfully jumped to note detail: $localId")
    }

    /**
     * 便签应用是否下载
     */
    private fun isNoteAppInstalled(context: Activity): Boolean {
        val isInstalled = try {
            context.packageManager.getPackageInfo(notePackageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
        return  isInstalled
    }

    private fun showInstallNotesDialog(activity: Activity, pkg: String) {
        val title = String.format(
            resources.getString(com.soundrecorder.common.R.string.summary_install_note),
            resources.getString(com.soundrecorder.common.R.string.app_name_main)
        )
        COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Custom)
            .setTitle(title)
            .setCustomMessage(activity.getString(com.soundrecorder.common.R.string.summary_install_note_content))
            .setCustomDrawable(AppCompatResources.getDrawable(activity, com.soundrecorder.summary.R.drawable.ic_note))
            .setPositiveButton(com.soundrecorder.common.R.string.install) { _, _ ->
                reInstallNote()
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
            .show()
    }

    /**
     * 使用系统应用找回功能重新安装便签应用
     */
    private fun reInstallNote() {
        runCatching {
            DebugUtil.d(TAG, "Starting note app reinstallation using removable app manager")
            val removableAppManager = RemovableAppManager(notePackageName)
            // 获取便签应用信息
            val removableAppInfo = removableAppManager.obtainRemovableInfo()
            // 执行重新安装
            removableAppManager.reInstallApp(context, removableAppInfo, callback = object : InstallResultCallback {
                    override fun onInstalledResult(success: Boolean) {
                        DebugUtil.d(TAG, "Note app reinstallation result: $success")
                        // 在主线程中处理结果
                        lifecycle?.launch(Dispatchers.Main) {
                            if (success) {
                                // 安装成功，提示用户重新尝试导出
                                val intent = Intent(JUMP_TO_NOTE_HOME_ACTION).apply {
                                    setPackage(notePackageName)
                                    addFlags(FLAG_ACTIVITY_NEW_TASK or FLAG_ACTIVITY_CLEAR_TOP)
                                }
                                context.startActivity(intent)
                            } else {
                                // 安装失败，显示错误信息
                                showExportError(context.getString(com.soundrecorder.common.R.string.second_verify_failed))
                            }
                        }
                    }
                },
                negative = {
                    // 用户在安装过程中取消
                    DebugUtil.d(TAG, "User cancelled note app installation")
                }
            )
        }.onFailure {
            DebugUtil.e(TAG, "Error during note app reinstallation: ${it.message}")
            // 如果系统应用找回失败，回退到原有的应用恢复列表逻辑
            jumpAppRecoverList()
        }
    }

    private fun jumpAppRecoverList() {
        kotlin.runCatching {
            val intent = Intent().apply {
                action = APPRECOVER_DISPLAY_LIST
                addFlags(FLAG_ACTIVITY_NEW_TASK)
                setPackage(JUMP_TO_SYSTEM_APP)
                putExtra("fromPkg", context.packageName)
            }
            context.startActivity(intent)
        }.onFailure {
            DebugUtil.e(TAG, "jumpAppRecoverList,error ${it.message}")
        }
    }
}