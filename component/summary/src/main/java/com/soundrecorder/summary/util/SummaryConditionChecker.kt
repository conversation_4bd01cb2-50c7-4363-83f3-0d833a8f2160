/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryConditionChecker
 * Description:
 * Version: 1.0
 * Date: 2024/3/1
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/1 1.0 create
 */

package com.soundrecorder.summary.util

import android.content.Context
import androidx.core.os.bundleOf
import com.oplus.aiunit.core.AIUnit
import com.oplus.aiunit.core.data.DetectName
import com.oplus.aiunit.toolkits.AISettings
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.MetaDataUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE_WARN
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_SMALL
import com.soundrecorder.modulerouter.summary.ERROR_CODE_FORMAT
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SIZE_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUCCESS
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUPER_SAVE_MODE
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.summary.RecordSummaryManager

object SummaryConditionChecker {
    /**从aiunit:nlp、av中定义的常量复制处理，升级unitsdk中，有冲突;不能随意修改*/
    const val AUDIO_RT_ASR = "realtime_asr"
    private const val PARAM_KEY_SUMMARY_TYPE = "ai::key::call_summary::summary_type"
    private const val PARAM_KEY_ASR_TYPE = "ai::key::asr::asr_type"
    private const val TYPE_SUMMARY_RECORD = 1
    private const val TYPE_ASR_RECORD = 1
    /**从aiunit:nlp、av中定义的常量复制处理，升级unitsdk中，有冲突 end*/
    /*摘要支持最大文件size*/
    private const val MAX_SUPPORT_RECORD_SIZE = 500 * 1024 * 1024

    /*摘要支持最大警告文件时长：2小时*/
    private const val MAX_SUPPORT_RECORD_DURATION_WARN = 2 * 60 * 60 * 1000

    /*摘要支持最大文件时长：5小时*/
    private const val MAX_SUPPORT_RECORD_DURATION = 5 * 60 * 60 * 1000

    /*摘要支持最小文件时长*/
    private const val MIN_SUPPORT_RECORD_DURATION = 20 * 1000

    /*便签支持录音摘要的metadata最小值*/
    private const val NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE = 1
    private const val NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE_EXPORT = 2
    /*摘要支持最大时长*/
    private const val SUMMARY_MAX_DURATION_HOUR = 5
    private const val TAG = "SummaryConditionChecker"

    @JvmStatic
    fun checkSummaryPreCondition(context: Context, record: Record): Int {
        if (RecordRouterManager.instance?.isSuperPowerSaveModeState(context) == true) { // 超级省电模式
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_super_power_mode)
            return ERROR_CODE_SUPER_SAVE_MODE
        }

        if (!isFileFormatMet(record.mimeType)) { // 格式不支持
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_record_format_not_support)
            return ERROR_CODE_FORMAT
        }
        if (!isDamageFileDuration(record.duration)) { // 时长<=0
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_damage_file)
            return ERROR_CODE_DURATION_SMALL
        }

        if (!isFileDurationMinMet(record.duration)) { // 时长<=20s
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.summary_error_record_short)
            return ERROR_CODE_DURATION_SMALL
        }

        if (!isFileSizeMaxMet(record.fileSize)) { // 大于500M
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.toast_summary_error_size_over)
            return ERROR_CODE_SIZE_LARGE
        }

        if (!isFileDurationMaxMet(record.duration)) { //大于5小时
            ToastManager.showShortToast(
                context, context.resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.summary_error_record_long,
                    SUMMARY_MAX_DURATION_HOUR,
                    SUMMARY_MAX_DURATION_HOUR
                )
            )
            return ERROR_CODE_DURATION_LARGE
        }

        if (!isFileDurationMaxWarnMet(record.duration)) { // 大于2小时
            return ERROR_CODE_DURATION_LARGE_WARN
        }
        return ERROR_CODE_SUCCESS
    }

    /**
     * AI_UNIT 是否支持录音摘要
     */
    @JvmStatic
    fun isAIUnitSupportSummary(context: Context): Boolean {
        if (!AIUnit.isDeviceSupported(context)) { // 判断设备是否支持或安装AIUnit
            DebugUtil.w(TAG, "isAIUnitSupportSummary false by device enable")
            return false
        }
        if (!AISettings.isRecordSummarySupported(context)) { // 判断当前版本是否是录音摘要版本
            DebugUtil.w(TAG, "isAIUnitSupportSummary false by record summary enable")
            return false
        }
        return true
    }

    /**
     * AI UNIT能力是否可用
     * isAvailable，能力是否可用，可用一定支持，支持不一定可用
     * true：可用
     * false：不可用，根据具体错误码判断，目前仅涉及STATE_UNAVAILABLE-设备不支持等原因
     * 错误码具体可见4.2 https://odocs.myoas.com/docs/loqeWm01PpUog1An?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&
     * pcMsgShowStyle=1&lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1
     */
    @JvmStatic
    fun isAiUnitAvailable(context: Context): Boolean {
        // asr
        if (!AISettings.isDetectSupported(context, DetectName.AUDIO_ASR, bundleOf(PARAM_KEY_ASR_TYPE to TYPE_ASR_RECORD))) {
            DebugUtil.w(
                TAG,
                "isAiUnitAvailable-asr enable: ${
                    AISettings.getDetectData(
                        context,
                        DetectName.AUDIO_ASR,
                        bundleOf(PARAM_KEY_ASR_TYPE to TYPE_ASR_RECORD))
                }")
            return false
        }

        // summary
        if (!AISettings.isDetectSupported(context, DetectName.NLP_CALL_SUMMARY, bundleOf(PARAM_KEY_SUMMARY_TYPE to TYPE_SUMMARY_RECORD))) {
            DebugUtil.w(
                TAG,
                "isAiUnitAvailable-summary enable: ${
                    AISettings.getDetectData(
                        context,
                        DetectName.NLP_CALL_SUMMARY,
                        bundleOf(PARAM_KEY_SUMMARY_TYPE to TYPE_SUMMARY_RECORD))
                }")
            return false
        }
        return true
    }

    /**
     * 判断语音转文字是否支持录音摘要
     * metaData["record_summary_support"] = true
     */
    @JvmStatic
    fun isAssistantSupportSummary(context: Context): Boolean {
        val isSupport = MetaDataUtils.getMetaDataBoolean(context, RecordSummaryManager.PACKAGE_NAME_ASSISTANT, "record_summary_support")
        if (!isSupport) {
            DebugUtil.w(TAG, "isAssistantSupportSummary false")
        }
        return isSupport
    }

    /**
     * 判断翻译服务是否支持录音摘要
     * metaData["record_summary_support"] = true
     */
    @JvmStatic
    fun isTranslateSupportSummary(context: Context): Boolean {
        val isSupport = MetaDataUtils.getMetaDataBoolean(context, RecordSummaryManager.PACKAGE_NAME_TRANSLATE, "record_summary_support")
        if (!isSupport) {
            DebugUtil.w(TAG, "isTranslateSupportSummary false")
        }
        return isSupport
    }

    /**
     * 判断便签是否支持录音摘要
     * 如果metaData["record_summary_support"] >= 1，代表支持录音摘要
     * 如果metaData["record_summary_support"] >= 2，代表支持录音摘要外销
     */
    @JvmStatic
    fun isNotesSupportSummary(context: Context): Boolean {
        val minVersion =
            if (BaseUtil.isEXP()) NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE_EXPORT else NOTE_SUPPORT_SUMMARY_METADATA_MIN_VALUE
        return MetaDataUtils.getMetaDataInt(
            context, AppUtil.getNotesPackageName(), "record_summary_support") >= minVersion
    }

    @JvmStatic
    fun isSupportRecordSummary(context: Context): Boolean {
        return isAIUnitSupportSummary(context)
                && isAssistantSupportSummary(context)
                && isTranslateSupportSummary(context)
    }

    /**
     * 校验文件格式mimeType
     */
    @JvmStatic
    fun isFileFormatMet(fileFormat: String): Boolean {
        return when (fileFormat) {
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_3GPP,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB,
            RecordConstant.MIMETYPE_WAV,
            RecordConstant.MIMETYPE_ACC,
            RecordConstant.MIMETYPE_ACC_ADTS,
            -> true

            else -> false
        }
    }

    /**
     * 文件大小是否满足最大限制
     */
    @JvmStatic
    fun isFileSizeMaxMet(fileSize: Long): Boolean {
        return fileSize < MAX_SUPPORT_RECORD_SIZE
    }

    /**
     * 文件时长是否满足最小限制
     */
    @JvmStatic
    fun isFileDurationMinMet(fileDuration: Long): Boolean {
        return fileDuration > MIN_SUPPORT_RECORD_DURATION
    }

    @JvmStatic
    fun isDamageFileDuration(fileDuration: Long): Boolean {
        return fileDuration > 0
    }

    /**
     * 文件时长是否满足最大限制警告
     */
    @JvmStatic
    fun isFileDurationMaxWarnMet(fileDuration: Long): Boolean {
        return fileDuration <= MAX_SUPPORT_RECORD_DURATION_WARN
    }

    /**
     * 文件时长是否满足最大限制
     */
    @JvmStatic
    fun isFileDurationMaxMet(fileDuration: Long): Boolean {
        return fileDuration <= MAX_SUPPORT_RECORD_DURATION
    }
}