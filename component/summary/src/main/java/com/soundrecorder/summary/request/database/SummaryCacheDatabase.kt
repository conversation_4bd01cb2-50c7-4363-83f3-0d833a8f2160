/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SummaryCacheDatabase
 * Description:
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/12      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

private const val DATABASE_NAME = "ai_summary.db"
private const val DATABASE_VERSION = 1

@Database(
    entities = [SummaryCacheEntity::class],
    version = DATABASE_VERSION,
    exportSchema = false
)

abstract class SummaryCacheDatabase : RoomDatabase() {
    /**
     * obtain the DAO for access AI Summary data
     */
    abstract fun summaryCacheDao(): SummaryCacheDao

    companion object {
        @Volatile
        private var databaseInstance: SummaryCacheDatabase? = null

        @JvmStatic
        fun getInstance(context: Context): SummaryCacheDatabase =
            databaseInstance ?: synchronized(this) {
                databaseInstance ?: buildDatabase(context.applicationContext).also {
                    databaseInstance = it
                }
            }

        @JvmStatic
        private fun buildDatabase(context: Context): SummaryCacheDatabase =
            Room.databaseBuilder(context, SummaryCacheDatabase::class.java, DATABASE_NAME)
                .enableMultiInstanceInvalidation()
                .build()
    }
}