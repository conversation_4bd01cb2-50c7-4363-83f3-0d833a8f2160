/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExportTxt.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.exportfile

import android.content.Context
import com.soundrecorder.base.utils.DebugUtil
import java.io.File
import java.io.FileOutputStream

object ExportTxt {
    private const val TAG = "ExportTxt"

    /**
     * 保存txt类型文件
     * dataList: MutableList<String?>?
     */
    fun saveReport(act: Context, targetPath: String, title: String, text: String) {
        if (text == null) {
            DebugUtil.d(TAG, "dataList or text is null")
            return
        }
        val file = File(targetPath)
        val filepath = file.absolutePath
        kotlin.runCatching {
            val data = text + "\n"
            FileOutputStream(filepath, true).use { fileOutputStream ->
                val bytes = data.toByteArray()
                fileOutputStream.write(bytes)
            }
        }.onFailure {
            DebugUtil.d(TAG, "saveReport: $it")
        }
    }
}
