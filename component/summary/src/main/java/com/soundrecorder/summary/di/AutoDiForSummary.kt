/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForSummary.kt
 * * Description : AutoDiForSummary
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.summary.di

import com.soundrecorder.modulerouter.summary.ConversionFileAction
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.summary.api.ConversionFileApi
import com.soundrecorder.summary.api.SummaryApi
import org.koin.dsl.module

object AutoDiForSummary {
    val summaryModule = module {
        single<SummaryInterface>(createdAtStart = true) {
            SummaryApi
        }
        single<ConversionFileAction>(createdAtStart = true) {
            ConversionFileApi
        }
    }
}