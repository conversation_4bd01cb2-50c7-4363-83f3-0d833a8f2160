/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryLinkResolver
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.plugin

import android.content.Context
import android.view.View
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.model.SummaryEntity
import io.noties.markwon.LinkResolver

class SummaryLinkResolver(
    context: Context,
    private val onClick: ((view: View, entity: SummaryEntity) -> Unit)? = null
) : LinkResolver {

    companion object {
        private const val TAG = "SummaryLinkResolver"
    }

    override fun resolve(view: View, link: String) {
        kotlin.runCatching {
            val entity = GsonUtil.getGson().fromJson(link, SummaryEntity::class.java)
            onClick?.invoke(view, entity)
        }.onFailure {
            DebugUtil.e(TAG, "resolve it ${it.message}")
        }
    }
}