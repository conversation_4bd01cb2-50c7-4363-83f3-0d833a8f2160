/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordSummaryControl
 * Description:
 * Version: 1.0
 * Date: 2024/3/4
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/4 1.0 create
 */

package com.soundrecorder.summary

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import androidx.core.os.bundleOf
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.displayName
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.OpenIdUtils
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.Sentence
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.utils.calling.ContactCallRecordUtil
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.summary.ACTION_SUMMARY_STATE_CHANGED
import com.soundrecorder.modulerouter.summary.BUNDLE_CALL_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_FROM_WHERE
import com.soundrecorder.modulerouter.summary.BUNDLE_MEDIA_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_NOTE_ID
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE_WARN
import com.soundrecorder.modulerouter.summary.ERROR_CODE_NO_INTERNET
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUCCESS
import com.soundrecorder.modulerouter.summary.ISummaryCallback
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.client.ISummaryStateChangeListener
import com.soundrecorder.summary.client.RecordSummaryServiceClient
import com.soundrecorder.summary.client.RecordSummaryServiceClient.Companion.SUMMARY_STATE_ENABLE
import com.soundrecorder.summary.client.RecordSummaryServiceClient.Companion.SUMMARY_STATE_END
import com.soundrecorder.summary.client.RecordSummaryServiceClient.Companion.SUMMARY_STATE_GEN_NOTE_ID
import com.soundrecorder.summary.client.RecordSummaryServiceClient.Companion.SUMMARY_STATE_PREPARED
import com.soundrecorder.summary.client.RecordSummaryServiceClient.Companion.SUMMARY_STATE_START
import com.soundrecorder.summary.client.RecordSummaryServiceClient.Companion.SUMMARY_STATE_SUMMARY_END
import com.soundrecorder.summary.client.ServiceConnectListener
import com.soundrecorder.summary.data.LrcFileResult
import com.soundrecorder.summary.data.SummaryRecordInfo
import com.soundrecorder.summary.data.SummaryStateResult
import com.soundrecorder.summary.ui.DurationWarnActivity
import com.soundrecorder.summary.util.SummaryConditionChecker
import com.soundrecorder.summary.util.SummaryLrcFileUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.io.File
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

object RecordSummaryManager {
    private const val TAG = "RecordSummaryManager"

    /*语音转文字包名*/
    const val PACKAGE_NAME_ASSISTANT = "com.coloros.accessibilityassistant"

    /*翻译服务包名*/
    const val PACKAGE_NAME_TRANSLATE = "com.coloros.translate.engine"

    const val SUMMARY_STATE_CLIENT_INIT = 0
    const val SUMMARY_STATE_CLIENT_GENERATING = 1
    const val SUMMARY_STATE_CLIENT_END = 2

    private const val DOWNLOAD_MODEL_ERROR = "privacy_aiunit_guide_error"
    private const val DOWNLOAD_MODEL_NOT_SUPPORT = "download_aiunit_not_support"
    private const val DOWNLOAD_MODEL_SCENE_NONE = "download_aiunit_scene_data_is_null"
    private const val DOWNLOAD_MODEL_FAIL = "download_aiunit_fail"

    private val scope: CoroutineScope = GlobalScope
    private val recordMap = ConcurrentHashMap<Long, LrcFileResult>()
    private val appContext by lazy { BaseApplication.getAppContext() }
    //记录摘要的实际状态，仅用内存存储，不做持久化
    private val summaryStateMap = ConcurrentHashMap<Long, Int>()
    private val summaryCallbackMap = ConcurrentHashMap<ISummaryCallback, Long>()
    private val summaryServiceClient: RecordSummaryServiceClient by lazy {
        RecordSummaryServiceClient()
    }

    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val summaryStateListener: ISummaryStateChangeListener by lazy {
        object : ISummaryStateChangeListener {
            override fun onSummaryStateChanged(state: Int, type: Int, result: SummaryStateResult?) {
                DebugUtil.d(TAG, "onSummaryStateChanged, $result")
                when (state) {
                    // 语言转文字内部准备结果回调，失败 or 成功
                    SUMMARY_STATE_ENABLE -> { // 0- 准备失败
                        DebugUtil.w(TAG, "onSummaryStateChanged enable,msg=${result?.errorMessage},count=${result?.count}")
                        handleAvailableCount(state, result?.count)
                        getSummaryCallback(result?.mediaId).forEachRun {
                            it.onSummaryPrepare(false)
                        }
                        recordMap.remove(result?.mediaId)?.run {
                            removeUriPermissionAndLrc(appContext, this)
                        }
                        dealErrorMessage(result?.errorMessage)
                    }
                    // 7-准备成功
                    SUMMARY_STATE_PREPARED -> {
                        getSummaryCallback(result?.mediaId).forEachRun {
                            it.onSummaryPrepare(true)
                        }
                    }
                    // 1-摘要流程开始
                    SUMMARY_STATE_START -> {
                        setSummaryState(result?.mediaId, SUMMARY_STATE_START)
                        getSummaryCallback(result?.mediaId).forEachRun {
                            it.onSummaryStart()
                        }
                        seedingApi?.onSummaryStart(result?.mediaId)
                        removeLrcFile(appContext, recordMap[result?.mediaId])
                    }
                    // 8-生成便签ID
                    SUMMARY_STATE_GEN_NOTE_ID -> {
                        if (result?.noteId != null) {
                            val recordFile = recordMap[result.mediaId] ?: return
                            scope.launch {
                                NoteDbUtils.updateOrInsertNote(recordFile.toNoteData(result.callId, result.noteId!!, result.mediaId.toString()))
                                sendNotifyNoteIdBroadcast(recordFile.fromWhere, result.noteId!!, result.callId!!, result.mediaId!!)
                                if (recordFile.isContactCallType()) {
                                    // 通话录音，更新电话本摘要flag
                                    ContactCallRecordUtil.setCallRecordingFlag(appContext, recordFile.mediaPath?.displayName() ?: "", true)
                                }
                            }
                        }
                        setSummaryState(result?.mediaId, SUMMARY_STATE_GEN_NOTE_ID)
                        getSummaryCallback(result?.mediaId).forEachRun {
                            it.onSummaryGenNotedId(result?.noteId, result?.callId)
                        }
                    }
                    // 5-完成摘要（成功or失败）
                    SUMMARY_STATE_SUMMARY_END -> {
                        setSummaryState(result?.mediaId, SUMMARY_STATE_SUMMARY_END)
                        getSummaryCallback(result?.mediaId).forEachRun {
                            it.onSummaryProgressEnd(result?.errorInfo?.asrError, result?.errorInfo?.summaryError)
                        }
                        val recordFile = recordMap[result?.mediaId] ?: return
                        scope.launch { // 有错误信息，拿到的noteId可能是上次生成的，这里若要修改，需确认清楚
                            val notedId = if (result?.errorInfo?.getErrorCode() == 0) result.noteId else null
                            val noteData = recordFile.toNoteData(result?.callId, notedId, result?.mediaId.toString()).also {
                                it.noteState = result?.errorInfo?.getErrorCode()
                            }
                            NoteDbUtils.updateNoteByMediaId(noteData)
                            seedingApi?.onSummaryProgressEnd(
                                result?.mediaId,
                                result?.noteId,
                                result?.callId,
                                result?.errorInfo?.asrError,
                                result?.errorInfo?.summaryError)
                        }
                    }
                    // 6-完全结束-流体云卡片消失
                    SUMMARY_STATE_END -> {
                        setSummaryState(result?.mediaId, SUMMARY_STATE_END)
                        // 摘要流程结束，移除相关uri授权、lrc文件
                        recordMap.remove(result?.mediaId)?.run {
                            removeUriPermissionAndLrc(BaseApplication.getAppContext(), this)
                        }
                        getSummaryCallback(result?.mediaId).forEachRun {
                            it.onSummaryEnd()
                        }
                    }

                    /*2-当前有正在生成摘要的文件*/
                    RecordSummaryServiceClient.SUMMARY_STATE_IN_PROGRESS -> {
                        ToastManager.showShortToast(appContext, com.soundrecorder.common.R.string.summary_error_record_generating)
                        if (result?.mediaId == result?.runningMediaId) {
                            // 当前正在转摘要的和当前点击的是同一条数据，则不做移除逻辑
                            DebugUtil.w(TAG, "onSummaryStateChanged in progress same file")
                            return
                        }
                        recordMap.remove(result?.mediaId)?.run {
                            removeUriPermissionAndLrc(appContext, this)
                            getSummaryCallback(result?.mediaId).forEachRun {
                                it.onSummaryProgressing(
                                    result?.runningMediaId,
                                    result?.runningMd5,
                                    result?.runningCallId)
                            }
                        }
                    }

                    /*3-今日剩余可用次数，触发摘要流程，sdk会查询一次返回，等于0，则今日次数已用完*/
                    RecordSummaryServiceClient.SUMMARY_STATE_AVAILABLE_TIMES -> {
                        handleAvailableCount(state, result?.count)
                        getSummaryCallback(result?.mediaId).forEachRun {
                            it.onAvailableTimeResult(result?.count)
                        }
                    }
                }
            }
        }
    }

    private fun setSummaryState(mediaId: Long?, newState: Int) {
        if (mediaId != null) {
            summaryStateMap[mediaId] = newState
        }
    }

    /**
     * 播放详情页获取摘要状态：生成摘要、生成中、查看摘要
     * 1、正常场景下：生成摘要-start-生成中-end-查看摘要
     * 2、过程中录音被杀，内存缓存值丢失
     * 1)唤醒后回调了start，变为生成中
     * 2）唤醒后回调了noteId生成，变为生成中
     * 3)唤醒活回调了end，判断是否有noteId决定查看摘要还是生成摘要
     */
    fun getSummaryState(mediaId: Long?, noteId: String?): Int {
        val curState = summaryStateMap[mediaId]
        return if (curState == SUMMARY_STATE_START || curState == SUMMARY_STATE_GEN_NOTE_ID) {
            SUMMARY_STATE_CLIENT_GENERATING
        } else if (!noteId.isNullOrEmpty()) {
            SUMMARY_STATE_CLIENT_END
        } else {
            SUMMARY_STATE_CLIENT_INIT
        }
    }

    /**
     * 是否已经有摘要中或者生成的
     * 用在播放详情页里是否显示摘要按钮
     */
    fun hasSummaryByState(summaryState: Int?): Boolean {
        return summaryState == SUMMARY_STATE_CLIENT_GENERATING || summaryState == SUMMARY_STATE_CLIENT_END
    }

    private fun dealErrorMessage(errorMessage: String?) {
        when (errorMessage) {
            DOWNLOAD_MODEL_FAIL,
            DOWNLOAD_MODEL_ERROR,
            DOWNLOAD_MODEL_NOT_SUPPORT,
            DOWNLOAD_MODEL_SCENE_NONE -> {
                ToastManager.showShortToast(
                    appContext,
                    com.soundrecorder.base.R.string.search_result_load_error
                )
            }
        }
    }

    /**
     * @param fromState 从哪个状态码获取到的（0：从缓存拿到的数量； 3：从接口查询到的数量）
     * @param count 剩余可用次数
     */
    @JvmStatic
    fun handleAvailableCount(fromState: Int, count: Long?) {
        /*仅0状态的次数为0才toast提示*/
        if (fromState == SUMMARY_STATE_ENABLE && count == 0L) {
            ToastManager.showShortToast(appContext, com.soundrecorder.common.R.string.summary_error_reach_max_count)
        }
    }

    /**
     * 开始摘要
     * @param from 点击入口来源，用于埋点 @See SummaryStaticUtil
     * @param record 媒体库音频文件
     * @param convertContent 音频转文本内容
     * @param markList 标记信息
     */
    @JvmStatic
    fun startSummaryWithPreCheck(from: String, record: Record, convertContent: List<Sentence>?, markList: List<MarkDataBean>?) {
        SummaryConditionChecker.checkSummaryPreCondition(appContext, record).run {
            DebugUtil.d(TAG, "startSummary checkPreCondition code $this")
            if (this != ERROR_CODE_DURATION_LARGE_WARN) {
                getSummaryCallback(record.id).forEachRun {
                    it.onRecordPreCheckResult(from, this)
                }
            }
            if (this != ERROR_CODE_SUCCESS) {
                if (this == ERROR_CODE_DURATION_LARGE_WARN) {
                    DurationWarnActivity.sRecordInfo = SummaryRecordInfo(from, record, convertContent, markList)
                    appContext.startActivity(Intent(appContext, DurationWarnActivity::class.java).apply {
                        flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    })
                }
                return
            }
        }
        startSummaryNoPreCheck(from, record, convertContent, markList)
    }

    @JvmStatic
    fun dispatchDurationWarnResult(ok: Boolean, mediaId: Long?, from: String) {
        getSummaryCallback(mediaId).forEachRun {
            it.onRecordPreCheckResult(from,
                if (ok) {
                    ERROR_CODE_SUCCESS
                } else {
                    ERROR_CODE_DURATION_LARGE_WARN
                })
        }
    }

    @JvmStatic
    fun startSummaryNoPreCheck(from: String, record: Record, convertContent: List<Sentence>?, markList: List<MarkDataBean>?) {
        if (NetworkUtils.isNetworkInvalid(appContext)) { // 校验网络
            ToastManager.showShortToast(appContext, com.soundrecorder.common.R.string.network_disconnect)
            getSummaryCallback(record.id).forEachRun {
                it.onRecordPreCheckResult(from, ERROR_CODE_NO_INTERNET)
            }
            return
        }
        summaryServiceClient.serviceConnListener = object : ServiceConnectListener {
            override fun onConnectSuccess() {
                scope.launch(Dispatchers.IO) {
                    doStartSummary(from, record, convertContent, markList)
                }
            }

            override fun onDisconnected(name: ComponentName?) {
                super.onDisconnected(name)
                recordMap.forEach {
                    removeLrcFile(appContext, it.value)
                }
                recordMap.clear()
            }
        }
        summaryServiceClient.summaryStateListener = summaryStateListener
        summaryServiceClient.startSummaryService(appContext)
    }

    @JvmStatic
    fun stopSummary(context: Context) {
        summaryServiceClient.stopSummaryService(context)
        runCatching {
            scope.cancel()
        }
    }

    @JvmStatic
    fun doStartSummary(from: String, record: Record, sentenceList: List<Sentence>?, markList: List<MarkDataBean>?) {
        DebugUtil.i(TAG, "doStartSummary,start")
        val recordUri = MediaDBUtils.genUri(record.id)
        record.checkMd5(recordUri)
        val lrcResult = SummaryLrcFileUtil.createLrcFile(record, sentenceList, markList).also {
            it.fromWhere = from
            it.mediaUri = recordUri
            it.md5 = record.mD5
            it.mediaPath = record.data
            val calUuid = if (record.uuid.isNullOrBlank() && it.isCallType()) {
                ContactCallRecordUtil.queryCallRecordingUUID(appContext, record.data.displayName() ?: "")?.apply {
                    if (isNotEmpty()) {
                        it.contactFlag = true
                    }
                }
            } else {
                record.uuid
            }
            it.callUUID = calUuid ?: UUID.randomUUID().toString()
        }

        recordMap[record.id] = lrcResult

        val bundle = bundleOf().apply {
            putInt("type", (lrcResult.summaryType)) // 类型 :1 普通录音或者 2 通话录音
            putString("callId", lrcResult.callUUID) // asr和摘要唯一ID
            putString("mp3_file", recordUri.toString()) //URI只给字幕授权的URI地址
            putString("lrc_file", lrcResult.lrcFileUri?.toString())  //URI只给字幕授权的URI地址-lrc图片地址直接授权给便签，字幕暂时不拷贝
            putString("audioName", record.displayName.title()) //字幕流体云显示的，文件名称
            putLong("audioTime", record.dateModied * TimeUtils.TIME_ONE_SECOND) //字幕流体云显示的,音频时间：ms
            putLong("audioDuration", record.duration) //音频时长，ms
            putString("duid", OpenIdUtils.INSTANCE.duid.run {
                /*调式中发现重置手机后有概率第一次进入录音，获取到duid为null，这里坐下处理，为null就使用uuid。该字段使用于记录摘要可用次数，若为null当做另外设备*/
                if (isBlank()) {
                    DebugUtil.w(TAG, "doStartSummary did is empty")
                    return@run OpenIdUtils.INSTANCE.uuid
                }
                return@run this
            }) // 透传asr
            putString("fileName", record.displayName) // 透传便签的
            putString("filePath", record.data) // 透传便签的
            putString("mediaId", record.id.toString()) // 透传便签的
            putString("md5", lrcResult.md5) // 透传便签的
        }
        DebugUtil.i(TAG, "doStartSummary,bundle=$bundle")
        grantSummaryUriPermission(BaseApplication.getAppContext(), lrcResult)
        summaryServiceClient.performSummary(bundle)
    }

    @JvmStatic
    fun registerSummaryCallback(mediaId: Long, listener: ISummaryCallback) {
        summaryCallbackMap[listener] = mediaId
    }

    @JvmStatic
    fun unregisterSummaryCallback(listener: ISummaryCallback) {
        summaryCallbackMap.remove(listener)
    }

    @JvmStatic
    private fun getSummaryCallback(mediaId: Long?): List<ISummaryCallback>? {
        mediaId ?: return null
        var result: MutableList<ISummaryCallback>? = null
        summaryCallbackMap.forEach {
            if (it.value == mediaId) {
                if (result == null) {
                    result = mutableListOf()
                }
                result?.add(it.key)
            }
        }
        return result
    }

    fun List<ISummaryCallback>?.forEachRun(innerFun: ((callback: ISummaryCallback) -> Unit)) {
        if (this.isNullOrEmpty()) {
            return
        }
        forEach {
            innerFun.invoke(it)
        }
    }

    /**
     * 给便签、语音转文字授予文件读取权限
     */
    @JvmStatic
    private fun grantSummaryUriPermission(context: Context, lrcFileResult: LrcFileResult) {
        DebugUtil.d(TAG, "grantSummaryUriPermission")
        lrcFileResult.mediaUri?.let { // 授权音频文件给语音转文字
            context.grantUriPermission(PACKAGE_NAME_ASSISTANT, it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        lrcFileResult.lrcFileUri?.let { // 授权LRC文件给语音转文字
            context.grantUriPermission(PACKAGE_NAME_ASSISTANT, it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        lrcFileResult.picMarkUriList?.forEach { // 授权图片标记的图片文件给便签
            context.grantUriPermission(AppUtil.getNotesPackageName(), it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
    }

    /**
     * 移除文件授权以及删除LRC文件
     */
    @JvmStatic
    private fun removeUriPermissionAndLrc(context: Context, lrcFileResult: LrcFileResult) {
        DebugUtil.d(TAG, "removeUriPermissionAndLrc ${lrcFileResult.mediaPath.title()}")
        lrcFileResult.mediaUri?.let {
            context.revokeUriPermission(it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        lrcFileResult.picMarkUriList?.forEach {
            context.revokeUriPermission(it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        removeLrcFile(context, lrcFileResult)
    }

    /**
     * 移除文件授权以及删除LRC文件
     */
    @JvmStatic
    private fun removeLrcFile(context: Context, lrcFileResult: LrcFileResult?) {
        DebugUtil.d(TAG, "removeLrcFile ${lrcFileResult?.mediaPath.displayName()}")
        lrcFileResult?.lrcFileUri?.let {
            context.revokeUriPermission(it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
            /*删除对应文件*/
            runCatching {
                val file = File(SummaryLrcFileUtil.getLrcSaveDirPath(context), lrcFileResult.lrcFileName)
                val result = file.delete()
                DebugUtil.d(TAG, "removeLrcFile file ${lrcFileResult.lrcFileName}, result = $result")
            }.onFailure {
                DebugUtil.e(TAG, "removeLrcFile delete lrc error $it")
            }
        }
    }

    /**
     * 拿到noteId后通知首页列表刷新数据
     */
    @JvmStatic
    private fun sendNotifyNoteIdBroadcast(fromWhere: String?, noteId: String, callId: String, mediaId: Long) {
        val intent = Intent(ACTION_SUMMARY_STATE_CHANGED).apply {
            putExtra(BUNDLE_FROM_WHERE, fromWhere)
            putExtra(BUNDLE_NOTE_ID, noteId)
            putExtra(BUNDLE_MEDIA_ID, mediaId)
            putExtra(BUNDLE_CALL_ID, callId)
        }
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }
}