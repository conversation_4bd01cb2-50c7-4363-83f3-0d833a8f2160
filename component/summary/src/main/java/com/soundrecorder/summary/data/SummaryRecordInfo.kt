/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryRecordInfo
 * Description:
 * Version: 1.0
 * Date: 2024/3/8
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/8 1.0 create
 */

package com.soundrecorder.summary.data

import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.Sentence
import com.soundrecorder.common.databean.markdata.MarkDataBean

data class SummaryRecordInfo(
    val from: String,
    var record: Record? = null,
    var convertSentence: List<Sentence>? = null,
    var markList: List<MarkDataBean>? = null
)