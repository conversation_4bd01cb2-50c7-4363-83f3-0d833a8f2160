/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryRecordParamGetter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.request

import android.content.Context
import android.os.Environment
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import com.oplus.unified.summary.sdk.speech.DialogContent
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.splitOddTrimEnd
import com.soundrecorder.base.ext.splitTrimEnd
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.SyncTimeUtils.TIME_5_MIN
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.share.OShareConvertUtil.KEY_END_FLAG
import com.soundrecorder.common.share.OShareConvertUtil.KEY_START_FLAG
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.modulerouter.convertService.ConvertServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets
import java.util.UUID

object SummaryRecordParamGetter {

    const val TAG = "SummaryRecordParamGetter"
    private const val COUNT_OSHARE_CONVERT_HEADER_LINE: Int = 4
    private const val TIME_UNIT = 1000
    private const val CONVERT_PATH_FLAG: String = "/convert"
    private const val SPLIT_FLAG: String = "/"
    private const val SPLIT_SPACE: String = " "

    const val SUMMARY_MIN_SIZE = 100
    private const val SUMMARY_MAX_SIZE = 30000

    private const val POS_0: Int = 0
    private const val POS_1: Int = 1
    private const val POS_2: Int = 2
    private const val POS_3: Int = 3
    private const val POS_4: Int = 4
    private const val POS_5: Int = 5
    private const val POS_6: Int = 6
    private const val CONTENT_3: Int = 3
    private const val CONTENT_5: Int = 5
    private const val CONTENT_7: Int = 7

    private const val SPEAKER_TYPE_OURSIDE = "ourside"
    private const val SPEAKER_TYPE_THIRDSIDE = "thirdSide"
    private const val SPEAKER_TYPE_OTHERSIDE = "otherside"
    private const val TIME_SECOND = 1000

    data class Result(
        val content: String,
        val dialogContent: List<DialogContent>,
        val length: Int,
        val isOverSize: Boolean
    )


    private val convertServiceApi by lazy {
        Injector.injectFactory<ConvertServiceInterface>()
    }

    @JvmStatic
    fun getAISummaryRecordParam(mediaId: Long, recordType: Int, recordTime: Long): AISummaryRecordParam {
        val sessionId = UUID.randomUUID().toString()
        val timeout = TIME_5_MIN
        //区分内外销，设置输入输出语言，后面要补充
        val inputLanguage = "zh"
        val outputLanguage = "zh"
        val content = getAsrContent(mediaId, RecordModeConstant.RECORD_TYPE_CALL, recordTime, inputLanguage)
        val contentText = content.content
        val dialogContent = content.dialogContent
        val length = content.length
        val isOverSize = content.isOverSize
        return AISummaryRecordParam(
            mediaId,
            sessionId,
            timeout,
            inputLanguage,
            outputLanguage,
            contentText,
            dialogContent,
            length,
            isOverSize
        )
    }

    @JvmStatic
    private fun getAsrContent(
        mediaId: Long,
        recordType: Int,
        recordTime: Long,
        inputLanguage: String
    ): Result {
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        val isConvertComplete = ConvertDbUtil.checkAlreadyConvertComplete(convertRecord)
        if (isConvertComplete.not()) {
            return Result("", emptyList(), 0, false)
        }
        val convertFileName = getConvertFileName(convertRecord)
        val convertContentItems: ArrayList<ConvertContentItem>? =
            if (TextUtils.isEmpty(convertFileName)) {
                null
            } else {
                /* 如果是OShare目录下的文件，采用对应文本解析规则 */
                if (convertRecord?.isOShareFile == true) {
                    readOShareConvertContent(
                        convertRecord.convertTextfilePath,
                        convertRecord.serverPlanCode
                    )
                } else {
                    convertRecord?.serverPlanCode?.let {
                        readConvertContent(BaseApplication.getAppContext(), convertFileName, it)
                    }
                }
            }

        if (convertContentItems.isNullOrEmpty()) {
            return Result("", emptyList(), 0, false)
        }
        return when (recordType) {
            RecordModeConstant.RECORD_TYPE_CALL -> {
                val dialogContent = mutableListOf<DialogContent>()
                var length = 0
                var index = 0
                var isOverSize = false
                for (item in convertContentItems) {
                    if (length >= SUMMARY_MAX_SIZE) {
                        isOverSize = true
                        break
                    }
                    val timestamp = recordTime * TIME_SECOND + item.startTime
                    dialogContent.add(
                        DialogContent(
                            id = index + 1,
                            content = item.textContent,
                            timestamp = timestamp,
                            speakerType = SPEAKER_TYPE_OTHERSIDE,
                            language = inputLanguage,
                        )
                    )
                    length += item.textContent.length
                    index++
                }
                Result("", dialogContent, length, isOverSize)
            }

            else -> {
                val stringBuilder = StringBuilder()
                var isOverSize = false
                for (item in convertContentItems) {
                    val format = "${item.startTime} ${item.roleName} ${item.textContent}"
                    if (stringBuilder.length + format.length >= SUMMARY_MAX_SIZE) {
                        isOverSize = true
                        break
                    }
                    if (stringBuilder.isNotEmpty()) {
                        stringBuilder.append("\n") // 可以根据需要设置分隔符
                    }
                    stringBuilder.append(format)
                }
                val string = stringBuilder.toString()
                Result(string, emptyList(), string.length, isOverSize)
            }
        }
    }

    @JvmStatic
    private fun getConvertFileName(convertRecord: ConvertRecord?): String? {
        val convertFilePath: String = convertRecord?.convertTextfilePath ?: return null
        var convertFileName: String? = null
        if (convertFilePath.isNotEmpty()) {
            val savePathDir = convertServiceApi?.getConvertSavePath(BaseApplication.getAppContext()) + File.separator
            convertFileName = convertFilePath.replace(savePathDir, "")
        }
        DebugUtil.i(TAG, "getConvertFileName, convertFileName:$convertFileName convertFilePath = $convertFilePath")
        return convertFileName
    }

    /**
     *
     * 封装此格式位置：OShareConvertUtil.getTextContent
     *
     * 解析OShare目录下的转文本文件
     * 文本内容格式：
     * 文本标题
     *
     * 时间：2025年2月7日 17:03:30 time:unix时间戳
     * 主题:  录音文件名称
     * 参会人：<讲话人序号><讲话人 1>， <讲话人序号><讲话人2>
     *
     * 讲话人 1 该句话起始时间字符串00:01 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     *
     * 讲话人 1该句话起始时间字符串00:03 <讲话人序号 该句话起始unix时间戳>
     * 说话内容
     */
    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @Synchronized
    @Throws(IOException::class, NumberFormatException::class)
    @JvmStatic
    fun readOShareConvertContent(
        filePath: String?,
        convertType: Int?
    ): ArrayList<ConvertContentItem>? {
        filePath ?: return null
        convertType ?: return null
        if (isExternalStorageReadable().not()) return null
        val startTime = System.currentTimeMillis()
        val file = File(filePath)
        DebugUtil.d(TAG, "readOShareConvertContent: " + file.name)
        if (file.exists().not()) return null
        val convertContentItems = ArrayList<ConvertContentItem>()
        val br = BufferedReader(InputStreamReader(FileInputStream(file), StandardCharsets.UTF_8))
        br.use {
            val headers = mutableListOf<String>()
            val hasSupportXunFeinOrByte = convertImplByXunFeinOrByte(convertType)
            kotlin.runCatching {
                while (true) {
                    val line = br.readLine()
                    if (line == null) {
                        DebugUtil.i(TAG, "readOShareConvertContent line is null")
                        break
                    }
                    if (line.isEmpty() || headers.size < COUNT_OSHARE_CONVERT_HEADER_LINE) {
                        if (line.isNotEmpty()) {
                            // 获取文件标题、时间、主题、参会人
                            headers.add(line)
                        }
                        continue
                    }
                    //讲话人 1 该句话起始时间字符串00:01 <讲话人序号 该句话起始unix时间戳>
                    val contents = line.split(KEY_START_FLAG).toTypedArray()
                    // 内容文本
                    val lineNext = br.readLine()
                    if (contents.size > 1 && lineNext.isNotEmpty()) {
                        val item = ConvertContentItem()
                        //讲话人 1 00:00
                        val roleNameAndTime = contents[0].split(" ")
                        // 1 259> 讲话人id与文本时间戳
                        val roleIdAndTime = contents[1].replace(KEY_END_FLAG, "").split(" ")
                        var roleId = 0
                        var textStartTime: Long = 0
                        if (roleIdAndTime.size > 1) {
                            kotlin.runCatching {
                                // 讲话人id
                                roleId = roleIdAndTime[0].toInt()
                                // 文本时间时间戳
                                textStartTime = roleIdAndTime[1].toLong()
                            }.onFailure {
                                DebugUtil.w(
                                    TAG,
                                    "readOShareConvertContent role info read failed:$it"
                                )
                            }
                        }
                        item.roleId = roleId
                        item.startTime = textStartTime
                        if (roleNameAndTime.size > 1) {
                            // 讲话时间
                            val speakTime = roleNameAndTime.last()
                            // 讲话人昵称
                            item.roleName = contents[0].replace(speakTime, "").trim()
                        } else {
                            item.roleName = contents[0]
                        }
                        item.textContent = lineNext
                        genListSubSentence(item, hasSupportXunFeinOrByte)
                        convertContentItems.add(item)
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "readConvertContent exception!" + it.message)
            }
        }
        DebugUtil.d(
            TAG,
            "readConvertContent: size:" + convertContentItems.size + ", time:" + (System.currentTimeMillis() - startTime)
        )
        return convertContentItems
    }

    @JvmStatic
    private fun isExternalStorageReadable(): Boolean {
        val state = Environment.getExternalStorageState()
        return Environment.MEDIA_MOUNTED == state || Environment.MEDIA_MOUNTED_READ_ONLY == state
    }

    @JvmStatic
    private fun convertImplByXunFeinOrByte(convertType: Int?): Boolean {
        if (convertType == ConvertDbUtil.SERVER_PLAN_ASR) {
            return false
        }
        return FunctionOption.hasSupportXunFei() || FunctionOption.hasSupportByte()
    }

    @JvmStatic
    @Suppress("LongMethod")
    private fun genListSubSentence(item: ConvertContentItem, isSupportXunFeiOrByte: Boolean) {
        item.apply {
            if (listSubSentence != null) {
                DebugUtil.e(TAG, "====>genListSubSentence is finished")
                return
            }
            listSubSentence = arrayListOf()
            /*
             * textWithWords == null || textWithWordsTimeStamp == null 此时没有字的时间戳 不支持分句标红和跳转播放，
             * 整段即一句
             *  item.textWithWords?.size != item.textWithWordsTimeStamp?.size 此时分字和时间戳不能一一对应， 兼容逻辑处理
             *  不支持分句 整段即一句
             */
            if (textWithWords == null
                || textWithWordsTimeStamp == null
                || item.textWithWords?.size != item.textWithWordsTimeStamp?.size
                || !item.textContent.equals(combineList(textWithWords!!))
            ) {
                /*
                if (textWithWords == null || textWithWordsTimeStamp == null) {
                    DebugUtil.d(
                        TAG,
                        "====>No textWithWordsTimeStamp, one sentence  is one Paragraph!"
                    )
                } else {
                    DebugUtil.d(
                        TAG,
                        "====>textWithWords.size = ${textWithWords?.size}," +
                                " textWithWordsTimeStamp.size = ${textWithWordsTimeStamp?.size}," +
                                " one sentence  is one Paragraph!"
                    )
                }
                 */

                val sentence = ConvertContentItem.SubSentence(
                    0, textContent.length - 1, item.startTime.toFloat(), textContent
                )
                listSubSentence?.add(sentence)
                return
            }
            val sb = StringBuilder()
            var startChar = 0
            for (i in textWithWords!!.indices) {
                sb.append(textWithWords!![i])
                if (checkIsSpiltFlag(textWithWords!![i]) || i == textWithWords!!.size - 1) {
                    val sentence =
                        ConvertContentItem.SubSentence(
                            startChar,
                            i,
                            //切自研以及字节后，服务端返回时间 timestamp与原有的有差异，单独处理
                            if (isSupportXunFeiOrByte) { item.startTime.toFloat() } else { genTime(item, startChar) },
                            sb.toString()
                        )
                    listSubSentence?.add(sentence)
                    sb.clear()
                    startChar = i + 1
                }
            }
        }
    }

    @JvmStatic
    @VisibleForTesting
    fun combineList(stringList: List<String>): String {
        val sb = StringBuffer()
        for (s in stringList) {
            sb.append(s)
        }
        return sb.toString()
    }

    @JvmStatic
    @VisibleForTesting
    fun checkIsSpiltFlag(inString: String?): Boolean {
        return inString in setOf("。", "?", "!", "？", "！")
    }

    @JvmStatic
    @VisibleForTesting
    fun genTime(item: ConvertContentItem, startChar: Int): Float {
        if (startChar < 0) {
            return item.startTime.toFloat()
        }
        if (startChar > item.textWithWordsTimeStamp!!.size - 1) {
            return item.endTime.toFloat()
        }
        return item.startTime + item.textWithWordsTimeStamp!![startChar].toFloat() * TIME_UNIT
    }

    @Suppress("LongMethod", "TooGenericExceptionCaught")
    @Synchronized
    @Throws(IOException::class, NumberFormatException::class)
    @JvmStatic
    fun readConvertContent(
        appContext: Context,
        filename: String?,
        convertType: Int?
    ): ArrayList<ConvertContentItem>? {
        filename ?: return null
        convertType ?: return null
        if (isExternalStorageReadable().not()) return null
        val startTime = System.currentTimeMillis()
        val file = File(getConvertSavePath(appContext), filename)
        DebugUtil.d(TAG, "readConvertFile: ${file.name}, convertType=$convertType")
        if (!file.exists()) return null
        val convertContentItems = ArrayList<ConvertContentItem>()
        val br = BufferedReader(InputStreamReader(FileInputStream(file), StandardCharsets.UTF_8))
        br.use {
            val hasSupportXunFeinOrByte = convertImplByXunFeinOrByte(convertType)
            kotlin.runCatching {
                while (true) {
                    val line = br.readLine()
                    if (line.isEmpty()) {
                        break
                    }
                    val contents = line.split(SPLIT_FLAG).toTypedArray()
                    if (contents.size < CONTENT_3) {
                        DebugUtil.i(
                            TAG,
                            "contents.length < 3, the text content is empty, read next line, continue. "
                        )
                        continue
                    }
                    val item = ConvertContentItem()
                    val textStartTime = contents[0].toLong() / NumberConstant.NUM_1000
                    val textEndTime = contents[1].toLong() / NumberConstant.NUM_1000
                    val textContent = contents[POS_2]

                    item.startTime = textStartTime
                    item.endTime = textEndTime
                    item.textContent = textContent
                    if (contents.size >= CONTENT_5) {
                        //"H E L "
                        val rawText = contents[POS_3]
                        //"0.81 0.96 1.11 "
                        val timestamp = contents[POS_4]
                        item.textWithWords = rawText.splitOddTrimEnd()

                        // timestamp 被写入 string “null” 需要过滤一下
                        if (timestamp != "null") {
                            item.textWithWordsTimeStamp =
                                timestamp.splitTrimEnd(SPLIT_SPACE)
                        }
                    }
                    if (contents.size >= CONTENT_7) {
                        val stringRoleId = contents[POS_5]
                        val roleName = contents[POS_6]
                        val roleId = stringRoleId.toInt()
                        item.roleId = roleId
                        item.roleName = roleName
                    }
                    genListSubSentence(item, hasSupportXunFeinOrByte)
                    convertContentItems.add(item)
                }
            }.onFailure {
                DebugUtil.e(TAG, "readConvertContent exception! ${it.message}")
            }
        }
        val endTime = System.currentTimeMillis()
        DebugUtil.d(
            TAG,
            "readConvertContent: size:" + convertContentItems.size + ", time:" + (endTime - startTime)
        )
        return convertContentItems
    }

    @JvmStatic
    private fun getConvertSavePath(context: Context): String {
        return context.filesDir.toString() + CONVERT_PATH_FLAG
    }
}