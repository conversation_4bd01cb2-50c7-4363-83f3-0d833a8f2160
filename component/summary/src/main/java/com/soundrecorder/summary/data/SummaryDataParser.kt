/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryDataParser
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.data

import android.content.Context
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.R
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryTrace
import org.json.JSONObject

object SummaryDataParser {

    private const val TAG = "SummaryDataParser"
    private const val TWO = 2
    private const val TASK_SYMBOL = "\n- [ ] "
    private const val TASK_DONE_SYMBOL = "\n- [x] "
    private const val DATA = "data"
    private const val CHOICES = "choices"
    private const val DELTA = "delta"
    private const val CONTENT = "content"

    private const val LAST_SUMMARY = "last_summary"
    private const val SUMMARY_ENTITIES = "summaryEntities"
    private const val EXTEND = "extend"
    private const val THEME = "theme"
    private const val TIME_STAMP = "timeStamp"


    @JvmStatic
    fun parseContentInStream(jsonString: String): String {
        return kotlin.runCatching {
            val json = JSONObject(jsonString)
            val choices = json.getJSONArray(CHOICES)
            choices.getJSONObject(0).getJSONObject(DELTA).getString(CONTENT)
        }.onFailure {
            DebugUtil.e(TAG, "parseContent it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseContentInFinish(jsonString: String): String {
        return kotlin.runCatching {
            JSONObject(jsonString).getString(LAST_SUMMARY)
        }.onFailure {
            DebugUtil.e(TAG, "parseContent it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseTheme(jsonString: String): String {
        return kotlin.runCatching {
            JSONObject(jsonString).getString(THEME)
        }.onFailure {
            DebugUtil.e(TAG, "parseTheme it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseStyle(jsonString: String): Int {
        return kotlin.runCatching {
            JSONObject(jsonString).getJSONObject(DATA).getJSONObject(EXTEND).getInt("theme")
        }.onFailure {
            DebugUtil.e(TAG, "parseStyle it = ${it.message}")
        }.getOrDefault(-1)
    }

    @JvmStatic
    fun parseTime(jsonString: String): Long {
        return kotlin.runCatching {
            JSONObject(jsonString).getLong(TIME_STAMP)
        }.onFailure {
            DebugUtil.e(TAG, "parseTime it = ${it.message}")
        }.getOrDefault(0L)
    }

    @JvmStatic
    fun parseAgent(context: Context, originText: String): List<SummaryAgentEvent> {
        var agentStart: String
        val agentEnd: String
        if (BaseUtil.isEXP()) {
            agentStart = context.getString(R.string.summary_agent_export)
            agentEnd = context.getString(R.string.summary_agent_export_end)
        } else {
            agentStart = context.getString(R.string.summary_agent)
            var index = originText.indexOf(agentStart)
            if (index == -1) {
                agentStart = context.getString(R.string.summary_agent_action)
            }
            index = originText.indexOf(agentStart)
            if (index == -1) {
                agentStart = context.getString(R.string.summary_agent_1)
            }
            agentEnd = context.getString(R.string.summary_agent_end)
        }
        val startIndex = originText.indexOf(agentStart)
        if (startIndex == -1) {
            return emptyList()
        }
        val endIndex = originText.indexOf(agentEnd, startIndex + agentStart.length)
        val subStringB = if (endIndex == -1) {
            originText.substring(startIndex + agentStart.length)
        } else {
            originText.substring(startIndex + agentStart.length, endIndex)
        }
        // 2. 将子字符串 B 去掉开头和结尾，并以换行符为分割点切割成多个子串
        val lines = subStringB.split("\n").filter { it.isNotEmpty() }
        val result = lines.map { line ->
            val agentText =
                line.trimStart { it !in 'a'..'z' && it !in 'A'..'Z' && it !in '\u4e00'..'\u9fa5' }
            SummaryAgentEvent(agentText, false)
        }.toList()
        return result
    }

    @JvmStatic
    fun parseSummaryEntity(jsonString: String): List<SummaryEntity> {
        val entities = mutableListOf<SummaryEntity>()
        kotlin.runCatching {
            val summaryEntitiesJsonArray = JSONObject(jsonString).getJSONArray(SUMMARY_ENTITIES)
            for (i in 0 until summaryEntitiesJsonArray.length()) {
                val entityJson = summaryEntitiesJsonArray.getJSONObject(i)
                val name = entityJson.optString("name", "")
                val turnId = entityJson.optInt("turnId", -1)
                val indexStart = entityJson.optInt("index_start", -1)
                val indexEnd = entityJson.optInt("index_end", -1)
                val timestamp = entityJson.optLong("timestamp", -1)
                val type = entityJson.optString("type", "")
                val summaryIndex = entityJson.optInt("summaryindex", -1)
                entities.add(
                    SummaryEntity(
                        name,
                        turnId,
                        indexStart,
                        indexEnd,
                        timestamp,
                        type,
                        summaryIndex
                    )
                )
            }
        }.onFailure {
            DebugUtil.e(TAG, "parseSummaryEntity e = ${it.message}")
        }
        return removeUselessEntity(entities)
    }

    @JvmStatic
    private fun removeUselessEntity(entities: List<SummaryEntity>): List<SummaryEntity> {
        return entities.filter { (it.name.isEmpty() || it.indexStart == -1 || it.indexEnd == -1 || it.type.isEmpty()).not() }
            .distinctBy { it.indexStart }
            .distinctBy { it.indexEnd }
            .sortedBy { it.indexStart }
    }

    @JvmStatic
    private fun parseOriginText(formatText: String): String {
        // 1. 去除格式为 [`B`](#C) 的字符串
        val removePattern1 = Regex("\\[`.*?`]\\(#.*?\\)")
        val step1 = formatText.replace(removePattern1, "")

        // 2. 将格式为 [D](F) 的字符串替换为 D
        val replacePattern2 = Regex("\\[(.*?)\\]\\(.*?\\)")
        val step2 = step1.replace(replacePattern2) { match ->
            match.groupValues[1]
        }

        // 3. 将格式为 - [] 或 - [x] 的字符串替换为 *
        val replacePattern3 = Regex("\\n - \\[ ?x?\\] ")
        val result = step2.replace(replacePattern3, "\n* ")

        return result
    }

    @JvmStatic
    private fun parseFormatAgent(context: Context, formatText: String): List<SummaryAgentEvent> {
        val pattern = Regex("(\\n - \\[ ?x?\\] .+?)(?=\\n - \\[ ?x?\\] |$)")
        val matches = pattern.findAll(formatText)
        return matches.map { match ->
            val fullMatch = match.groupValues[0].trim()
            val isDone = fullMatch.startsWith(TASK_DONE_SYMBOL)
            val prefix = if (fullMatch.startsWith("\n - [x] ")) TASK_DONE_SYMBOL else TASK_SYMBOL
            val content = fullMatch.substring(prefix.length)
            SummaryAgentEvent(content, isDone)
        }.toList()
    }

    /**
     * 从格式化后的文本获取实体
     */
    @JvmStatic
    private fun parseFormatSummaryEntity(formatText: String): List<SummaryEntity> {
        val pattern = Regex("\\[.*?]\\((.*?)\\)")
        val matches = pattern.findAll(formatText)
        return matches.map {
            GsonUtil.getGson().fromJson(it.groupValues[1], SummaryEntity::class.java)
        }.toList()
    }

    @JvmStatic
    private fun parseFormatSummaryTrace(formatText: String): List<SummaryTrace> {
        val pattern = Regex("\\[`.*?`]\\(#(.*?)\\)")
        val matches = pattern.findAll(formatText)

        return matches.map {
            GsonUtil.getGson().fromJson(it.groupValues[1], SummaryTrace::class.java)
        }.toList()
    }

    @JvmStatic
    fun replaceEntity(content: String): String {
        val regex = Regex("\\[(.*?)\\]\\((.*?)\\)")
        return regex.replace(content) { matchResult ->
            matchResult.groupValues[1]
        }
    }
}