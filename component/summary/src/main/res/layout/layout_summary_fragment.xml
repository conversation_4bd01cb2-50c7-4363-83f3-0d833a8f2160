<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.SummaryFragment">

    <com.soundrecorder.summary.ui.content.SummaryContentView
        android:id="@+id/summary_content_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/stop_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/stop_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp96"
        android:visibility="gone"
        android:gravity="center_horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/summary_content_view">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/summary_stop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp24"
            android:background="@drawable/bg_image_icon"
            android:src="@drawable/ic_stop_icon" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>