<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <group>
    <clip-path
        android:pathData="M10,0L30,0A10,10 0,0 1,40 10L40,30A10,10 0,0 1,30 40L10,40A10,10 0,0 1,0 30L0,10A10,10 0,0 1,10 0z"/>
    <path
        android:pathData="M10,0L30,0A10,10 0,0 1,40 10L40,30A10,10 0,0 1,30 40L10,40A10,10 0,0 1,0 30L0,10A10,10 0,0 1,10 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="0"
            android:startY="0"
            android:endX="40"
            android:endY="40"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFCF4B"/>
          <item android:offset="1" android:color="#FFFFB200"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M33.61,31.917H6.277V31.417H33.61V31.917ZM33.61,24.25H6.277V23.75H33.61V24.25ZM33.61,16.583H6.277V16.083H33.61V16.583ZM33.61,8.917H6.277V8.417H33.61V8.917Z"
        android:fillColor="#97360B"
        android:fillAlpha="0.15"/>
    <path
        android:pathData="M119.67,-81.879L127.442,-74.106L30.487,22.849C30.377,22.959 30.254,23.056 30.122,23.138L19.064,29.951L18.753,29.64L15.114,31.451C14.475,31.769 13.797,31.092 14.116,30.452L15.926,26.813L15.619,26.506L22.426,15.442C22.507,15.309 22.604,15.187 22.715,15.076L119.67,-81.879Z"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14.034"
            android:startY="30.148"
            android:endX="53.051"
            android:endY="-8.868"
            android:type="linear">
          <item android:offset="0" android:color="#4C97360B"/>
          <item android:offset="0.729" android:color="#0C97360B"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M122.464,-74.594L114.644,-82.414L17.228,15.002C17.116,15.114 17.06,15.17 17.009,15.229C16.963,15.283 16.921,15.338 16.881,15.396C16.836,15.461 16.797,15.529 16.718,15.666L10.22,26.896L13.158,29.834L24.385,23.332C24.521,23.253 24.589,23.213 24.654,23.169C24.711,23.129 24.767,23.086 24.82,23.041C24.88,22.99 24.935,22.934 25.047,22.823L122.464,-74.594Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="28.599"
            android:startY="9.574"
            android:endX="33.475"
            android:endY="14.45"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFEFDE"/>
          <item android:offset="1" android:color="#FFFDCB69"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M24.904,22.964L17.084,15.144C16.978,15.252 16.885,15.372 16.805,15.5L10.22,26.895L13.159,29.833L24.549,23.243C24.677,23.163 24.796,23.07 24.904,22.964Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="26.518"
            android:startY="24.579"
            android:endX="17.084"
            android:endY="15.145"
            android:type="linear">
          <item android:offset="0" android:color="#2697360B"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M12.799,29.463L10.75,30.373C10.127,30.649 9.815,30.788 9.624,30.728C9.459,30.676 9.33,30.547 9.278,30.382C9.218,30.191 9.356,29.88 9.633,29.256L10.542,27.206L12.799,29.463Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="13.18"
            android:startY="29.622"
            android:endX="10.378"
            android:endY="26.82"
            android:type="linear">
          <item android:offset="0" android:color="#FF654B15"/>
          <item android:offset="1" android:color="#FF9B731F"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
