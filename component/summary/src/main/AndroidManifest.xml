<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <queries>
        <package android:name="com.coloros.accessibilityassistant" />
        <package android:name="com.coloros.translate.engine" />
        <package android:name="com.coloros.note" />
        <package android:name="com.oneplus.note" />
    </queries>

    <uses-permission android:name="com.oplus.permission.safe.PHONE"/>
    <!--语音转文字所需权限-->
    <uses-permission android:name="com.oplus.permission.safe.AI_APP" />
    <!--AI UNIT 所需权限-->
    <uses-permission android:name="com.oplus.permission.safe.AUTHENTICATE" />
    <!--跳转便签摘要详情所需-->
    <uses-permission android:name="com.oplus.permission.safe.PROTECT"/>

    <application>
        <meta-data
            android:name="record_summary_support"
            android:value="1" />
        <!--放到translate module中-->
       <!-- &lt;!&ndash; AIUnit的鉴权标识符 &ndash;&gt;
        <meta-data
            android:name="com.oplus.aiunit.auth_style"
            android:value="0" />
        &lt;!&ndash; AIUnit中要接入下面两个下载metaData，代表接入的新流程，支持通过插件下载，和前前确认内销预置也没问题 &ndash;&gt;
        <meta-data
            android:name="aiunit_download_enable"
            android:value="true" />
        <meta-data
            android:name="aiunit_download_group"
            android:value="cloud_call_summary" />-->
        <activity
            android:name="com.soundrecorder.summary.ui.DurationWarnActivity"
            android:configChanges="locale|orientation|keyboardHidden|mcc|mnc|density"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="behind"
            android:uiOptions="splitActionBarWhenNarrow"
            android:theme="@style/TransparentActivityTheme" />

        <service
            android:name=".request.AISummaryService"
            android:exported="false" />

    </application>
</manifest>