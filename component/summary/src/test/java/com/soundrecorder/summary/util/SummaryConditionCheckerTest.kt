/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryConditionCheckerTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/18
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/18 1.0 create
 */

package com.soundrecorder.summary.util

import android.content.Context
import android.os.Build
import android.os.Bundle
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.aiunit.core.AIUnit
import com.oplus.aiunit.core.data.DetectData
import com.oplus.aiunit.toolkits.AISettings
import com.soundrecorder.base.utils.MetaDataUtils
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE_WARN
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_SMALL
import com.soundrecorder.modulerouter.summary.ERROR_CODE_FORMAT
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SIZE_LARGE
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUCCESS
import com.soundrecorder.summary.shadow.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SummaryConditionCheckerTest {
    var context: Context? = null
    var mockStaticAIUnit: MockedStatic<AIUnit>? = null
    var mockStaticAISetting: MockedStatic<AISettings>? = null
    var mockStaticMetaDataUtil: MockedStatic<MetaDataUtils>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mockStaticAIUnit = Mockito.mockStatic(AIUnit::class.java)
        mockStaticAISetting = Mockito.mockStatic(AISettings::class.java)
        mockStaticMetaDataUtil = Mockito.mockStatic(MetaDataUtils::class.java)
    }

    @After
    fun tearDown() {
        context = null
        mockStaticAIUnit?.close()
        mockStaticAIUnit = null
        mockStaticAISetting?.close()
        mockStaticAISetting = null
        mockStaticMetaDataUtil?.close()
        mockStaticMetaDataUtil = null
    }

    @Test
    fun should_correct_when_checkSummaryPreCondition() {
        val context = context ?: return
        val record = Record()
        record.mimeType = "m"
        Assert.assertEquals(ERROR_CODE_FORMAT, SummaryConditionChecker.checkSummaryPreCondition(context, record))

        record.mimeType = RecordConstant.MIMETYPE_MP3
        Assert.assertEquals(ERROR_CODE_DURATION_SMALL, SummaryConditionChecker.checkSummaryPreCondition(context, record))

        record.duration = 100
        Assert.assertEquals(ERROR_CODE_DURATION_SMALL, SummaryConditionChecker.checkSummaryPreCondition(context, record))

        record.duration = 10 * 60 * 1000
        record.fileSize = 600 * 1024 * 1024
        Assert.assertEquals(ERROR_CODE_SIZE_LARGE, SummaryConditionChecker.checkSummaryPreCondition(context, record))

        record.fileSize = 100 * 1024 * 1024
        record.duration = 6 * 60 * 60 * 1000
        Assert.assertEquals(ERROR_CODE_DURATION_LARGE, SummaryConditionChecker.checkSummaryPreCondition(context, record))

        record.duration = 3 * 60 * 60 * 1000
        Assert.assertEquals(ERROR_CODE_DURATION_LARGE_WARN, SummaryConditionChecker.checkSummaryPreCondition(context, record))

        record.duration = 1 * 60 * 60 * 1000
        Assert.assertEquals(ERROR_CODE_SUCCESS, SummaryConditionChecker.checkSummaryPreCondition(context, record))
    }

    @Ignore
    @Test
    fun should_correct_when_isAIUnitSupportSummary() {
        val context = context ?: return
        mockStaticAIUnit?.`when`<Boolean> { AIUnit.isDeviceSupported(context) }?.thenReturn(false)
        Assert.assertFalse(SummaryConditionChecker.isAIUnitSupportSummary(context))

        mockStaticAIUnit?.`when`<Boolean> { AIUnit.isDeviceSupported(context) }?.thenReturn(true)
        mockStaticAISetting?.`when`<Boolean> { AISettings.isGlobalDepSupported(context) }?.thenReturn(false, true)
        Assert.assertFalse(SummaryConditionChecker.isAIUnitSupportSummary(context))
        Assert.assertTrue(SummaryConditionChecker.isAIUnitSupportSummary(context))
    }

    @Ignore
    @Test
    fun should_correct_when_isAiUnitAvailable() {
        val context = context ?: return
        val mockDetectData = Mockito.mock(DetectData::class.java)
        Mockito.`when`(mockDetectData.isAvailable).thenReturn(true)
        Mockito.`when`(mockDetectData.state).thenReturn(0)
        mockStaticAIUnit?.`when`<DetectData> { AIUnit.getDetectData(context, anyString(), any<Bundle>()) }
            ?.thenReturn(mockDetectData)
        Assert.assertFalse(SummaryConditionChecker.isAiUnitAvailable(context))

        Mockito.`when`(mockDetectData.state).thenReturn(0, 1, 1, 1)
        Assert.assertFalse(SummaryConditionChecker.isAiUnitAvailable(context))
        Assert.assertTrue(SummaryConditionChecker.isAiUnitAvailable(context))
    }

    @Ignore
    @Test
    fun should_correct_when_isAssistantSupportSummary() {
        val context = context ?: return
        mockStaticMetaDataUtil?.`when`<Boolean> { MetaDataUtils.getMetaDataBoolean(context, anyString(), anyString()) }
            ?.thenReturn(false, true)

        Assert.assertFalse(SummaryConditionChecker.isAssistantSupportSummary(context))
        Assert.assertTrue(SummaryConditionChecker.isAssistantSupportSummary(context))
    }

    @Ignore
    @Test
    fun should_correct_when_isTranslateSupportSummary() {
        val context = context ?: return
        mockStaticMetaDataUtil?.`when`<Boolean> { MetaDataUtils.getMetaDataBoolean(context, anyString(), anyString()) }
            ?.thenReturn(false, true)

        Assert.assertFalse(SummaryConditionChecker.isTranslateSupportSummary(context))
        Assert.assertTrue(SummaryConditionChecker.isTranslateSupportSummary(context))
    }

    @Ignore
    @Test
    fun should_correct_when_isNotesSupportSummary() {
        val context = context ?: return
        mockStaticMetaDataUtil?.`when`<Int> { MetaDataUtils.getMetaDataInt(any(), anyString(), anyString()) }
            ?.thenReturn(1, 3)

        Assert.assertFalse(SummaryConditionChecker.isNotesSupportSummary(context))
        Assert.assertTrue(SummaryConditionChecker.isNotesSupportSummary(context))
    }

    @Test
    fun should_correct_when_isFileFormatMet() {
        Assert.assertTrue(SummaryConditionChecker.isFileFormatMet(RecordConstant.MIMETYPE_MP3))
        Assert.assertTrue(SummaryConditionChecker.isFileFormatMet(RecordConstant.MIMETYPE_3GPP))
        Assert.assertTrue(SummaryConditionChecker.isFileFormatMet(RecordConstant.MIMETYPE_AMR))
        Assert.assertTrue(SummaryConditionChecker.isFileFormatMet(RecordConstant.MIMETYPE_AMR_WB))
        Assert.assertTrue(SummaryConditionChecker.isFileFormatMet(RecordConstant.MIMETYPE_WAV))
        Assert.assertTrue(SummaryConditionChecker.isFileFormatMet(RecordConstant.MIMETYPE_ACC))
        Assert.assertTrue(SummaryConditionChecker.isFileFormatMet(RecordConstant.MIMETYPE_ACC_ADTS))
        Assert.assertFalse(SummaryConditionChecker.isFileFormatMet(""))
    }

    @Test
    fun should_correct_when_isFileSizeMaxMet() {
        Assert.assertTrue(SummaryConditionChecker.isFileSizeMaxMet(10))
    }

    @Test
    fun should_correct_when_isFileDurationMinMet() {
        Assert.assertTrue(SummaryConditionChecker.isFileDurationMinMet(3 * 60 * 1000))
    }
    @Test
    fun should_correct_when_isFileDurationMaxWarnMet() {
        Assert.assertTrue(SummaryConditionChecker.isFileDurationMaxWarnMet(6000))
    }

    @Test
    fun should_correct_when_isFileDurationMaxMet() {
        Assert.assertTrue(SummaryConditionChecker.isFileDurationMaxMet(6000))
    }
}