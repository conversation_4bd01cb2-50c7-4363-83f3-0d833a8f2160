/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryLrcFileUtilTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/18 1.0 create
 */

package com.soundrecorder.summary.util

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.Sentence
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.summary.shadow.ShadowFeatureOption
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SummaryLrcFileUtilTest {

    @Ignore
    @Test
    fun should_notNull_when_createLrcFile() {
        Assert.assertNotNull(SummaryLrcFileUtil.createLrcFile(Record().apply { relativePath = "/standard" }, null, null))
        Assert.assertNotNull(SummaryLrcFileUtil.createLrcFile(Record(), null, listOf(MarkDataBean(1))))
    }

    @Test
    fun should_equals_when_calRecordSummaryType() {
        Assert.assertEquals(1, SummaryLrcFileUtil.calRecordSummaryType(RecordModeConstant.RELATIVE_PATH_CALL_NO_SLASH))
        Assert.assertEquals(6, SummaryLrcFileUtil.calRecordSummaryType(""))
    }

    @Test
    fun should_notNull_when_genLrcFileJsonContent() {
        Assert.assertNotNull(SummaryLrcFileUtil.genLrcFileJsonContent(1, arrayListOf(Sentence()), arrayListOf(MarkDataBean(1))))
        Assert.assertNotNull(SummaryLrcFileUtil.genLrcFileJsonContent(1, arrayListOf(Sentence()), arrayListOf(MarkDataBean(1, 2))))
    }
}