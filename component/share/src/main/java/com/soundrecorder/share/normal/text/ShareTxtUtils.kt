/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.share.normal.text

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import android.view.View
import androidx.core.content.FileProvider
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.flexible.FollowHandDialogUtils
import com.soundrecorder.common.share.ShareTypeText
import com.soundrecorder.modulerouter.share.ShareType
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException

object ShareTxtUtils {
    private const val TAG = "ShareUtils"
    private const val DIR_TRANSFER = "file"
    private const val TXT_MIMETYPE = "text/plain"

    @JvmStatic
    fun getShareTextFile(
        activity: Activity,
        filename: String,
        contentString: String?,
        type: ShareType? = null
    ): Uri? {
        if (contentString.isNullOrEmpty()) return null
        DebugUtil.i(TAG, " filename $filename")
        //write file
        var txtFile = File(filename)
        if (type is ShareTypeText) {
            txtFile = genTextFile(activity, filename) ?: return null
        }
        var fileOutputStream: FileOutputStream? = null
        try {
            fileOutputStream = FileOutputStream(txtFile)
            fileOutputStream.write(contentString.toByteArray())
            fileOutputStream.close()
        } catch (exception: FileNotFoundException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
        } catch (exception: IOException) {
            DebugUtil.e(TAG, "fileOutputStream exception > ${exception.message}")
        } finally {
            try {
                fileOutputStream?.close()
            } catch (exception: IOException) {
                DebugUtil.e(TAG, "close fileOutputStream exception > ${exception.message}")
            }
        }
        return getFileProviderUri(activity, txtFile)
    }

    @JvmStatic
    private fun getFileProviderUri(activity: Activity?, txtFile: File): Uri? {
        if (activity == null) return null
        return FileProvider.getUriForFile(activity, activity.packageName + ".fileProvider", txtFile)
    }

    @JvmStatic
    private fun genTextFile(activity: Activity?, filename: String): File? {
        if (activity == null) return null
        var file = File(activity.filesDir, DIR_TRANSFER)
        if (file.exists()) {
            val listFiles: Array<File>? = file.listFiles()
            if (listFiles != null) {
                for (item in listFiles) {
                    val result = item.delete()
                    if (!result) {
                        DebugUtil.e(
                            TAG,
                            FileUtils.getDisplayNameByPath(item.absolutePath) + " delete share error."
                        )
                    }
                }
            }
        }
        val isDirExists = file.exists() || file.mkdirs()
        if (isDirExists && !TextUtils.isEmpty(filename)) {
            file = File(file, filename)
            DebugUtil.i(
                TAG,
                "genShareTextFile:" + FileUtils.getDisplayNameByPath(file.absolutePath)
            )
            return file
        }
        return null
    }

    @JvmStatic
    fun doExport(activity: Activity, anchor: View?, uri: Uri) {
        val intent = Intent(Intent.ACTION_SEND)
        intent.putExtra(Intent.EXTRA_STREAM, uri)
        intent.type = TXT_MIMETYPE
        FollowHandDialogUtils.addShareDialogAnchor(anchor, intent)
        activity.startActivity(
            Intent.createChooser(
                intent,
                activity.getString(com.soundrecorder.common.R.string.send)
            )
        )
    }
}