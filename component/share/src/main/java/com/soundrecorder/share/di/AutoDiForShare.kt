/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AutoDiForShare
 ** Description : AutoDiForShare
 ** Version     : 1.0
 ** Date        : 2025/06/06
 ** Author      : renjiahao
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  renjiahao       2025/06/06       1.0      create
 ***********************************************************************/
package com.soundrecorder.share.di

import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.share.api.ShareApi
import org.koin.dsl.module

object AutoDiForShare {
    val shareModule = module {
        single<ShareAction>(createdAtStart = true) {
            ShareApi
        }
    }
}