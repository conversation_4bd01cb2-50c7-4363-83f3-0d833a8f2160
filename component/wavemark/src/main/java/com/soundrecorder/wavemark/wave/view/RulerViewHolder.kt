/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RulerViewHolder
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave.view

import android.text.TextUtils
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.wavemark.wave.view.WaveItemView.MarkOnClickListener

class RulerViewHolder<T : WaveItemView>(
    private val adapter: IMarkTimeAdapter,
    val waveView: T
) : RecyclerView.ViewHolder(waveView), MarkOnClickListener {

    private val photoViewerApi by lazy {
        Injector.injectFactory<PhotoViewerInterface>()
    }

    init {
        waveView.setMarkOnClickListener(this)
    }

    override fun onClick(mark: MarkDataBean) {
        if (!TextUtils.isEmpty(mark.pictureFilePath)) {
            val pictures: MutableList<MarkDataBean> = ArrayList()
            adapter.markDataList?.forEach { markDataBean: MarkDataBean ->
                if (markDataBean.fileExists()) {
                    pictures.add(markDataBean)
                }
            }
            val index = pictures.indexOf(mark)
            photoViewerApi?.startWithBigImage(waveView.context, pictures, index, null, null)
        }
    }
}

interface IMarkTimeAdapter {
    val markDataList: List<MarkDataBean>?
}