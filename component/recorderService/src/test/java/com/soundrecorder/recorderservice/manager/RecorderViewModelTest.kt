package com.soundrecorder.recorderservice.manager

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecorderViewModelTest {
    @Test
    fun should_correct_when_checkMarkDataDuplicated() {
        Assert.assertFalse(RecorderViewModel.getInstance().checkMarkDataDuplicated())
    }

    @Test
    fun should_correct_when_isRecordSaving() {
        RecorderViewModel.getInstance().updateLoadingCallback(null)
        Assert.assertTrue(RecorderViewModel.getInstance().isRecordSaving())

        RecorderViewModel.getInstance().onServiceDestroy()
        Assert.assertFalse(RecorderViewModel.getInstance().isRecordSaving())
    }
}