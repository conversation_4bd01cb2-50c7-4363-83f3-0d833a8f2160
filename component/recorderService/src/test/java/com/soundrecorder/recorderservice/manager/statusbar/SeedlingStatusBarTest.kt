/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingStatusBarTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context
import android.os.Build
import androidx.lifecycle.LiveData
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusCompactUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusCompactUtil::class]
)
class SeedlingStatusBarTest {

    private var ctx: Context? = null
    private var recorderViewModelApi: MockedStatic<RecorderViewModelApi>? = null

     @Before
     fun setUp() {
         ctx = ApplicationProvider.getApplicationContext()
//         seedlingActionStatic = Mockito.mockStatic(SeedlingAction::class.java)
         recorderViewModelApi = Mockito.mockStatic(RecorderViewModelApi::class.java)
     }

     @After
     fun reset() {
         ctx = null
        /* seedlingActionStatic?.close()
         seedlingActionStatic = null*/
         recorderViewModelApi?.close()
         recorderViewModelApi = null
     }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)

    @Test
    @Ignore
    fun should_not_null_when_createSeedlingDataInfo() {
        Mockito.`when`(RecorderViewModelApi.getAmplitudeCurrentTime()).thenReturn(2000)
        val data = SeedlingStatusBar.createSeedlingDataInfo()
        Assert.assertEquals(2000, data.curTime)
    }

    @Test
    @Ignore
    fun should_equals_when_getSeedlingData() {
        val firstData = SeedlingDataInfo(curTime = 3000)
        val firstSeedlingData = SeedlingStatusBar.getSeedlingData(firstData)
        Assert.assertEquals(firstData, Whitebox.getInternalState(SeedlingStatusBar::class.java, "lastSeedlingDataInfo"))

        val secondData = SeedlingDataInfo(curTime = 3500)
        val secondSeedlingData = SeedlingStatusBar.getSeedlingData(secondData)
        Assert.assertEquals(firstSeedlingData.toString(), secondSeedlingData.toString())
    }

    @Test
    @Ignore
    fun should_equals_when_show() {
        val ctx = this.ctx ?: return
        SeedlingStatusBar.show(ctx, true, "test")
        Assert.assertTrue(Whitebox.getInternalState(SeedlingStatusBar::class.java, "isShowing"))
    }

    @Test
    @Ignore
    fun should_equals_when_forceDismiss() {
        val ctx = this.ctx ?: return
        SeedlingStatusBar.dismiss(ctx, true, "test")
        Assert.assertFalse(Whitebox.getInternalState(SeedlingStatusBar::class.java, "isShowing"))
    }

    @Test
    @Ignore
    fun should_equals_when_register() {
        SeedlingStatusBar.register()
        val screenLiveData = Whitebox.getInternalState<LiveData<Boolean>>(SeedlingStatusBar::class.java, "screenStateLiveData")
        Assert.assertTrue(screenLiveData.hasObservers())
    }

    @Test
    @Ignore
    fun should_equals_when_unRegister() {
        SeedlingStatusBar.unRegister()
        Assert.assertNull(Whitebox.getInternalState(SeedlingStatusBar::class.java, "lastSeedlingDataInfo"))
    }
}