/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingDataInfo
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.TAG
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.modulerouter.recorder.INIT
import com.soundrecorder.modulerouter.recorder.PAUSED
import com.soundrecorder.modulerouter.recorder.RECORDING
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.recorder.SaveFileState.ERROR
import com.soundrecorder.modulerouter.recorder.SaveFileState.SHOW_LOADING_DIALOG
import com.soundrecorder.modulerouter.recorder.SaveFileState.START_LOADING
import com.soundrecorder.modulerouter.recorder.SaveFileState.SUCCESS
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import com.soundrecorder.recorderservice.manager.statusbar.SeedlingSavingTimer.Companion.processValue
import org.json.JSONObject

data class SeedlingDataInfo(
    var curTime: Long = -1,
    var curStatus: Int = -1,
    var curMarkTime: Long = -1,
    var markEnable: Boolean? = null,
    var recordEnable: Boolean? = null,
    var saveFileState: Int? = -1,
    var fileName: String? = null
) {
    companion object {
        private const val ENABLE_START = 0
        private const val ENABLE_PAUSE = 1
        private const val DISABLE_PAUSE = 2
        private const val DISABLE_START = 3

        private const val RECORD_CONSTANTS_0 = 0
        private const val RECORD_CONSTANTS_1 = 1
        private const val RECORD_CONSTANTS_2 = 2
        private const val RECORD_CONSTANTS_3 = 3
        private const val RECORD_CONSTANTS_4 = 4

        private const val SAVING_DEFAULT_PROCESS = -1
        private const val SAVE_COMPLEDED_PROCESS = 100

        private const val PACKAGE_SYSTEM_UI_PLUGIN = "com.oplus.systemui.plugins"
        private const val SYSTEM_UI_ENTRANCE_BG_COLOR = "entranceBgColor"

        const val KEY_CUR_TIME_TEXT = "timeText"
        const val KEY_CUR_TIME_DESC = "timeDescription"
        const val KEY_CUR_MARK_TIME = "labelText"
        const val KEY_CUR_MARK_TIME_DESC = "labelDescription"
        const val KEY_MARK_BTN_DISABLE = "labelDisable"//true或false
        const val KEY_MARK_BTN_DESC = "labelBtnDescription"
        const val KEY_RECORD_BTN_DESC = "recordBtnDescription"
        const val KEY_RECORD_BTN_DISABLE = "recordDisable"
        const val KEY_RECORD_BTN_IMAGE_ON = "recordImageOn"
        const val KEY_RECORD_BTN_IMAGE_OFF = "recordImageOff"
        const val KEY_LOGO_DESC = "logoDescription"
        const val KEY_LOTTIE_LOGO_PLAY = "recordInProgress"//true或false
        const val KEY_RECORD_TRANSITION_ON = "transitionOn"
        const val KEY_RECORD_TRANSITION_OFF = "transitionOff"
        const val KEY_RECORD_BUTTON_BG_COLOR = "recordBtnBgColor"
        const val KEY_MARK_BUTTON_ICON_COLOR = "markBtnIconColor"
        const val KEY_SHOW_RECORD_VIEW = "showG1"
        const val KEY_SHOW_PROCESS_PERCENT = "processPercent"
        const val KEY_SHOW_DEFAULT_SAVE_TIME = "isShowDefaultSavingTime"
        const val KEY_SAVE_RECORD_FROM_WHERE = "saveRecordFromWhere"
        const val KEY_SHOW_SAVED_LOTTIE = "showSavedLottie"
        const val KEY_SAVE_BTN_TEXT = "saveBtn"
        const val KEY_SAVE_LOTTIE = "saveLottie"
        const val KEY_SMALL_CARD_SAVE_LOTTIE = "saveSmallCardLottie"
        const val KEY_SAVE_CONTENT = "saveContent"
        const val KEY_SAVE_TITLE = "saveTitle"
        const val KEY_CARD_CLICK_TYPE = "cardClickType"
        const val KEY_FILE_NAME = "fileName"
        const val KEY_DAY_TEXT = "dayText"
        const val KEY_HOUR_TEXT = "hourText"
        const val KEY_MINUTE_TEXT = "minuteText"
        const val KEY_SECOND_TEXT = "secondText"
        const val KEY_DAY_SEPARATOR = "daySeparator"
        const val KEY_HOUR_SEPARATOR = "hourSeparator"
        const val KEY_MINUTE_SEPARATOR = "minuteSeparator"
        const val KEY_SAVE_FILE_NAME = "fileName2"
        const val KEY_SAVE_TITLE_2 = "saveTitle2"
        const val KEY_CARD_BG = "cardBg"

        const val KEY_RECORD_BTN_STATE_ICON = "recordStateIcon"

        private const val IMAGE_RECORD_BTN_PLAY_ENABLE = "\$r('images.recording')"
        private const val IMAGE_RECORD_BTN_PLAY_DISABLE = "\$r('images.disable_recording')"
        private const val IMAGE_RECORD_BTN_PAUSE_ENABLE = "\$r('images.paused')"
        private const val IMAGE_RECORD_BTN_PAUSE_DISABLE = "\$r('images.disable_paused')"
        private const val TRANSITION_START_TO_PAUSE = "\$r('images.lottie_start')"
        private const val TRANSITION_PAUSE_TO_START = "\$r('images.lottie_pause')"
        private const val TRANSITION_START_TO_DISABLE = "\$r('images.lottie_start_to_disable')"
        private const val TRANSITION_DISABLE_TO_START = "\$r('images.lottie_disable_to_start')"
        private const val TRANSITION_PAUSE_TO_DISABLE = "\$r('images.lottie_pause_to_disable')"
        private const val TRANSITION_DISABLE_TO_PAUSE = "\$r('images.lottie_disable_to_pause')"
        private const val BUTTON_BG_COLOR = "\$r('colors.recorde_button')"
        private const val BUTTON_BG_COLOR_DISABLE = "\$r('colors.recorde_button_disable')"
        private const val MARK_BTN_ICON_COLOR = "\$r('colors.mark_icon_color')"
        private const val MARK_BTN_ICON_COLOR_DISABLE = "\$r('colors.mark_disable_icon_color')"
        private const val CARD_BG_COLOR = "\$r('colors.card_bg_color')"
        private const val CARD_ENTRANCE_BG_COLOR = "\$r('colors.card_entrance_bg_color')"

        private const val LOTTIE_SAVING = "\$r('images.lottie_saving')"
        private const val LOTTIE_SAVE_FAIL = "\$r('images.lottie_save_fail')"
        private const val LOTTIE_SAVE_SUCCESS = "\$r('images.lottie_save_success')"
        private const val KEY_CARD_CLICK_TYPE_DATE = "deeplink"

        const val SMALL_CARD_SAVING = "small_card_saving"
        const val SMALL_CARD_SAVE_SUCCESS = "small_card_save_success"
        const val SMALL_CARD_SAVING_FAIL = "small_card_saving_fail"

        /**
         * 是否有systemui新框架底板
         */
        val isEntranceBg by lazy {
            AppUtil.metaDataEquals(
                PACKAGE_SYSTEM_UI_PLUGIN,
                SYSTEM_UI_ENTRANCE_BG_COLOR
            )
        }
    }
    /**时间数组，*/
    private var curTimeInfo: Array<String>? = null
    private var curMarkInfo: Array<String>? = null
    private var curMarkBtnInfo: Array<String>? = null
    private var curRecordBtnInfo: Array<String>? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    /**
     * 计算时间信息
     */

    @VisibleForTesting
    fun getCurTimeInfo(forceUpdate: Boolean = false): Array<String>? {
        if (curTime >= 0 && (forceUpdate || curTimeInfo.isNullOrEmpty())) {
            curTimeInfo = arrayOf(
                TimeUtils.getFormatTimeExclusiveMill(curTime),
                TimeUtils.getDurationHint(BaseApplication.getAppContext(), curTime)
            )
        }

        return curTimeInfo
    }

    /**
     * 计算标记时间信息
     */
    @VisibleForTesting
    fun getCurLabelInfo(forceUpdate: Boolean = false): Array<String>? {
        if (curMarkTime > 0 && (forceUpdate || curMarkInfo.isNullOrEmpty())) {
            curMarkInfo = arrayOf(
                BaseApplication.getAppContext().getString(
                    com.soundrecorder.common.R.string.notification_marked,
                    TimeUtils.getFormatTimeExclusiveMill(curMarkTime)
                ),
                BaseApplication.getAppContext().getString(
                    com.soundrecorder.common.R.string.notification_marked,
                    TimeUtils.getFormatContentDescriptionTimeByMillisecond(
                        BaseApplication.getAppContext(),
                        curMarkTime
                    )
                )
            )
        }

        return curMarkInfo
    }

    @VisibleForTesting
    fun getMarkBtnInfo(forceUpdate: Boolean = false): Array<String>? {
        if (forceUpdate || curMarkBtnInfo.isNullOrEmpty()) {
            curMarkBtnInfo = arrayOf(
                if (markEnable != false) MARK_BTN_ICON_COLOR else MARK_BTN_ICON_COLOR_DISABLE,
                BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.record_mark)
            )
        }

        return curMarkBtnInfo
    }

    @VisibleForTesting
    fun getRecordBtnInfo(forceUpdate: Boolean = false): Array<String>? {
        if (forceUpdate || curRecordBtnInfo.isNullOrEmpty()) {
            val isPlaying = curStatus == RECORDING
            val recordBtnDescription = if (isPlaying) {
                BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.recording_notify_talk_back)
            } else {
                BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.record_pause_tips)
            }
            curRecordBtnInfo = arrayOf(
                getRecordBtnStateIcon(isPlaying),
                if (recordEnable != false) IMAGE_RECORD_BTN_PLAY_ENABLE else IMAGE_RECORD_BTN_PLAY_DISABLE,
                if (recordEnable != false) IMAGE_RECORD_BTN_PAUSE_ENABLE else IMAGE_RECORD_BTN_PAUSE_DISABLE,
                BUTTON_BG_COLOR,
                recordBtnDescription
            )
        }

        return curRecordBtnInfo
    }

    private fun getRecordBtnStateIcon(isPlaying: Boolean): String {
        return if (isPlaying) {
            if (recordEnable != false) IMAGE_RECORD_BTN_PLAY_ENABLE else IMAGE_RECORD_BTN_PLAY_DISABLE
        } else {
            if (recordEnable != false) IMAGE_RECORD_BTN_PAUSE_ENABLE else IMAGE_RECORD_BTN_PAUSE_DISABLE
        }
    }

    fun getChangedDataSimple(): JSONObject {
        val jsonObject = JSONObject()
        jsonObject.put(KEY_CUR_TIME_TEXT, getCurTimeInfo()?.getOrNull(0))
        jsonObject.put(KEY_CUR_TIME_DESC, getCurTimeInfo()?.getOrNull(1))

        jsonObject.put(KEY_LOGO_DESC, BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.app_name_main))

        return jsonObject
    }

    fun getChangedData(showMarkText: Boolean = false, lastSeedlingDataInfo: SeedlingDataInfo? = null): JSONObject {
        val jsonObject = getChangedDataSimple()

        //是否有新框架底板
        if (isEntranceBg) {
            jsonObject.put(KEY_CARD_BG, CARD_ENTRANCE_BG_COLOR)
        } else {
            jsonObject.put(KEY_CARD_BG, CARD_BG_COLOR)
        }

        //为响应toast，假置灰
        jsonObject.put(KEY_MARK_BTN_DISABLE, false)
        getMarkBtnInfo(true)?.let {
            jsonObject.put(KEY_MARK_BUTTON_ICON_COLOR, it.getOrNull(0))
            jsonObject.put(KEY_MARK_BTN_DESC, it.getOrNull(1))
        }

        //为响应toast，假置灰
        jsonObject.put(KEY_RECORD_BTN_DISABLE, false)
        getRecordBtnInfo(true)?.let {
            jsonObject.put(KEY_RECORD_BTN_STATE_ICON, it.getOrNull(RECORD_CONSTANTS_0))

            jsonObject.put(KEY_RECORD_BTN_IMAGE_ON, it.getOrNull(RECORD_CONSTANTS_1))
            jsonObject.put(KEY_RECORD_BTN_IMAGE_OFF, it.getOrNull(RECORD_CONSTANTS_2))
            jsonObject.put(KEY_RECORD_BUTTON_BG_COLOR, it.getOrNull(RECORD_CONSTANTS_3))
            jsonObject.put(KEY_RECORD_BTN_DESC, it.getOrNull(RECORD_CONSTANTS_4))
        }

        //获取卡片按钮的json动效资源
        getChangeDataRecorderBtnJsonAnimation(jsonObject, lastSeedlingDataInfo)

        //胶囊录音是否正在录制
        jsonObject.put(KEY_LOTTIE_LOGO_PLAY, curStatus == RECORDING)

        //需要展示标记时发送正常数据，不显示时返回空串
        getChangedDataMarkText(jsonObject, showMarkText)

        //获取录制保存数据
        getSaveRecordData(jsonObject)

        //获取拆分的录制时间
        getSplitRecordTime(jsonObject)

        return jsonObject
    }

    private fun getChangeDataRecorderBtnJsonAnimation(jsonObject: JSONObject, lastSeedlingDataInfo: SeedlingDataInfo? = null) {
        val lastStatus = lastSeedlingDataInfo?.let { it.recordEnable?.let { it1 -> getRecordStatus(it1, it.curStatus) } }
        val nowStatus = recordEnable?.let { getRecordStatus(it, curStatus) }

        if (lastStatus != nowStatus) {
            when (lastStatus) {
                ENABLE_START -> {
                    when (nowStatus) {
                        ENABLE_PAUSE -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_PAUSE_TO_START)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_START_TO_PAUSE)
                        }


                        DISABLE_START -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_DISABLE_TO_START)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_START_TO_DISABLE)
                        }
                    }
                }

                ENABLE_PAUSE -> {
                    when (nowStatus) {
                        ENABLE_START -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_PAUSE_TO_START)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_START_TO_PAUSE)
                        }

                        DISABLE_PAUSE -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_DISABLE_TO_PAUSE)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_PAUSE_TO_DISABLE)
                        }
                    }
                }

                DISABLE_PAUSE -> {
                    when (nowStatus) {
                        ENABLE_PAUSE -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_DISABLE_TO_PAUSE)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_PAUSE_TO_DISABLE)
                        }

                        DISABLE_START -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_DISABLE_TO_START)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_START_TO_DISABLE)
                        }
                    }
                }
                //实际体现情况无不可用播放态，但是代码逻辑有此状态需当作不可用暂停态来使用
                DISABLE_START -> {
                    when (nowStatus) {
                        ENABLE_START -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_DISABLE_TO_START)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_START_TO_DISABLE)
                        }

                        DISABLE_PAUSE -> {
                            jsonObject.put(KEY_RECORD_TRANSITION_ON, TRANSITION_DISABLE_TO_START)
                            jsonObject.put(KEY_RECORD_TRANSITION_OFF, TRANSITION_START_TO_DISABLE)
                        }
                    }
                }
            }
        }
    }

    private fun getRecordStatus(recordEnable: Boolean, curStatus: Int): Int {
        return if (recordEnable && curStatus != RECORDING) { //正常暂停状态
            ENABLE_PAUSE
        } else if (recordEnable) { //正常录制状态
            ENABLE_START
        } else if (curStatus != RECORDING) { //不可用暂停状态
            DISABLE_PAUSE
        } else { //不可用播放状态
            DISABLE_START
        }
    }

    private fun getChangedDataMarkText(jsonObject: JSONObject, showMarkText: Boolean) {
        if (showMarkText) {
            (getMarkTextInternal() ?: getRecordStatusTextInternal()).run {
                jsonObject.put(KEY_CUR_MARK_TIME, first)
                jsonObject.put(KEY_CUR_MARK_TIME_DESC, second)
            }
        } else {
            val statusDec = getRecordStatusTextInternal()
            jsonObject.put(KEY_CUR_MARK_TIME, statusDec.first)
            jsonObject.put(KEY_CUR_MARK_TIME_DESC, statusDec.second)
        }
    }

    fun getSaveRecordData(jsonObject: JSONObject) {
        if (isEntranceBg) {
            jsonObject.put(KEY_CARD_BG, CARD_ENTRANCE_BG_COLOR)
        } else {
            jsonObject.put(KEY_CARD_BG, CARD_BG_COLOR)
        }

        val isShowG1 = RecorderViewModel.getInstance().isAlreadyRecordingExceptSaving()
        if (isShowG1) {
            jsonObject.put(KEY_SAVE_BTN_TEXT,  BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.rename_save))
        }

        //是否显示录制中页面 true 显示录制中卡片 false 显示保存状态卡片
        jsonObject.put(KEY_SHOW_RECORD_VIEW, isShowG1)
        //只有在保存成功和录制状态才能点击
        jsonObject.put(KEY_CARD_CLICK_TYPE,
            if (RecorderViewModel.getInstance().isAlreadyRecordingExceptSavingOrSaveSuccess()) KEY_CARD_CLICK_TYPE_DATE else "")

        when (saveFileState) {
            START_LOADING, SHOW_LOADING_DIALOG -> {
                //一加外销1*2录音卡,正在保存特有字段,其他卡片不使用 start
                jsonObject.put(KEY_SMALL_CARD_SAVE_LOTTIE, SMALL_CARD_SAVING)
                //一加外销1*2录音卡,正在保存特有字段,其他卡片不使用 end
                jsonObject.put(KEY_SAVE_CONTENT, fileName.title())
                jsonObject.put(KEY_SAVE_TITLE, BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.is_saving))
                jsonObject.put(KEY_SAVE_TITLE_2, BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.is_saving))
                jsonObject.put(KEY_SAVE_FILE_NAME, fileName.title())
                //获取保存波形图的进度（推测值）。
                putRecorderSavingProcessData(jsonObject)
            }

            SUCCESS -> {
                //一加外销1*2录音卡,保存成功特有字段,其他卡片不使用 start
                jsonObject.put(KEY_SMALL_CARD_SAVE_LOTTIE, SMALL_CARD_SAVE_SUCCESS)
                //一加外销1*2录音卡,保存成功特有字段,其他卡片不使用 end
                jsonObject.put(KEY_SAVE_LOTTIE, LOTTIE_SAVE_SUCCESS)
                jsonObject.put(KEY_SAVE_CONTENT, BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.click_to_view))
                jsonObject.put(KEY_SAVE_TITLE, BaseApplication.getAppContext().resources.getString(
                    com.soundrecorder.common.R.string.recorder_saved,
                        fileName.title()))
                jsonObject.put(KEY_FILE_NAME, fileName)
                jsonObject.put(KEY_SAVE_TITLE_2, BaseApplication.getAppContext().resources.getString(
                        com.soundrecorder.common.R.string.save_as_v2,
                        BaseApplication.getAppContext().resources.getString(com.soundrecorder.common.R.string.app_name_main)))
                jsonObject.put(KEY_SAVE_FILE_NAME, fileName.title())
                jsonObject.put(KEY_SHOW_PROCESS_PERCENT, SAVE_COMPLEDED_PROCESS)
                jsonObject.put(KEY_SAVE_RECORD_FROM_WHERE, SeedlingSavingTimer.mSaveRecordFromWhere)
            }

            ERROR -> {
                //一加外销1*2录音卡,保存失败特有字段,其他卡片不使用 start
                jsonObject.put(KEY_SMALL_CARD_SAVE_LOTTIE, SMALL_CARD_SAVING_FAIL)
                //一加外销1*2录音卡,保存失败特有字段,其他卡片不使用 end
                jsonObject.put(KEY_SAVE_LOTTIE, LOTTIE_SAVE_FAIL)
                jsonObject.put(KEY_SAVE_CONTENT, fileName.title())
                jsonObject.put(KEY_SAVE_TITLE, BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.save_failed))
                jsonObject.put(KEY_SAVE_TITLE_2, BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.save_failed))
                jsonObject.put(KEY_SAVE_FILE_NAME, fileName.title())
            }
        }
    }

    private fun getSplitRecordTime(jsonObject: JSONObject) {
        if (curTime >= 0) {
            val time = TimeUtils.getFormatTimeExclusiveMill(curTime, false)
            val timeSeparator = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.time_separator)
            val timeArray = time.split(timeSeparator)
            when (timeArray.size) {
                RECORD_CONSTANTS_4 -> {
                    putRecorderDayData(jsonObject, timeArray[RECORD_CONSTANTS_0], timeSeparator)
                    putRecorderHourData(jsonObject, timeArray[RECORD_CONSTANTS_1], timeSeparator)
                    putRecorderMinuteData(jsonObject, timeArray[RECORD_CONSTANTS_2], timeSeparator, timeArray[RECORD_CONSTANTS_3])
                }

                RECORD_CONSTANTS_3 -> {
                    putRecorderHourData(jsonObject, timeArray[RECORD_CONSTANTS_0], timeSeparator)
                    putRecorderMinuteData(jsonObject, timeArray[RECORD_CONSTANTS_1], timeSeparator, timeArray[RECORD_CONSTANTS_2])
                }

                RECORD_CONSTANTS_2 -> {
                    putRecorderMinuteData(jsonObject, timeArray[RECORD_CONSTANTS_0], timeSeparator, timeArray[RECORD_CONSTANTS_1])
                }

                else -> DebugUtil.w(TAG, "getSplitRecordTime error time =$time")
            }
        }
    }

    private fun putRecorderDayData(jsonObject: JSONObject, dayText: String, daySeparator: String) {
        jsonObject.put(KEY_DAY_TEXT, dayText)
        jsonObject.put(KEY_DAY_SEPARATOR, daySeparator)
    }

    private fun putRecorderHourData(jsonObject: JSONObject, hourText: String, hourSeparator: String) {
        jsonObject.put(KEY_HOUR_TEXT, hourText)
        jsonObject.put(KEY_HOUR_SEPARATOR, hourSeparator)
    }

    private fun putRecorderMinuteData(jsonObject: JSONObject, minuteText: String, minuteSeparator: String, secondText: String) {
        jsonObject.put(KEY_MINUTE_TEXT, minuteText)
        jsonObject.put(KEY_MINUTE_SEPARATOR, minuteSeparator)
        jsonObject.put(KEY_SECOND_TEXT, secondText)
    }

    fun putRecorderSavingProcessData(jsonObject: JSONObject) {
        if ((processValue != SAVING_DEFAULT_PROCESS)) {
            jsonObject.put(KEY_SHOW_PROCESS_PERCENT, processValue)
        }
    }

    private fun getMarkTextInternal(): Pair<String, String>? {
        getCurLabelInfo(true)?.let {
            val showText = it.getOrNull(0)
            val contentDescription = it.getOrNull(1)
            if (showText != null && contentDescription != null) {
                return Pair(showText, contentDescription)
            }
        }
        return null
    }

    private fun getRecordStatusTextInternal(): Pair<String, String> {
        if (recorderViewModelApi?.saveFileState != INIT) {
            return Pair(
                BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.is_saving),
                BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.is_saving_talk_back)
            )
        }
        return when (curStatus) {
            RECORDING -> {
                Pair(
                    BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.recording),
                    BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.recording_notify_talk_back)
                )
            }

            PAUSED -> {
                if (recorderViewModelApi?.isAudioModeChangePause() == true) {
                    val pauseStr = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.state_call_record_paused)
                    Pair(pauseStr, pauseStr)
                } else {
                    val pauseStateText = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.record_pause_tips)
                    Pair(pauseStateText, pauseStateText)
                }
            }

            else -> Pair("", "")
        }
    }

    override fun equals(other: Any?): Boolean {
        if (other !is SeedlingDataInfo) {
            return false
        }

        if (!other.getCurTimeInfo().contentEquals(getCurTimeInfo(true))) {
            return false
        }

        if (other.curMarkTime != curMarkTime) {
            return false
        }

        if (other.markEnable != markEnable) {
            return false
        }

        if (other.curStatus != curStatus) {
            return false
        }

        if (other.recordEnable != recordEnable) {
            return false
        }

        if (other.saveFileState != saveFileState) {
            return false
        }

        return true
    }

    override fun hashCode(): Int {
        var result = curStatus
        result = 31 * result + (markEnable?.hashCode() ?: 0)
        result = 31 * result + (recordEnable?.hashCode() ?: 0)
        result = 31 * result + (curTimeInfo?.contentHashCode() ?: 0)
        result = 31 * result + (curMarkInfo?.contentHashCode() ?: 0)
        result = 31 * result + saveFileState.hashCode()
        return result
    }
}

