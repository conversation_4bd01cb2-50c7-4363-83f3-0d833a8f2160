/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordSaveProcessor
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.controller.worker

import android.annotation.SuppressLint
import android.app.RecoverableSecurityException
import android.content.ContentUris
import android.content.ContentValues
import android.net.Uri
import android.provider.MediaStore
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.MediaDataScanner
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.DirectRecordStatus
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.CURRENT_ACTION_SAVERECORD
import com.soundrecorder.recorderservice.CURRENT_RESULT_FAILED
import com.soundrecorder.recorderservice.CURRENT_RESULT_SUC
import com.soundrecorder.recorderservice.RecordResult
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import com.soundrecorder.recorderservice.controller.RecorderController
import com.soundrecorder.recorderservice.controller.observer.ControllerObserver
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import com.soundrecorder.wavemark.wave.id3tool.Mp3File
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil
import java.io.File
import java.io.FileOutputStream

class RecordSaveProcessor constructor(private val mRefRecorderController: RecorderController) {

    private companion object {
        private const val TAG = "RecordSaveProcessor"
    }

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    @Suppress("TooGenericExceptionCaught")
    @Synchronized
    fun saveRecordInfo(defaultDisplayName: String, saveRecordFromWhere: Int, trigSyncNow: Boolean): RecordResult {

        val controllerObserver: ControllerObserver<*>? = mRefRecorderController.controllerObserver
        val recordInfoSaveObserver = mRefRecorderController.recordInfoSaveObserver

        if (controllerObserver == null) {
            DebugUtil.e(TAG, "run, should register ControllerObserver first, now is null")
            return RecordResult(CURRENT_ACTION_SAVERECORD, CURRENT_RESULT_FAILED)
        }

        if (recordInfoSaveObserver == null) {
            DebugUtil.e(TAG, "run, should register RecordInfoSaveObserver first, now is null, ")
        }

        var namePath: String? = Constants.EMPTY

        try {
            namePath = controllerObserver.recordFilePath
            DebugUtil.i(TAG, "namepath: $namePath")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "namePath", e)
        }

        //存在界面的时候小布停止会触发停止录音    故会有两次停止日志
        /*if (recordInfoSaveObserver != null && recordInfoSaveObserver.stopHook()) {
            return RecordResult(CURRENT_ACTION_SAVERECORD, CURRENT_RESULT_SUC)
        }*/

        DebugUtil.d(TAG, "run, displayName: $defaultDisplayName, sampleName:=${controllerObserver.sampleFileName}")
        if (BaseUtil.isAndroidQOrLater) {
            namePath = defaultDisplayName
        }

        val inputName = namePath ?: Constants.EMPTY
        DebugUtil.i(TAG, "inputName: $inputName")
        while (RecorderViewModel.getInstance().isDoMultiPictureMarkLoading()) {
            try {
                Thread.sleep(NumberConstant.NUM_100.toLong())
            } catch (_: Exception) {
            }
        }
        /*write mark data into db*/
        val markList = RecorderViewModel.getInstance().getMarkData()
        val directRecordStatus = DirectRecordStatus(RecorderViewModelApi.getDirectRecordOn(),
            RecorderViewModelApi.getDirectRecordTime())
        writeMarkTag(
            mRefRecorderController,
            controllerObserver,
            recordInfoSaveObserver,
            inputName,
            markList,
            saveRecordFromWhere,
            directRecordStatus,
            trigSyncNow
        )

        val absoluteFilePath =
            StorageManager.getInstance(BaseApplication.getAppContext()).storagePrefix + File.separator + controllerObserver.relativePath
        val fullPath = absoluteFilePath + inputName
        //录音界面回调
        recordInfoSaveObserver?.saveDialogCallback(inputName, fullPath, saveRecordFromWhere)
        playbackApi?.startMuteDetectIfNecessary(fullPath)
        CloudStaticsUtil.addCloudLog(TAG, "saveRecordInfo, success- $inputName")
        return RecordResult(CURRENT_ACTION_SAVERECORD, CURRENT_RESULT_SUC)
    }


    @Synchronized
    private fun writeMarkTag(
        controller: RecorderController,
        controllerObserver: ControllerObserver<*>,
        recordInfoSaveObserver: RecordInfoSaveObserver?,
        inputName: String,
        markList: List<MarkDataBean>,
        saveRecordFromWhere: Int = MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY,
        directRecordStatus: DirectRecordStatus,
        trigSyncNow: Boolean
    ) {

        if (controller.mIsNeedResult) {
            return
        }
        DebugUtil.d(TAG, "writeMarkTag,isDirectOn:$directRecordStatus.isDirectOn," +
                "directStartTime:$directRecordStatus.directStartTime, " +
                "directEndTime:$directRecordStatus.directEndTime")
        val markTagString = StringBuilder()
        markTagString.append(MarkSerializUtil.MARK_TAG_HEAD).append(MarkSerializUtil.MARK_TAG_TAIL)
        DebugUtil.i(TAG, "inputName: $inputName,start amplitudeList")
        val amplitudeList = controller.recorderAmplitudeModel?.amplitudeList
        val amplitudeString = AmplitudeListUtil.getAmplitudeString(amplitudeList)
        DebugUtil.i(TAG, "inputName: $inputName,end amplitudeList,size：${amplitudeList?.size}")
        markTagString.append(MarkSerializUtil.SPLIT_BETWEEN_MARK_AND_AMPLITUDE)
        markTagString.append(amplitudeString)
        val size = markTagString.toString().toByteArray(Constants.UTF_8).size
        DebugUtil.d(TAG, "writeMarkTAG,  the byte length is $size")
        val recorderDBUtil = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
        val currentGroupInfo = GroupInfoDbUtil.getGroupInfoForSaveRecordFile(saveRecordFromWhere)
        try {
            if (BaseUtil.isAndroidQOrLater) {
                if (isMp3File(controllerObserver.mimeType)) {
                    val file = Mp3File()
                    file.create(controllerObserver.sampleUri, BaseApplication.getAppContext())
                    file.customTag = markTagString.toString().toByteArray(Constants.UTF_8)
                    file.setMarkSizeTag(Mp3File.intToByteArray(size))
                    file.saveMarkToNewFile(FileOutputStream(controllerObserver.recordFilePath, true))
                    DebugUtil.i(TAG, "end write mp3File, markString: $markList")
                }

                DebugUtil.d(TAG, "writeMarkTag end, cur time: ${System.currentTimeMillis()}")
                val result =
                    try {
                        MediaDBUtils.rename(controllerObserver.sampleUri, inputName)
                    } catch (e: RecoverableSecurityException) {
                        recordInfoSaveObserver?.deleteUriInMediaDBWhenException(inputName, e)
                        0
                    }
                if (result > 0) {
                    DebugUtil.i(TAG, "writeMarkTag rename result=$result, cur time: ${System.currentTimeMillis()}")
                    RecorderViewModel.getInstance().setFileBeingRecorded(null)
                    //Mark表中的删除逻辑已经放入到该数据库更新逻辑中，下面单独更新逻辑需要删除
                    recorderDBUtil.insertOrUpdateNewRecord(
                        controllerObserver.sampleUri,
                        amplitudeString,
                        markList,
                        controllerObserver.recordType,
                        currentGroupInfo,
                        true,
                        directRecordStatus,
                        controllerObserver.uuid
                    )
                    // 手动调用同步中子-由于默认名称更改为了“标准录音 x”，若用户未重命名，导致文件监听将该文件过滤不会同步到中子
                    notifyDmp(controllerObserver.sampleUri)
                }
            } else {
                if (TextUtils.isEmpty(controllerObserver.sampleFileName)) {
                    DebugUtil.e(TAG, "run, writeMarkTag, getDefaultDisplayName is null")
                    return
                }
                var mSaveUri: Uri? = null
                val inputFile = File(inputName)
                val mSaveFile = File(inputFile.parent, controllerObserver.sampleFileName)
                if (isMp3File(controllerObserver.mimeType)) {
                    val file = Mp3File(inputFile)
                    file.customTag = markTagString.toString().toByteArray(Constants.UTF_8)
                    file.setMarkSizeTag(Mp3File.intToByteArray(size))
                    file.saveMarkToNewFile(mSaveFile)
                }
                try {
                    mSaveUri =
                        MediaDBUtils.genUri(genSaveContentValues(controllerObserver, mSaveFile))
                } catch (e: Exception) {
                    DebugUtil.e(TAG, "writeMarkTag error", e)
                }
                if (mSaveUri == null) {
                    DebugUtil.e(TAG, "write Mark Tag failed , no media file generated")
                    return
                }
                DebugUtil.i(
                    TAG, "writeMarkTAG : save file " + mSaveFile.name + ", save file length: " + mSaveFile.length()
                            + ", sample file length: " + inputFile.length()
                )
                val paths = arrayOf(inputFile.absolutePath)
                inputFile.delete()
                MediaDataScanner.getInstance().mediaScan(BaseApplication.getAppContext(), paths)
                DebugUtil.v(TAG, "mark tag write successful.")
                MediaDBUtils.updateRealSizeAndDuration(
                    BaseApplication.getAppContext(),
                    mSaveFile
                )
                //Mark表中的删除逻辑已经放入到该数据库更新逻辑中，下面单独更新逻辑需要删除
                recorderDBUtil.insertOrUpdateNewRecord(
                    mSaveFile,
                    amplitudeString,
                    markList,
                    controllerObserver.recordType,
                    currentGroupInfo,
                    directRecordStatus
                )
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "mark tag error", e)
        }
        //删除旧录音资源
        val relativePath = controllerObserver.relativePath
        val displayName = controllerObserver.sampleFileName
        val recordId = RecorderDBUtil.getKeyIdByPath(relativePath, displayName)
        RecorderService.removeRecordIdWhenRecordComplete(recordId, PrefUtil.KEY_SAVE_RECORD_ID_WHEN_RECORDING)
        RecorderService.removeRecordIdWhenRecordComplete(recordId, PrefUtil.KEY_SAVE_RECORD_ID_FOR_ABNORMAL_EXIT)
        //分组信息更新
        GroupInfoManager.getInstance(BaseApplication.getAppContext()).verifyGroupCount()
        // 本地数据保存完成，触发云同步
        DebugUtil.i(TAG, "insert new record trig backup, $trigSyncNow")
        if (trigSyncNow) {
            cloudKitApi?.trigBackupNow(BaseApplication.getAppContext())
        }
    }

    private fun genSaveContentValues(
        observer: ControllerObserver<*>,
        name: String
    ): ContentValues {
        val current = System.currentTimeMillis()
        val values = ContentValues()
        values.put(MediaStore.Audio.Media.IS_MUSIC, "0")
        values.put(MediaStore.Audio.Media.TITLE, MediaDBUtils.getTitleByName(name))
        values.put(MediaStore.Audio.Media.DISPLAY_NAME, name)
        values.put(MediaStore.Audio.Media.DATE_ADDED, current / TimeUtils.TIME_ONE_SECOND)
        values.put(MediaStore.Audio.Media.DATE_MODIFIED, current / TimeUtils.TIME_ONE_SECOND)
        values.put(MediaStore.Audio.Media.MIME_TYPE, observer.mimeType)
        val mRelativePath = observer.relativePath
        values.put(MediaStore.Audio.Media.RELATIVE_PATH, mRelativePath)
        return values
    }

    private fun genSaveContentValues(
        observer: ControllerObserver<*>,
        file: File
    ): ContentValues {
        val current = System.currentTimeMillis()
        val values = ContentValues()
        values.put(MediaStore.Audio.Media.IS_MUSIC, "0")
        values.put(MediaStore.Audio.Media.TITLE, MediaDBUtils.getTitleByName(file.name))
        values.put(MediaStore.Audio.Media.DISPLAY_NAME, file.name)
        values.put(MediaStore.Audio.Media.DATE_ADDED, current / TimeUtils.TIME_ONE_SECOND)
        values.put(MediaStore.Audio.Media.DATE_MODIFIED, current / TimeUtils.TIME_ONE_SECOND)
        values.put(MediaStore.Audio.Media.MIME_TYPE, observer.mimeType)
        values.put(MediaStore.Audio.Media.DATA, file.absolutePath)
        DebugUtil.i(TAG, "save values:$values")
        return values
    }

    @SuppressLint("NewApi")
    private fun deleteUriInMediaDB(uri: Uri, observer: RecordInfoSaveObserver?) {
        if (BaseUtil.isAndroidQOrLater) {
            try {
                val count = MediaDBUtils.delete(uri)
                DebugUtil.i(TAG, "deleteUriInMediaDB input uri: $uri, delete count: $count")
            } catch (e: RecoverableSecurityException) {
                DebugUtil.e(TAG, "catch delete RecoverableSecurityException", e)
                observer?.deleteUriInMediaDBWhenException(null, e)
            }
        }
    }

    private fun notifyDmp(uri: Uri) {
        runCatching {
            browseFileApi?.onFileUpdateSuccess(ContentUris.parseId(uri), null)
        }
    }

    @Synchronized
    fun release() {
    }

    private fun isMp3File(mimeType: String): Boolean {
        return RecordConstant.MIMETYPE_MP3 == mimeType
    }
}