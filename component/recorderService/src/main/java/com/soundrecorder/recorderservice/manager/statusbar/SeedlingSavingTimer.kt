/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingSavingTimer
 * * Description :  Used for countdown tasks in fluid cloud capsule storage。
 * * Version     : 1.0
 * * Date        : 2024/11/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.os.CountDownTimer
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.controller.RecorderController
import com.soundrecorder.recorderservice.manager.statusbar.FluidCardStatusBar.createSeedlingDataInfo
import com.soundrecorder.recorderservice.manager.statusbar.FluidCardStatusBar.getSeedlingData
import org.json.JSONObject

class SeedlingSavingTimer(
    val recorderController: RecorderController? = null,
    val saveRecordFromWhere: Int?
) {

    companion object {
        private const val TAG = "SeedlingSavingTimer"
        private const val TIMER_INTERVAL = 0.1 * TimeUtils.TIME_ONE_SECOND_LONG
        private const val DEFAULT_SAVE_TIME = 1.5 * TimeUtils.TIME_ONE_SECOND_LONG
        private const val DEFAULT_INIT_PROCESS = 5
        private const val END_PROCESS = 95
        private const val COMPLETE_PROCESS = 100
        private const val NUMBER_100: Int = 100

        private var AMPLITUDE_LIST_SIZE_10000 = 10000
        private var AMPLITUDE_LIST_SIZE_20000 = 20000
        private var AMPLITUDE_LIST_SIZE_45000 = 45000
        private var AMPLITUDE_LIST_SIZE_100000 = 100000
        private var AMPLITUDE_LIST_SIZE_1000000 = 1000000
        private var AMPLITUDE_LIST_SIZE_1500000 = 1500000

        private var HANDLE_SPEED_2500 = 2500
        private var HANDLE_SPEED_5000 = 5000
        private var HANDLE_SPEED_10000 = 10000
        private var HANDLE_SPEED_20000 = 20000
        private var HANDLE_SPEED_30000 = 30000
        private var HANDLE_SPEED_40000 = 40000
        private var HANDLE_SPEED_50000 = 50000
        private var HANDLE_SPEED_160000 = 160000

        @JvmStatic
        var processValue = -1

        @JvmStatic
        var mSaveRecordFromWhere: Int? = null
    }
    private var timerDuration = 0L  //保存时间。
    private var timer: CountDownTimer? = null
    private lateinit var originData: JSONObject
    private lateinit var newSeedlingDataInfo: SeedlingDataInfo
    private var amplitudeList: List<Int>? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }


    fun startTimer() {
        cancelTimer()
        initRecordData()
        timer = object : CountDownTimer(timerDuration, TIMER_INTERVAL.toLong()) {
            override fun onTick(remainTime: Long) {
                val process = ((timerDuration - remainTime) * NUMBER_100 / timerDuration).toInt()
                if ((0 <= process && process <= DEFAULT_INIT_PROCESS)) {
                    processValue = DEFAULT_INIT_PROCESS
                } else if (END_PROCESS <= process && process <= COMPLETE_PROCESS) {
                    //99不明显,看着跟100差不多。所以这里设置最大95。
                    processValue = END_PROCESS
                } else {
                    processValue = process
                }
                DebugUtil.d(
                    TAG,
                    "remainTime:$remainTime ,processValue:$processValue"
                )
                updateProcess()
            }

            override fun onFinish() {
                DebugUtil.d(TAG, "onFinish")
                processValue = END_PROCESS
                updateProcess()
                cancelTimer()
            }
        }
        timer?.start()

        mSaveRecordFromWhere = saveRecordFromWhere
    }


    fun cancelTimer() {
        timer?.cancel()
        timer = null
        processValue = -1
    }

    fun initRecordData() {
        originData = getSeedlingData()
        newSeedlingDataInfo = createSeedlingDataInfo()
        timerDuration = getHandleTime() * TimeUtils.SECONDS_1000
        DebugUtil.d(TAG, "timerDuration:$timerDuration")
    }

    fun updateProcess() {
        DebugUtil.d(TAG, "updateProcess:")
        recorderViewModelApi?.onSeedlingCardStateChanged(true)
    }

    fun getHandleTime(): Long {
        return (((recorderController?.recorderAmplitudeModel?.amplitudeList?.size)?.div(
            getHandleSpeed(
                recorderController.recorderAmplitudeModel?.amplitudeList?.size
            )
        ))
            ?: DEFAULT_SAVE_TIME).toLong()
    }

    fun getHandleSpeed(size: Int?): Int {
        return when (size) {
            in 0..AMPLITUDE_LIST_SIZE_10000 -> HANDLE_SPEED_160000
            in AMPLITUDE_LIST_SIZE_10000..AMPLITUDE_LIST_SIZE_20000 -> HANDLE_SPEED_50000
            in AMPLITUDE_LIST_SIZE_20000..AMPLITUDE_LIST_SIZE_45000 -> HANDLE_SPEED_40000
            in AMPLITUDE_LIST_SIZE_45000..AMPLITUDE_LIST_SIZE_100000 -> HANDLE_SPEED_20000
            in AMPLITUDE_LIST_SIZE_100000..AMPLITUDE_LIST_SIZE_1000000 -> HANDLE_SPEED_10000
            in AMPLITUDE_LIST_SIZE_1000000..AMPLITUDE_LIST_SIZE_1500000 -> HANDLE_SPEED_5000
            else -> HANDLE_SPEED_2500
        }
    }
}