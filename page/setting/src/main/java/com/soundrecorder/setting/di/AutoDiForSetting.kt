/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForSetting.kt
 * * Description : AutoDiForSetting
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.setting.di

import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.setting.SettingApi
import org.koin.dsl.module

object AutoDiForSetting {
    val settingModule = module {
        single<SettingInterface>(createdAtStart = true) {
            SettingApi
        }
    }
}