/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ThirdRecordResultBean
 * Description:
 * Version: 1.0
 * Date: 2024/4/23
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/4/23 1.0 create
 */

package com.soundrecorder.setting.setting.callsummary

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.annotation.Keep
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.base.utils.MetaDataUtils
import com.soundrecorder.modulerouter.summary.SummaryInterface

@Keep
data class ThirdRecordResultBean(val stateOn: <PERSON>olean, val supportSummaryVersion: Int, val summaryAvailable: Boolean) {
    companion object {
        const val SUPPORT_SUMMARY_VERSION_RECORD = 1
        const val SUPPORT_SUMMARY_VERSION_THIRD_SUMMARY = 2
        const val SUPPORT_SUMMARY_VERSION_GLOBAL_SUMMARY = 3

        const val PACKAGE_NAME_THIRD_APP_RECORD = "com.oplus.audiomonitor"
        /*语音转文字包名*/
        const val PACKAGE_NAME_ASSISTANT = "com.coloros.accessibilityassistant"
        /*语音转文字改名“AI语音摘记” meta-data >=1*/
        const val SUPPORT_AI_VOICESCRIBE = "support_ai_voiceScribe"
        /*翻译服务包名*/
        private const val PACKAGE_NAME_TRANSLATE = "com.coloros.translate.engine"
        private const val TAG = "ThirdRecordResultBean"

        private const val SETTING_VALUE_AUTO_SMART_VOICE_SWITCH = "auto_smart_voice_switch_status"
        private const val DEFAULT_VALUE_SWITCH_OPEN = 1
        private const val DEFAULT_VALUE_SWITCH_CLOSE = 0

        private const val SUMMARY_SUPPORT_GLOBAL = "global_summary_support"
        private const val ASSISTANT_APP_SETTINGS_ACTION = "action.oplus.accessibilityassistant.global.settings"

        //third app feature
        private const val SUMMARY_SUPPORT_THIRD_SUMMARY = "third_call_summary_support"
        private const val THIRD_APP_RECORD_SETTINGS_ACTION = "com.oplus.third.app.record.settings"
        private const val THIRD_APP_RECORD_AUTHORITY = "content://com.oplus.thirdapp.toogle.status"

        /**
         * 语音转文字 + 翻译服务是否支持全局通话录音的摘要功能
         */
        @JvmStatic
        fun isSupportGlobalSummary(context: Context): Boolean {
            if (MetaDataUtils.getMetaDataInt(
                    context,
                    PACKAGE_NAME_ASSISTANT,
                    SUMMARY_SUPPORT_GLOBAL
                ) < 1
            ) { // 判断语音转文字版本是否支持第三方通话录音摘要
                DebugUtil.w(TAG, "isSupportGlobalSummary assistant false ")
                return false
            }

            if (MetaDataUtils.getMetaDataInt(
                    context,
                    PACKAGE_NAME_TRANSLATE,
                    SUMMARY_SUPPORT_GLOBAL
                ) < 1
            ) { // 判断翻译服务版本是否支持第三方通话录音摘要
                DebugUtil.w(TAG, "isSupportGlobalSummary translate false")
                return false
            }

            return true
        }

        /**
         * 语音转文字接管三方通话录音后，从语音转文字查询开关状态
         * @return true: 开关打开 false：开关关闭
         */
        fun isThirdRecordSwitchStateOnByAssistant(): Boolean {
            return Settings.Secure.getInt(
                BaseApplication.getAppContext().contentResolver,
                SETTING_VALUE_AUTO_SMART_VOICE_SWITCH,
                DEFAULT_VALUE_SWITCH_CLOSE
            ) == DEFAULT_VALUE_SWITCH_OPEN
        }

        /**
         * 智慧语音能力是否可用
         */
        @JvmStatic
        fun isGlobalSummaryAvailable(): Boolean {
            if (false == Injector.injectFactory<SummaryInterface>()?.isSupportGlobalSummary(BaseApplication.getAppContext())) {
                DebugUtil.w(TAG, "isGlobalSummaryAvailable false by not support")
                return false
            }
            return true
        }

        /**
         * 获取跳转到语音转文字app的设置页intent
         */
        @JvmStatic
        fun getJumpSettingIntentOfAssistant(): Intent {
            return Intent().also {
                it.setPackage(PACKAGE_NAME_ASSISTANT)
                it.action = ASSISTANT_APP_SETTINGS_ACTION
            }
        }

        /**
         * 语音转文字 + 翻译服务是否支持摘要功能
         */
        @JvmStatic
        fun isAssistantThirdAppSupportSummary(context: Context): Boolean {
            if (!MetaDataUtils.getMetaDataBoolean(
                    context,
                    PACKAGE_NAME_ASSISTANT,
                    SUMMARY_SUPPORT_THIRD_SUMMARY
                )
            ) { // 判断语音转文字版本是否支持第三方通话录音摘要
                DebugUtil.w(TAG, "isAssistantThirdAppSupportSummary assistant false ")
                return false
            }

            if (!MetaDataUtils.getMetaDataBoolean(
                    context,
                    PACKAGE_NAME_TRANSLATE,
                    SUMMARY_SUPPORT_THIRD_SUMMARY
                )
            ) { // 判断翻译服务版本是否支持第三方通话录音摘要
                DebugUtil.w(TAG, "isAssistantThirdAppSupportSummary translate false")
                return false
            }

            return true
        }

        /**
         * 第三方通话录音是否支持摘要版本
         */
        @JvmStatic
        fun isThirdRecordSupportSummary(): Boolean {
            if (!MetaDataUtils.getMetaDataBoolean(
                    BaseApplication.getAppContext(),
                    PACKAGE_NAME_THIRD_APP_RECORD,
                    SUMMARY_SUPPORT_THIRD_SUMMARY
                )
            ) {
                DebugUtil.w(TAG, "isSupportThirdRecordSummary false by metaData")
                return false
            }

            if (ThirdAppRecordCheckManager.isSupportRmExportAudioMonitor()) {
                DebugUtil.w(TAG, "isSupportThirdRecordSummary false by realme export version")
                return false
            }

            return true
        }

        /**
         * 第三方通话录音开关状态
         * @return true: 开关打开 false：开关关闭
         */
        @JvmStatic
        fun isThirdRecordSwitchStateOn(): Boolean {
            kotlin.runCatching {
                val state =
                    BaseApplication.getAppContext().contentResolver.getType(Uri.parse(THIRD_APP_RECORD_AUTHORITY))?.toIntOrNull()
                return state == DEFAULT_VALUE_SWITCH_OPEN
            }.onFailure {
                DebugUtil.e(TAG, "isThirdRecordSwitchStateOn error $it")
            }

            return false
        }

        /**
         * 第三方通话录音摘要是否可用
         */
        @JvmStatic
        fun isThirdRecordSummaryAvailable(): Boolean {
            if (!isAssistantThirdAppSupportSummary(BaseApplication.getAppContext())) {
                DebugUtil.w(TAG, "isThirdRecordSummaryAvailable false by metaData")
                return false
            }

            if (false == Injector.injectFactory<SummaryInterface>()?.isSupportThirdRecordSummary(BaseApplication.getAppContext())) {
                DebugUtil.w(TAG, "isThirdRecordSummaryAvailable false by not support")
                return false
            }
            return true
        }

        /**
         * 获取到三方通话录音app的设置页Intent
         */
        @JvmStatic
        fun getJumpSettingIntentOfThirdApp(): Intent {
            return Intent().also {
                it.setPackage(PACKAGE_NAME_THIRD_APP_RECORD)
                it.action = THIRD_APP_RECORD_SETTINGS_ACTION
            }
        }
    }
}