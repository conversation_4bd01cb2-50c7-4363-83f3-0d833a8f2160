/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SettingApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting

import android.app.Activity
import android.content.Intent
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import com.soundrecorder.base.ext.isFlexibleWindow
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.setting.about.RecordAboutActivity
import com.soundrecorder.setting.opensource.OpenSourceActivity
import com.soundrecorder.setting.privacypolicy.PrivacyPolicyActivity
import com.soundrecorder.setting.setting.SettingRecorderActivity
import com.soundrecorder.setting.setting.callsummary.ThirdAppRecordCheckManager
import com.soundrecorder.setting.setting.dialog.RecordModeDialog

object SettingApi : SettingInterface {

    private const val TAG = "SettingApi"
    private const val FEEDBACKNAME = "com.customer.feedback.sdk.activity.FeedbackActivity"

    override fun launchForResult(fragment: Fragment, requestCode: Int) {
        val intent = Intent(fragment.context, SettingRecorderActivity::class.java)
        FollowHandPanelUtils.startActivityForResult(fragment, intent, requestCode)
        if (isFlexibleWindow(fragment.activity)) {
            fragment.activity?.overridePendingTransition(
                com.soundrecorder.common.R.anim.coui_open_slide_enter_zoom,
                com.soundrecorder.common.R.anim.coui_open_slide_exit_zoom
            )
        }
    }

    /**
     * 跳转开机向导隐私政策页面
     */
    override fun launchBootRegPrivacy(activity: Activity?, disableDialogFun: ((dialog: AlertDialog) -> Unit)) {
        BootRegUtil.launchBootRegPrivacy(activity, disableDialogFun)
    }

    /**
     * 录音隐私政策页面
     */
    override fun launchRecordPrivacy(activity: Activity?, type: Int) {
        val intent = Intent(activity, PrivacyPolicyActivity::class.java)
        intent.putExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TYPE, type)
        activity?.startActivity(intent)
    }

    /**
     * 收集个人信息明示清单
     */
    override fun launchCollectionInfo(activity: Activity?, type: Int) {
        if (activity != null) {
            val intent = Intent(activity, PrivacyPolicyActivity::class.java)
            intent.putExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TYPE, type)
            FollowHandPanelUtils.startActivity(activity, intent)
        }
    }

    /**
     * 收集个人信息明示清单-详情页
     */
    override fun launchCollectionInfoDetails(activity: Activity?, title: String?, type: Int, collectionType: Int) {
        if (activity != null) {
            val intent = Intent(activity, PrivacyPolicyActivity::class.java)
            intent.putExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TITLE, title)
            intent.putExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TYPE, type)
            intent.putExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_COLLECTION_TYPE, collectionType)
            FollowHandPanelUtils.startActivity(activity, intent)
        }
    }

    /**
     * 录音设置内部页面名称
     */
    override fun getPageNameInSetting(): Array<String> {
        return arrayOf(
            SettingRecorderActivity::class.java.name,
            RecordAboutActivity::class.java.name,
            OpenSourceActivity::class.java.name,
            FEEDBACKNAME
        )
    }

    /**
     * 获取是否需要显示录制模式小红点
     */
    override fun getNeedShowRecordModeRedDot(): Boolean {
        return RecordModeDialog.isNeedShowRecordModeRedDot()
    }

    /**
     * 获取是否需要显示录制模式小红点
     */
    override fun setRecordModeRedDotShowed() {
        RecordModeDialog.setRecordModeRedDotShowed()
    }

    /**
     * 获取是否需要显示录制模式小红点
     */
    override fun isSupportTripartiteAudioMonitor(): Boolean {
        return ThirdAppRecordCheckManager.isSupportTripartiteAudioMonitor()
    }
}