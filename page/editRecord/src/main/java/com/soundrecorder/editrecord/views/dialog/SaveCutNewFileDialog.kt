/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SaveCutNewFileDialog
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.dialog

import android.app.Activity
import android.view.View
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.dialog.AbsEditAlertDialog
import com.soundrecorder.common.dialog.PositiveCallback
import com.soundrecorder.common.fileoperator.rename.NameFileDialogUtil

class SaveCutNewFileDialog(
    activity: Activity,
    private var content: String,
    private var positiveCallback: PositiveCallback? = null
) : AbsEditAlertDialog(activity) {

    var mediaRecord: Record? = null

    override fun onInitCustomView(customView: View) {
        getEditText()?.apply {
            hint = activity.getString(com.soundrecorder.common.R.string.custom_mark_description)
            contentDescription = activity.getString(com.soundrecorder.common.R.string.talkback_input_flag_name)
        }
    }

    override fun getTitleText() = com.soundrecorder.common.R.string.saved_cut_file_title

    override fun onSave() {
        CuttingStaticsUtil.addCutTrimDialogSaveCancel(RecorderUserAction.VALUE_TRIM_DIALOG_SAVE)
        val newName = getNewContent()
        val mDialogUtil = NameFileDialogUtil(Constants.REQUEST_CODE_RENAME)
        val result: Int = mediaRecord?.let { mDialogUtil.onPositive(activity, NameFileDialogUtil.DIALOG_TYPE_CUT, newName, it) } ?: -1
        CloudStaticsUtil.addCloudLog("SaveCutNewFileDialog", "onSave, $newName, result=$result")
        if (result != -1) {
            showTextNote(result)
            return
        }
        val path: String? = mediaRecord?.data?.let { mDialogUtil.core2Full(newName, it) }
        if (isTitleChange()) {
            CuttingStaticsUtil.addActionSaveClipNameEdit()
        }
        positiveCallback?.callback(newName, path)
        CuttingStaticsUtil.addCutTrimSave(RecorderUserAction.VALUE_TRIM_SAVE_RETAIN)
        dismiss()
    }

    override fun onCancel() {
        CuttingStaticsUtil.addCutTrimDialogSaveCancel(RecorderUserAction.VALUE_TRIM_DIALOG_CANCEL)
        dismiss()
    }

    override fun getOriginalContent() = content
}