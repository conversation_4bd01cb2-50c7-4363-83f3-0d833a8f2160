/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditViewUtils
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.View
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.arch.core.executor.ArchTaskExecutor
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.allNotNull
import com.soundrecorder.base.utils.AddonAdapterCompatUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.RtlUtils
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.DirectRecordStatus
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.dialog.PositiveCallback
import com.soundrecorder.common.utils.AudioNameUtils
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.editrecord.ClipTask
import com.soundrecorder.editrecord.EditConstant
import com.soundrecorder.editrecord.EditRecordActivity
import com.soundrecorder.editrecord.databinding.EditRecordLayoutBinding
import com.soundrecorder.editrecord.ui.DialogDismissListener
import com.soundrecorder.editrecord.ui.EditRecordFragment
import com.soundrecorder.editrecord.ui.EditViewModel
import com.soundrecorder.editrecord.views.dialog.SaveCutNewFileDialog
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.notification.NotificationInterface
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_CLIP
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.launch
import java.io.File
import java.lang.ref.WeakReference
import java.util.Locale

object EditViewUtils {

    private const val TAG = "EditViewUtils"
    private var mCancelDialog: AlertDialog? = null
    private var mSaveClipDialog: SaveCutNewFileDialog? = null
    private var mLoadingDialog: LoadingDialog? = null
    //private val mTimerHandler = Handler(Looper.getMainLooper())
    private var mTimerHandler: SaveClipHandler? = null
    private const val DELAYS_SHOW_SAVE_RECORD_FILE_DIALOG = 100L
    private const val MSG_WHAT_FOR_SAVING = 1

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val notificationApi by lazy {
        Injector.injectFactory<NotificationInterface>()
    }

    @JvmStatic
    fun cancelNotificationModeAndGroup(editViewModel: EditViewModel?) {
        if (editViewModel == null) {
            return
        }
        notificationApi?.cancelNotificationModeAndGroup(
            notificationApi?.getNotificationMode(editViewModel.mIsFromOtherApp) ?: NotificationUtils.NOTIFICATION_MODE_COMMON,
            NotificationUtils.NOTIFICATION_PAGE_EDIT_RECORD
        )
    }

    @JvmStatic
    fun createCancelClipDialog(
        mIsClipped: Boolean,
        activity: Activity?,
        mPlayName: String?,
        mMarkDataList: MutableList<MarkDataBean>?,
        editViewModel: EditViewModel?
    ) {
        if (!allNotNull(mIsClipped, activity, mPlayName, mMarkDataList, editViewModel)) {
            DebugUtil.d(TAG, "createCancelClipDialog params is null return")
            return
        }

        if (!mIsClipped) {
            activity?.finish()
            return
        }

        ToastManager.clearToast()
        if (activity == null) {
            return
        }
        mCancelDialog = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setBlurBackgroundDrawable(true)
            .setTitle(com.soundrecorder.common.R.string.crop_record_exit_title)
            .setMessage(com.soundrecorder.common.R.string.crop_record_exit_msg)
            .setPositiveButton(com.soundrecorder.common.R.string.crop_record_exit_title) { _, _ ->
                if (mIsClipped) {
                    MediaDBUtils.delete(editViewModel?.playerController?.getPlayUri())
                }
                CuttingStaticsUtil.addCutTrimCancle(RecorderUserAction.VALUE_TRIM_CANCLE_EXIT)
                CuttingStaticsUtil.addCutTrimSave(RecorderUserAction.VALUE_TRIM_SAVE_NO_RETAIN)
                activity.finish()
            }
            .setNegativeButton(com.soundrecorder.common.R.string.crop_record_exit_continue) { _, _ ->
                CuttingStaticsUtil.addCutTrimCancle(RecorderUserAction.VALUE_TRIM_CANCLE_CONTINUE)
            }
            .create()
        mCancelDialog?.show()
        ViewUtils.updateWindowLayoutParams(mCancelDialog?.window)
    }

    private class SaveClipHandler(activity: EditRecordActivity?) : Handler(Looper.getMainLooper()) {

        var mWeakReference: WeakReference<EditRecordActivity>? = null

        init {
            this.mWeakReference = WeakReference(activity)
        }

        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == MSG_WHAT_FOR_SAVING) {
                val activity = mWeakReference?.get()
                DebugUtil.d(
                    "showSavingRecordFileDialogPost",
                    "showSavingRecordFileDialogPost"
                )
                showSaveLoadingDialog(activity)
            }
        }
    }

    @JvmStatic
    fun createSaveClipDialog(
        isRestore: Boolean = false,
        activity: Activity?,
        mPlayName: String?,
        mSaveDialogEditContent: String?,
        mMarkDataList: MutableList<MarkDataBean>?,
        editViewModel: EditViewModel?
    ): SaveCutNewFileDialog? {
        if (!allNotNull(activity, mPlayName, mMarkDataList, editViewModel)) {
            DebugUtil.d(TAG, "createSaveClipDialog params is null return")
            return null
        }

        val mediaRecord =
            MediaDBUtils.queryRecordByUri(editViewModel?.playerController?.getPlayUri())
        if (mediaRecord == null) {
            DebugUtil.e(TAG, " queryRecordByUri mediaRecord is null ,clip failed.")
            return null
        }

        val title = getClipName(mediaRecord, mPlayName)
        val content = if (isRestore) {
            mSaveDialogEditContent
        } else {
            title
        }
        if (content.isNullOrEmpty()) {
            return null
        }
        if (mTimerHandler == null) {
            mTimerHandler = SaveClipHandler(activity as? EditRecordActivity)
        }
        activity?.let {
            mSaveClipDialog = SaveCutNewFileDialog(it, content, object : PositiveCallback {
                @SuppressLint("RestrictedApi")
                override fun callback(displayName: String?, path: String?) {
                    val act = activity as? EditRecordActivity ?: return
                    mTimerHandler?.let { handler ->
                        val msg = handler.obtainMessage(MSG_WHAT_FOR_SAVING)
                        msg?.what = MSG_WHAT_FOR_SAVING
                        msg?.obj = act
                        handler.sendMessageDelayed(msg, DELAYS_SHOW_SAVE_RECORD_FILE_DIALOG)
                    }

                    act.lifecycleScope.launch(IO) {
                        asyncSaveCutRecords(
                            mediaRecord,
                            displayName,
                            path,
                            mMarkDataList,
                            editViewModel?.ampList?.value
                        )
                        ArchTaskExecutor.getInstance().executeOnMainThread {
                            releaseSaveLoadingDialog()
                            val data = Intent()
                            data.putExtra(Constants.KEY_IS_CLIPPED_SAVE, true)
                            data.putExtra(Constants.KEY_CLIPPED_SAVE_RECORD_MEDIA_ID, mediaRecord.id)
                            activity.setResult(RESULT_OK, data)
                            activity.finish()
                        }
                    }
                }
            })
            mSaveClipDialog?.apply {
                this.mediaRecord = mediaRecord
                show()
            }
        }
        return mSaveClipDialog
    }

    @JvmStatic
    fun showSaveLoadingDialog(activity: Activity?) {
        if (mLoadingDialog == null) {
            mLoadingDialog = LoadingDialog(activity)
        }
        mLoadingDialog?.show(com.soundrecorder.common.R.string.is_saving)
    }

    @JvmStatic
    fun getClipName(mediaRecord: Record, originName: String?): String? {
        var relativePath: String? = ""
        if (mediaRecord.relativePath != null) {
            relativePath = mediaRecord.relativePath
        } else if (mediaRecord.data != null) {
            relativePath =
                RecorderDBUtil.getRelativePathForData(mediaRecord.data, mediaRecord.displayName)
        }
        val clipName = AudioNameUtils.genDefaultFileTitle(relativePath, originName)
        DebugUtil.d(TAG, "originalName:$originName clipName:$clipName")
        return clipName
    }

    @JvmStatic
    fun isCancelDialogIsShowing(): Boolean = mCancelDialog?.isShowing() == true

    @JvmStatic
    fun isSaveClipDialogIsShowing(): Boolean = mSaveClipDialog?.isShowing() == true

    @JvmStatic
    fun getSaveClipDialogInputText(): String? = mSaveClipDialog?.getNewContent()

    @JvmStatic
    fun isSavingDialogIsShowing(): Boolean = mLoadingDialog?.isShowing() == true

    @JvmStatic
    fun releaseCancelDialog() {
        mCancelDialog?.dismiss()
        mCancelDialog = null
    }

    @JvmStatic
    fun releaseSaveDialog() {
        mSaveClipDialog?.dismiss()
        mSaveClipDialog = null
    }

    @JvmStatic
    fun releaseSaveLoadingDialog() {
        mLoadingDialog?.dismiss()
        mLoadingDialog = null
    }

    @JvmStatic
    fun removeCallbackForSavingDialog() {
        mTimerHandler?.removeCallbacksAndMessages(null)
        mTimerHandler = null
    }

    @JvmStatic
    fun handlerSavingHasMsgWhat(): Boolean = mTimerHandler?.hasMessages(MSG_WHAT_FOR_SAVING) == true

    @JvmStatic
    fun createProgressDialog(editRecordFragment: EditRecordFragment?, clickCancel: (() -> Unit)?): Pair<AlertDialog, DialogDismissListener>? {
        if (!allNotNull(editRecordFragment, editRecordFragment?.context)) {
            DebugUtil.d(TAG, "createProgressDialog editRecordFragment is null return")
            return null
        }
        var clipProgressDialogDismissListener: DialogDismissListener? = null
        var clipProgressDialog: AlertDialog? = null
        editRecordFragment?.context?.let {
            if (clipProgressDialogDismissListener == null) {
                clipProgressDialogDismissListener = DialogDismissListener(editRecordFragment)
            }
            clipProgressDialog = COUIAlertDialogBuilder(it, com.support.dialog.R.style.COUIAlertDialog_Progress_Cancelable)
                .setBlurBackgroundDrawable(true)
                .setTitle(com.soundrecorder.common.R.string.record_clipping)
                .setOnDismissListener(clipProgressDialogDismissListener)
                .setCancelable(false)
                .setPositiveButton(
                    it.resources.getString(com.soundrecorder.common.R.string.cancel),
                ) { dialog, which ->
                    clickCancel?.invoke()
                }
                .create()
            clipProgressDialog?.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.max =
                ClipTask.MAX_PROGRESS
            clipProgressDialog?.setCanceledOnTouchOutside(false)
        }
        if (clipProgressDialogDismissListener != null && clipProgressDialog != null) {
            return Pair(clipProgressDialog!!, clipProgressDialogDismissListener!!)
        }
        return null
    }

    @JvmStatic
    fun middleControlImageViewAccessibilityDelegate(
        editViewModel: EditViewModel?,
        mMiddleControlImageView: View?
    ): View.AccessibilityDelegate = object : View.AccessibilityDelegate() {
        override fun sendAccessibilityEvent(host: View, eventType: Int) {
            if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                if (editViewModel?.playerController?.isPlaying() != true) {
                    mMiddleControlImageView?.contentDescription =
                        mMiddleControlImageView?.resources?.getString(com.soundrecorder.common.R.string.recorder_play)
                } else {
                    mMiddleControlImageView?.contentDescription =
                        mMiddleControlImageView?.resources?.getString(com.soundrecorder.common.R.string.recorder_pause)
                }
            }
            super.sendAccessibilityEvent(host, eventType)
        }

        override fun onInitializeAccessibilityNodeInfo(
            view: View,
            accessibilityNodeInfo: AccessibilityNodeInfo
        ) {
            super.onInitializeAccessibilityNodeInfo(view, accessibilityNodeInfo)
            accessibilityNodeInfo.className = Button::class.java.name
        }
    }

    @JvmStatic
    fun timerTextViewAccessibilityDelegate(editViewModel: EditViewModel?): View.AccessibilityDelegate =
        object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    val position = (editViewModel?.playerController?.currentTimeMillis?.value ?: 0)
                    host.contentDescription =
                        TimeUtils.getDurationHint(BaseApplication.getAppContext(), position)
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }

    @JvmStatic
    fun timerTextViewAccessibilityDelegate(duration: Long): View.AccessibilityDelegate =
        object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    host.contentDescription =
                        TimeUtils.getDurationHint(BaseApplication.getAppContext(), duration)
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }

    @JvmStatic
    fun clipLastTime(
        lastTime: Long,
        isDelete: Boolean,
        editViewModel: EditViewModel?,
        dataBindUtil: EditRecordLayoutBinding?
    ): Long {
        val clipStartDuration = dataBindUtil?.rulerView?.startRecord ?: 0
        val clipEndDuration = dataBindUtil?.rulerView?.endRecord ?: 0
        return if (isDelete) {
            when {
                lastTime >= clipEndDuration -> {
                    lastTime - (clipEndDuration - clipStartDuration)
                }
                lastTime >= clipStartDuration -> {
                    clipStartDuration
                }
                else -> {
                    lastTime
                }
            }
        } else {
            when {
                lastTime >= clipEndDuration -> {
                    editViewModel?.playerController?.getDuration() ?: 0
                }
                lastTime >= clipStartDuration -> {
                    lastTime - clipStartDuration
                }
                else -> {
                    0
                }
            }
        }
    }

    @JvmStatic
    fun updateSetStartAndEndEnable(
        dataBindUtil: EditRecordLayoutBinding?,
        editViewModel: EditViewModel?
    ) {
        val clipEndDuration = dataBindUtil?.rulerView?.endRecord ?: -1
        if (clipEndDuration > 0) {
            setOpEnable(true, dataBindUtil, editViewModel)
        }
    }

    @JvmStatic
    fun setOpEnable(
        enable: Boolean,
        dataBindUtil: EditRecordLayoutBinding?,
        editViewModel: EditViewModel?
    ) {
        DebugUtil.d(TAG, "setOpEnable:$enable")
        updateLayoutState(true, dataBindUtil)
        updateLayoutState(enable, dataBindUtil)
        if (enable) {
            val clipStartDuration = dataBindUtil?.rulerView?.startRecord ?: 0
            val clipEndDuration = dataBindUtil?.rulerView?.endRecord ?: 0
            val selectDuration = clipEndDuration - clipStartDuration
            val duration = (editViewModel?.playerController?.getDuration() ?: 0)
            val clipOffsetWithDuration = duration - selectDuration
            DebugUtil.d(
                TAG,
                "clipStartDuration:$clipStartDuration  clipEndDuration:$clipEndDuration  selectDuration:$selectDuration"
            )
            DebugUtil.d(TAG, "duration:$duration  clipOffsetWithDuration:$clipOffsetWithDuration")
            if ((clipOffsetWithDuration < EditConstant.MIN_CLIP_TIME) || (selectDuration < EditConstant.MIN_CLIP_TIME)) {
                updateLayoutState(false, dataBindUtil)
            }
        }
    }

    @JvmStatic
    fun showInsufficientSpace(context: Context) {
        val hasSD = AddonAdapterCompatUtil.isExternalSdMounted(context)
        val hasInternal = AddonAdapterCompatUtil.isInternalSdMounted(context)
        if (hasSD || !hasInternal) {
            ToastManager.showLongToast(context, com.soundrecorder.common.R.string.sd_no_storage)
        } else {
            ToastManager.showLongToast(
                context,
                if (FeatureOption.IS_PAD) {
                    com.soundrecorder.common.R.string.device_no_storage
                } else {
                    com.soundrecorder.common.R.string.phone_no_storage
                }
            )
        }
    }

    @JvmStatic
    fun getTitle(progress: Int): String? {
        var title =
            BaseApplication.getAppContext().resources.getString(com.soundrecorder.common.R.string.record_clipping)
        var percent = "$progress%"
        if (BaseApplication.sIsRTLanguage) {
            percent = RtlUtils.addDirectionSymbolForRtl(percent).toString()
        }
        title += percent
        return title
    }

    @JvmStatic
    fun processTimerTextPadding(inputTextView: TextView?) {
        val isMy = Locale.getDefault().language == "my"
        if (isMy) {
            TimerTextViewPadingUtil.setFixTimeTopAndBottom(
                inputTextView,
                Constants.PLAYER_FIX_PADDING
            )
        } else {
            TimerTextViewPadingUtil.setCenterH(inputTextView)
        }
    }

    @JvmStatic
    private fun updateLayoutState(enable: Boolean, dataBindUtil: EditRecordLayoutBinding?) {
        dataBindUtil?.apply {
            extractIcon.isEnabled = enable
            extractText.isEnabled = enable
            extract.isEnabled = enable
            removeIcon.isEnabled = enable
            removeText.isEnabled = enable
            remove.isEnabled = enable
        }
    }

    @JvmStatic
    private fun asyncSaveCutRecords(
        mediaRecord: Record,
        resultName: String?,
        path: String?,
        mMarkDataList: MutableList<MarkDataBean>?,
        ampDataList: MutableList<Int>?
    ) {
        DebugUtil.i(TAG, "asyncSaveCutRecords start")
        val appContext = BaseApplication.getAppContext()
        val mRecordDBUtil = RecorderDBUtil.getInstance(appContext)
        val recordType = RecordModeUtil.getRecordTypeForMediaRecord(mediaRecord)
        val rowId = mediaRecord.id
        val mediaUri = MediaDBUtils.genUri(rowId)
        val ampString = (ampDataList ?: ArrayList()).joinToString(",", transform = Int::toString)
        val directRecordStatus = DirectRecordStatus(mediaRecord.directOn, mediaRecord.directTime)
        val currentGroupInfo = GroupInfoDbUtil.getGroupInfoForSaveRecordFile(MSG_ARG2_SAVE_RECORD_FROM_CLIP)
        if (BaseUtil.isAndroidQOrLater) {
            mRecordDBUtil.insertOrUpdateNewRecord(
                mediaUri,
                ampString,
                mMarkDataList,
                recordType,
                currentGroupInfo,
                true,
                directRecordStatus,
                null
            )
        } else {
            var lastIndex = -1
            if (path == null || path.lastIndexOf(".").also { lastIndex = it } == -1) {
                return
            }
            val suffix = path.substring(lastIndex)
            val file = File(File(path).parent, resultName + suffix)
            //标记table更新逻辑已经放入到下面的函数之中了，不需要单独更新mark表，删除下方逻辑
            mRecordDBUtil.insertOrUpdateNewRecord(file, ampString, mMarkDataList, recordType, currentGroupInfo, directRecordStatus)
        }
        cloudKitApi?.trigBackupNow(appContext)
        DebugUtil.i(TAG, "asyncSaveCutRecords end")
    }
}
