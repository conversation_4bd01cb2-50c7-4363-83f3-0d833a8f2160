/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PlaybackActivity
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: <PERSON>.Ren
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */
package com.soundrecorder.editrecord.ui

import android.content.res.Configuration
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.FileObserver
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.View.GONE
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.annotation.DimenRes
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.marginStart
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.progressbar.COUIHorizontalProgressBar
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.splitwindow.OnFragmentFoldStateChangedListener
import com.soundrecorder.base.userchange.OnFragmentUserChangeListener
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.getFloatValue
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.fileobserve.MultiFileObserver
import com.soundrecorder.common.fileobserve.OnFileEventListener
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.TimeSetUtils
import com.soundrecorder.common.utils.ViewUtils.addItemDecoration
import com.soundrecorder.common.utils.ViewUtils.doOnLayoutChange
import com.soundrecorder.common.utils.ViewUtils.fixTextFlash
import com.soundrecorder.common.utils.ViewUtils.updateConstraintPercentWidth
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.editrecord.R
import com.soundrecorder.editrecord.databinding.EditRecordLayoutBinding
import com.soundrecorder.editrecord.ui.animation.EditViewAnimationControl
import com.soundrecorder.editrecord.utils.EditViewUtils
import com.soundrecorder.editrecord.utils.EditViewUtils.handlerSavingHasMsgWhat
import com.soundrecorder.editrecord.views.preview.DragBar
import com.soundrecorder.editrecord.views.preview.GloblePreViewBar
import com.soundrecorder.editrecord.views.wave.HandlerMoveListener
import com.soundrecorder.editrecord.views.zoomwindow.ZoomWindowChangeObserver
import com.soundrecorder.player.TimerTickCallback
import com.soundrecorder.player.WavePlayerController
import com.soundrecorder.player.speaker.SpeakerReceiver
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.wavemark.mark.MarkListAdapter
import kotlin.math.min

class EditRecordFragment : Fragment(), View.OnCreateContextMenuListener,
    OnFragmentUserChangeListener,
    ZoomWindowChangeObserver.ZoomWindowChange, OnFileEventListener, OnBackPressedListener,
    OnFragmentFoldStateChangedListener {

    var mMarkListAdapter: MarkListAdapter? = null
    var dataBindUtil: EditRecordLayoutBinding? = null
    val mEditViewModel by activityViewModels<EditViewModel>()
    private val mMainHandler = Handler(Looper.getMainLooper())

    private var mIsShow = false
    private var mSaveMenu: MenuItem? = null
    private var mIsDestroying = false
    private var mClipProgressDialog: AlertDialog? = null
    private var mClipProgressDialogDismissListener: DialogDismissListener? = null
    private var isReadMarkTag = false
    private var mDialogStartTime = 0L
    private var mTimeTickCallback: TimerTickCallback? = null

    // 是否按下大波形起始线条
    private var isOnTouchHandler = false

    // 是否按下小波形左右把手
    private var isOnTouchDragBar = false

    // 记录按下把手之前的裁切终点时间
    private var mCutEndTimeBeforeTouchDownDragBar = 0L
    private var mNavigationDrawable: Drawable? = null
    private val viewAnimationControl: EditViewAnimationControl = EditViewAnimationControl()

    private val mPermissionGrantedListener = {
        DebugUtil.d(TAG, "onPermissionAllGranted")
        if (PermissionUtils.hasReadAudioPermission()) {
            val isNotPrepareAmplitudeAndMark =
                (mEditViewModel.isPrepareAmplitudeAndMark.value == false)
            DebugUtil.d(
                TAG,
                "isNotPrepareAmplitudeAndMark:$isNotPrepareAmplitudeAndMark  isReadMarkTag:$isReadMarkTag"
            )
            if (isNotPrepareAmplitudeAndMark && !isReadMarkTag) {
                DebugUtil.d(TAG, "mPermissionGrantedListener  readMarkTag")
                mEditViewModel.readMarkTag()
            }
        }
    }

    private val mPointMoveListener = object : GloblePreViewBar.PointMoveListener {
        private var isPauseWhenTouchDown = false // 记录按下把手的时候是否处于暂停状态

        /**
         * 把手 ACTION_DOWN
         */
        override fun onTouchDownOnBar(direction: Int, time: Long) {
            DebugUtil.i(TAG, "Fragment onTouchDownOnBar,direction=$direction, time=$time")
            isOnTouchDragBar = true
            mCutEndTimeBeforeTouchDownDragBar = mEditViewModel.cutEndTime.getValueWithDefault()
            isPauseWhenTouchDown = !mEditViewModel.playerController.isWholePlaying()
            // 暂停下拖动把手，进度跟随把手
            if (isPauseWhenTouchDown) {
                mEditViewModel.needSyncRulerView = true
                mEditViewModel.playerController.setOCurrentTimeMillis(time)
                dataBindUtil?.preViewBar?.setMiddleLineFollowDragBar(true)
            }
        }

        /**
         * 拖动左右把手
         */
        override fun onMoveOnDragBar(direction: Int, time: Long) {
            when (direction) {
                DragBar.DRAG_START -> mEditViewModel.cutStartTime.value = time
                DragBar.DRAG_END -> mEditViewModel.cutEndTime.value = time
            }
            // 暂停下拖动把手，进度跟随把手
            if (isPauseWhenTouchDown) {
                mEditViewModel.needSyncRulerView = true
                mEditViewModel.playerController.doSetOCurrentTimeMillis(time)
            }
            EditViewUtils.setOpEnable(true, dataBindUtil, mEditViewModel)
        }

        /**
         * 把手 ACTION_UP
         */
        override fun onTouchUpOnBar(direction: Int, time: Long) {
            DebugUtil.i(
                TAG,
                "onTouchUpOnBar,direction=$direction, time=$time,isPauseWhenTouchDown=$isPauseWhenTouchDown"
            )
            val currentMill = mEditViewModel.getCurrentTime()
            when (direction) {
                DragBar.DRAG_START -> {
                    mEditViewModel.cutStartTime.value = time
                    // 进度位置小于起点位置，更正为起点位置
                    if (currentMill < time) {
                        mEditViewModel.needSyncRulerView = true
                        mEditViewModel.playerController.seekTime(time)
                    }
                    CuttingStaticsUtil.doCutStart()
                }

                DragBar.DRAG_END -> {
                    mEditViewModel.cutEndTime.value = time
                    // 进度位置大于终点位置，更正为终点位置
                    if (currentMill > time) {
                        mEditViewModel.needSyncRulerView = true
                        mEditViewModel.playerController.seekTime(time)
                    }
                    CuttingStaticsUtil.doCutEnd()
                }
            }
            // 暂停下拖动把手，进度跟随把手
            if (isPauseWhenTouchDown) {
                mEditViewModel.needSyncRulerView = true
                mEditViewModel.playerController.seekTime(time)
                dataBindUtil?.preViewBar?.setMiddleLineFollowDragBar(false)
            }
            isOnTouchDragBar = false
        }

        /**
         * 进度红线ACTION_DOWN
         */
        override fun onTouchDownMiddleBar() {
            mEditViewModel.playerController.onStartTouchSeekBar()
            if (mEditViewModel.playerController.isWholePlaying()) {
                mEditViewModel.playerController.stopTimerNow()
                waveStopMove()
            }
        }

        /**
         * 拖动进度红线
         */
        override fun onMoveOnMiddleBar(currentTime: Long) {
            mEditViewModel.needSyncRulerView = true
            mEditViewModel.playerController.setOCurrentTimeMillis(currentTime)
        }

        /**
         * 进度红线ACTION_UP
         */
        override fun onTouchUpMiddleBar(currentTime: Long) {
            mEditViewModel.playerController.onStopTouchSeekBar()
            mEditViewModel.needSyncRulerView = true
            mEditViewModel.playerController.seekTime(currentTime)
            //按下之前是播放状态，松手后也应该继续播放。
            mEditViewModel.playerController.onResetPlayState()
            CuttingStaticsUtil.addTrimDragPreview()
        }
    }

    private fun waveStartMove() {
        dataBindUtil?.rulerView?.let { rulerView ->
            rulerView.setSelectTime(mEditViewModel.getCurrentTime())
            rulerView.setIsCanScrollTimeRuler(true)
        }
    }

    private fun waveStopMove() {
        dataBindUtil?.rulerView?.let { rulerView ->
            rulerView.setSelectTime(mEditViewModel.getCurrentTime())
            rulerView.stopScroll()
            if (!isOnTouchHandler) {
                rulerView.setIsCanScrollTimeRuler(true)
            }
        }
    }

    private fun waveStopMoveForEnd() {
        if (!isOnTouchHandler) {
            dataBindUtil?.rulerView?.setIsCanScrollTimeRuler(true)
        }
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        when (it) {
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> {
                waveStopMove()
                dataBindUtil?.redCircleIcon?.switchPlayState()
                // 播放域为裁切区域，超过该区域自动暂停，若暂停后若获取时间大于裁切结束时间，则矫正播放时间
                val endTime =
                    if (isOnTouchDragBar) mCutEndTimeBeforeTouchDownDragBar else mEditViewModel.cutEndTime.getValueWithDefault()
                if (mEditViewModel.getCurrentTime() > endTime) {
                    mEditViewModel.needSyncRulerView = true
                    mEditViewModel.playerController.seekTime(endTime)
                }
            }

            PlayStatus.PLAYER_STATE_PLAYING -> {
                if (mEditViewModel.playerController.mIsTouchSeekbar.value != true) {
                    waveStartMove()
                }
                dataBindUtil?.redCircleIcon?.switchPauseState()
            }

            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                if (mEditViewModel.playerController.mIsTouchSeekbar.value != true) {
                    waveStartMove()
                }
                dataBindUtil?.redCircleIcon?.switchPauseState()
            }

            PlayStatus.PLAYER_STATE_HALTON -> {
                waveStopMoveForEnd()
                dataBindUtil?.redCircleIcon?.switchPlayState()
            }
        }
    }

    private val mRecordControlOnClickListener = View.OnClickListener { v ->
        if (EditViewModel.isQuickClick) {
            return@OnClickListener
        }
        when (v.id) {
            R.id.red_circle_icon -> {
                mEditViewModel.playBtnClick()
                PermissionUtils.checkNotificationPermission(activity)
            }

            else -> {
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dataBindUtil =
            DataBindingUtil.inflate(inflater, R.layout.edit_record_layout, container, false)
        DebugUtil.i(
            TAG,
            " onCreateView  ----------(null == rootView):${(null == dataBindUtil?.root)} mEditViewModel $mEditViewModel ------------"
        )
        dataBindUtil?.editViewModel = mEditViewModel
        mTimeTickCallback = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                DebugUtil.d(TAG, "refreshTimerTick: newTime = $timeTickMillis")
                val realTime = min(timeTickMillis, mEditViewModel.playerController.getDuration())
                dataBindUtil?.rulerView?.startSmoothScroll(realTime)
                mEditViewModel.onTimerRefreshTime(timeTickMillis)
            }
        }
        mEditViewModel.playerController.addTimerTickListener(mTimeTickCallback)
        dataBindUtil?.recordControlOnClickListener = mRecordControlOnClickListener
        return dataBindUtil?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        init(savedInstanceState)
        initiateWindowInsets()
        initUiStatusObsers()
        initHoverView(null)
        if (dataBindUtil?.markListview != null && dataBindUtil?.editMarkListFooterDivider != null) {
            dataBindUtil?.markListview?.viewTreeObserver?.addOnDrawListener {
                dataBindUtil?.editMarkListFooterDivider?.let { divider ->
                    divider.visibility =
                        if (calcFooterDividerVisibility(dataBindUtil?.markListview)) VISIBLE else View.INVISIBLE
                }
            }
        }
    }

    private fun calcFooterDividerVisibility(markListView: COUIRecyclerView?): Boolean {
        if (markListView == null) {
            return false
        }

        val llm: LinearLayoutManager =
            (markListView.layoutManager as? LinearLayoutManager) ?: return false

        val adapter = markListView.adapter ?: return false

        val totalItemCount = adapter.itemCount
        if (totalItemCount <= 0) {
            return false
        }

        val lastVisibleItemPosition = llm.findLastVisibleItemPosition()
        if (lastVisibleItemPosition < (totalItemCount - 1)) {
            return true
        }

        if (lastVisibleItemPosition > (totalItemCount - 1)) {
            return false
        }

        val lastVisibleView = llm.findViewByPosition(lastVisibleItemPosition)
        return (lastVisibleView != null
                && lastVisibleView.bottom > markListView.height)
    }

    private fun initHoverView(sysBarHeight: Int?) {
        val viewCenterDiver = dataBindUtil?.viewCenterDivider
        viewCenterDiver?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            // 状态栏+导航栏高度
            val realHeight =
                sysBarHeight ?: (StatusBarUtil.getStatusBarHeight(viewCenterDiver.context) +
                        StatusBarUtil.getNavigationBarHeight(viewCenterDiver.context))
            // 计算偏移量： （状态栏+导航栏）/2
            topMargin = realHeight / 2
        }
    }

    private fun initUiStatusObsers() {
        ResponsiveUIConfig.getDefault(this.context).uiStatus.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "onUI config change to $it")
            resetLayoutForConfigChanged()
            if (EditViewUtils.isSaveClipDialogIsShowing()) {
                // 缓存保存弹窗状态、内容
                cacheSaveClipDialog()
                // 释放保存弹窗
                EditViewUtils.releaseSaveDialog()
                // 重新显示保存弹窗
                createSaveClipDialog(true)
            }
        }
    }

    fun init(bundle: Bundle?) {
        (activity as? PermissionActivity)?.setPermissionGrantedListener(mPermissionGrantedListener)

        mEditViewModel.arguments = arguments
        if (bundle == null) {
            EditViewUtils.cancelNotificationModeAndGroup(mEditViewModel)
        }

        initView()
        dataBindUtil?.body?.visibility = View.GONE
        if (BaseApplication.sIsRTLanguage) {
            dataBindUtil?.preViewBar?.reverseLayout = true
        }
        dataBindUtil?.preViewBar?.setListenter(mPointMoveListener)
        initActionBar()
        initWaveView()
        TimeSetUtils(viewLifecycleOwner) {
            if (mEditViewModel.playerController.isWholePlaying()) {
                mEditViewModel.playerController.startTimerNow("doTimeSet")
                dataBindUtil?.rulerView?.let { rulerView ->
                    rulerView.setIsCanScrollTimeRuler(true)
                    rulerView.setSelectTime(mEditViewModel.playerController.currentTimeMillis.getValueWithDefault())
                }
            }
        }
        initClipView()
        DebugUtil.d(TAG, "----------onCreateView-(bundle == null):${bundle == null}------------")
        val isPermission = PermissionUtils.hasReadAudioPermission()
        if ((bundle == null) && isPermission) {
            isReadMarkTag = true
            mEditViewModel.readMarkTag()
        }
        SpeakerReceiver.getInstance().setToDoInSpeakerReceiver(mEditViewModel.playerController)
        initViewModel()

        MultiFileObserver.getInstance().addFileEventListener(this)

        restoreDialog()
    }

    private fun initClipView() {
        EditViewUtils.setOpEnable(false, dataBindUtil, mEditViewModel)
    }

    override fun onDestroy() {
        DebugUtil.e(TAG, "onDestroy ")
        mIsDestroying = true
        dataBindUtil?.rulerView?.run {
            setDragListener(null)
            onTouchDownOnWaveBar = null
            onTouchUpOnWaveBar = null
        }

        dataBindUtil?.colorLoadView?.visibility = View.GONE
        if (mClipProgressDialogDismissListener != null) {
            mClipProgressDialogDismissListener?.release()
            mClipProgressDialogDismissListener = null
        }

        EditViewUtils.releaseCancelDialog()
        cacheSaveClipDialog()
        EditViewUtils.releaseSaveDialog()
        cacheSavingDialog()
        EditViewUtils.releaseSaveLoadingDialog()
        EditViewUtils.removeCallbackForSavingDialog()

        mMainHandler.removeCallbacksAndMessages(null)

        if (activity?.isFinishing == true) {
            mEditViewModel.onActivityFinish()
        }
        super.onDestroy()
    }

    /**
     * 保存保存弹窗显示状态及输入信息
     */
    private fun cacheSaveClipDialog() {
        mEditViewModel.cacheSaveDialogName = EditViewUtils.getSaveClipDialogInputText()
        mEditViewModel.isShowSaveDialog.value = EditViewUtils.isSaveClipDialogIsShowing()
    }

    /**
     * 保存“保存中...”弹窗显示状态
     */
    private fun cacheSavingDialog() {
        // 在100毫秒内重建了，isSavingDialogIsShowing就会为false，但是msg已经发出来了，实际上是需要显示dialog的
        mEditViewModel.isShowSavingDialog.value =
            EditViewUtils.isSavingDialogIsShowing() || handlerSavingHasMsgWhat()
    }

    override fun onPause() {
        mIsShow = false
        super.onPause()
    }

    override fun onResume() {
        mIsShow = true
        super.onResume()
        SpeakerReceiver.getInstance().setToDoInSpeakerReceiver(mEditViewModel.playerController)
        // 临时规避#4501003,造成该问题原有是由于waveStartMove中调用setSelectTime导致最后一次波形滚动被拦截未执行
        if (mEditViewModel.playerController.playerState.value == PlayStatus.PLAYER_STATE_HALTON) {
            dataBindUtil?.rulerView?.setSelectTime(mEditViewModel.getCurrentTime())
        }
    }

    /**
     * 裁切界面中分屏改为不重建的方式，上下调整分屏窗口大小，
     * 一直会回调onConfigurationChange的回调，在这里动态调用windowInsetChange的处理
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        DebugUtil.i(TAG, "onConfigurationChanged $newConfig")
        activity?.let {
            dataBindUtil?.rulerView?.resetWaveData()
            initWaveView()
            dataBindUtil?.rulerView?.setSelectTime(mEditViewModel.getCurrentTime())
        }
        resetLayoutForConfigChanged()
        initView()
        initCutTime(mEditViewModel.playerController.getDuration())
        super.onConfigurationChanged(newConfig)
        ResponsiveUIConfig.getDefault(this.context).onActivityConfigChanged(newConfig)
    }

    /**
     * 这里的代码是为了解决一个问题，从分屏模式下进入裁切界面，播放音频过程中，退出分屏模式，大的波形图会快速滑动到当前播放的位置
     * 原因是由于之前的大波形一直没有显示过，第一次显示的时候WaveRecyclerView中的getFirstVisiblePosition这个始终为0导致，修改方案这里人为更新一下时间节点
     */
    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        DebugUtil.i(TAG, "onMultiWindowModeChanged $isInMultiWindowMode")
        if (!isInMultiWindowMode) {
            dataBindUtil?.rulerView?.setSelectTime(
                mEditViewModel.playerController.currentTimeMillis.getValueWithDefault()
            )
        }
        super.onMultiWindowModeChanged(isInMultiWindowMode)
    }

    /**
     * 1.重建或切换分屏，需通过 Livedata(cutStartTime,cutEndTime) 重新设置把手当前位置对应的时间
     * 2.关闭所有文件管理权限，cutStartTime 为 null，设为初始状态
     */
    private fun initCutTime(duration: Long) {
        if (duration == 0L) {
            return
        }
        if (mEditViewModel.cutStartTime.value == null) {
            mEditViewModel.cutStartTime.value = 0
        }
        if (mEditViewModel.cutEndTime.value == null) {
            mEditViewModel.cutEndTime.value = duration
        }
    }

    private fun initViewModel() {
        /**
         * 获取 mDuration 后，需设置初始时间，以显示蒙层
         */
        mEditViewModel.playerController.mDuration.observe(viewLifecycleOwner) { duration ->
            initCutTime(duration)
        }

        mEditViewModel.cutStartTime.observe(viewLifecycleOwner) {
            EditViewUtils.updateSetStartAndEndEnable(dataBindUtil, mEditViewModel)
            dataBindUtil?.rulerView?.setCutStartTime(it)
            dataBindUtil?.preViewBar?.setCutStartTime(it)
        }

        mEditViewModel.cutEndTime.observe(viewLifecycleOwner) {
            EditViewUtils.updateSetStartAndEndEnable(dataBindUtil, mEditViewModel)
            dataBindUtil?.rulerView?.setCutEndTime(it)
            dataBindUtil?.preViewBar?.setCutEndTime(it)
        }

        mEditViewModel.clipTaskViewModel.dialogStatus.observe(
            viewLifecycleOwner,
            Observer { dialogStatus ->
                when (dialogStatus) {
                    ClipTaskViewModel.CLIP_SHOW_DIALOG -> {
                        DebugUtil.i(TAG, "CLIP_SHOW_DIALOG")
                        if (this.activity == null) {
                            return@Observer
                        }
                        if (this.activity?.isDestroyed != false || this.activity?.isFinishing != false) {
                            return@Observer
                        }
                        val createProgressDialog =
                            EditViewUtils.createProgressDialog(this, clickCancel = {
                                mEditViewModel.mClickCancel = true
                                mEditViewModel.cancelClipTask(true)
                            })
                        mClipProgressDialog = createProgressDialog?.first
                        mClipProgressDialogDismissListener = createProgressDialog?.second
                        mClipProgressDialog?.show()
                        mDialogStartTime = SystemClock.elapsedRealtime()
                    }

                    ClipTaskViewModel.CLIP_ERROR_DIALOG -> {
                        this.context?.let(EditViewUtils::showInsufficientSpace)
                    }

                    ClipTaskViewModel.CLIP_DISMISS_DIALOG -> {
                        val actualTime = SystemClock.elapsedRealtime() - mDialogStartTime
                        if (actualTime > DIALOG_MIN_TIME) {
                            mClipProgressDialog?.dismiss()
                            mClipProgressDialog = null
                        } else {
                            mMainHandler.postDelayed({
                                mEditViewModel.clipTaskViewModel.dialogStatus.postValue(
                                    ClipTaskViewModel.CLIP_DISMISS_DIALOG
                                )
                            }, DIALOG_MIN_TIME - actualTime)
                        }
                    }
                }
            })

        mEditViewModel.clipTaskViewModel.dialogProgress.observe(viewLifecycleOwner) { clipProgress ->
            mClipProgressDialog?.let {
                it.findViewById<COUIHorizontalProgressBar>(com.support.dialog.R.id.progress)?.progress =
                    clipProgress
                it.setTitle(clipProgress?.let(EditViewUtils::getTitle))
            }
        }

        mEditViewModel.playerController.mDuration.observe(viewLifecycleOwner) {
            dataBindUtil?.preViewBar?.totalTime = it

            dataBindUtil?.rulerView?.let { rulerView ->
                val currentTimeMillis =
                    mEditViewModel.playerController.currentTimeMillis.getValueWithDefault()
                DebugUtil.d(
                    TAG,
                    "startplay total time = $it ,mCurrentTimeMillis = $currentTimeMillis"
                )
                rulerView.totalTime = it
                rulerView.setSelectTime(currentTimeMillis)
            }
        }

        mEditViewModel.playerController.playerState.observeForever(mPlayStateChangeObserver)
        mEditViewModel.markDataList.observe(viewLifecycleOwner) { markData ->
            DebugUtil.e(TAG, "mMarkDataList size is${markData?.size}")
            mMarkListAdapter?.setData(markData)
            dataBindUtil?.rulerView?.setMarkTimeList(markData)
        }
        mEditViewModel.ampList.observe(viewLifecycleOwner) { ampList ->
            hideLoadingWithoutStartPlay()
            dataBindUtil?.rulerView?.setAmplitudeList(ampList)
            dataBindUtil?.preViewBar?.amplitudes = ArrayList(ampList)
        }
        mEditViewModel.playerController.currentTimeMillis.observe(viewLifecycleOwner) { time ->
            DebugUtil.d(
                TAG,
                "currentTimeMillisObserver: curTime = $time, needSync=${mEditViewModel.needSyncRulerView}}"
            )
            if (mEditViewModel.needSyncRulerView) {
                mEditViewModel.needSyncRulerView = false
                dataBindUtil?.rulerView?.setSelectTime(time)
            }
            EditViewUtils.updateSetStartAndEndEnable(dataBindUtil, mEditViewModel)
            setCurrentTime(time)
            dataBindUtil?.preViewBar?.setPointTime(time, false)
            // 播放域为裁切范围，若播放超过裁切区域，则暂停播放
            val endTime =
                if (isOnTouchDragBar) mCutEndTimeBeforeTouchDownDragBar else mEditViewModel.cutEndTime.getValueWithDefault()
            if ((mEditViewModel.playerController.isWholePlaying()) && (mEditViewModel.getCurrentTime() >= endTime)) {
                mEditViewModel.playerController.pausePlay()
            }
        }
        mEditViewModel.isPrepareAmplitudeAndMark.observe(viewLifecycleOwner) {
            dataBindUtil?.rulerView?.post {
                onAmpLoadingFinish()
            }
        }
        mEditViewModel.isClipped.observe(viewLifecycleOwner) {
            mSaveMenu?.isEnabled = it
        }

        mEditViewModel.playerController.lastPlayStartTime.observe(viewLifecycleOwner) { time ->
            dataBindUtil?.rulerView?.setLastPlayTime(time)
        }
    }

    fun setTotalTime(duration: Long) {
        dataBindUtil?.cutTotalTime?.let {
            it.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(duration)
            it.text = duration.durationInMsFormatTimeExclusive(true)
        }
    }

    fun setCurrentTime(currentTimeMillis: Long) {
        setTimerTextViewText(currentTimeMillis)
        dataBindUtil?.currentTime?.let {
            it.contentDescription =
                TimeUtils.getContentDescriptionForTimeDuration(currentTimeMillis)
            it.text = currentTimeMillis.durationInMsFormatTimeExclusive()
        }
    }

    private fun onAmpLoadingFinish() {
        val duration = mEditViewModel.playerController.getDuration()
        val currentTimeMillis =
            mEditViewModel.playerController.currentTimeMillis.getValueWithDefault()
        val ampList = mEditViewModel.ampList.value
        DebugUtil.d(TAG, "onAmpLoadingFinish:$duration, amplistsize ${ampList?.size}")

        dataBindUtil?.rulerView?.let {
            it.setAmplitudeList(mEditViewModel.ampList.value)
            it.totalTime = duration
        }
        if (currentTimeMillis in 0 until duration) {
            DebugUtil.i(
                TAG,
                "onAmpLoadingFinish mWaveRecyclerView set select time $currentTimeMillis + duraton : $duration"
            )
            dataBindUtil?.rulerView?.setSelectTime(currentTimeMillis)
        }
        dataBindUtil?.preViewBar?.totalTime = duration
        dataBindUtil?.preViewBar?.setSelectTime(currentTimeMillis)
        dataBindUtil?.preViewBar?.amplitudes = ampList

        setTotalTime(duration)
    }

    private fun initView() {
        dataBindUtil?.redCircleIcon?.accessibilityDelegate =
            EditViewUtils.middleControlImageViewAccessibilityDelegate(
                mEditViewModel,
                dataBindUtil?.redCircleIcon
            )
        dataBindUtil?.timerView?.apply {
//            typeface = FontUtils.getOpposansNumberFontRegular(RecorderApplication.getAppContext())
            accessibilityDelegate = EditViewUtils.timerTextViewAccessibilityDelegate(mEditViewModel)
            setCurrentTime(mEditViewModel.playerController.currentTimeMillis.getValueWithDefault())
        }
        dataBindUtil?.recordName?.text = mEditViewModel.playName.value.title()

        mMarkListAdapter = MarkListAdapter(activity, false, false, false, false).also {
            it.setOnMarkClickListener(object : MarkListAdapter.OnMarkClickListener {
                override fun onMarkClick(data: MarkDataBean) {
                    dataBindUtil?.rulerView?.stopScroll()
                    mEditViewModel.onMarkClicked(data)
                }
            })
            it.setData(mEditViewModel.markDataList.value)
        }
        dataBindUtil?.markListview?.let {
            it.layoutManager = LinearLayoutManager(activity)
            it.adapter = mMarkListAdapter
            it.addItemDecoration(com.soundrecorder.common.R.dimen.card_margin_top_buttom)
        }

        dataBindUtil?.body?.doOnLayoutChange { _, _, _ ->
            DebugUtil.i(TAG, "doOnLayoutChange invoke checkNeedHideOtherView")
            correctViewSpaceInDifferentWindow()
            checkNeedHideOtherView()
            setTimerTextViewText(mEditViewModel.playerController.currentTimeMillis.getValueWithDefault())
        }
    }

    private fun correctViewSpaceInDifferentWindow() {
        // 底部红色按钮距离底部间距
        dataBindUtil?.middleControl?.run {
            updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin =
                    resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_margin_bottom)
                        .toInt()
                val newWidth =
                    resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam)
                        .toInt()
                if (width != newWidth) {
                    width = newWidth
                    height = newWidth
                    // 宽高改变了，相关按钮大小也要更新
                    dataBindUtil?.redCircleIcon?.refreshCircleRadius(
                        resources.getDimension(
                            com.soundrecorder.common.R.dimen.circle_record_button_radius
                        )
                    )
                    dataBindUtil?.extractIcon?.setImageResource(R.drawable.selector_button_edit_trim)
                    dataBindUtil?.removeIcon?.setImageResource(R.drawable.selector_button_edit_remove)
                }
            }
        }
        // 时间区域左右间距
        dataBindUtil?.animTitle?.run {
            val marginHorizontal =
                resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.common_time_area_margin_horizontal)
            if (marginHorizontal != marginStart) {
                updateLayoutParams<ConstraintLayout.LayoutParams> {
                    marginStart = marginHorizontal
                    marginEnd = marginHorizontal
                }
            }
        }
        //标记左右间距
        dataBindUtil?.layoutMarklist?.run {
            val marginHorizontal =
                resources.getDimension(com.soundrecorder.common.R.dimen.common_mark_list_margin_horizontal_small_window)
                    .toInt()
            updatePadding(left = marginHorizontal, right = marginHorizontal)
            updateConstraintPercentWidth(context.getFloatValue(com.soundrecorder.common.R.dimen.screen_width_percent))
        }
        // 小波形间距、padding
        dataBindUtil?.preViewBarAndCutLayout?.run {
            updateConstraintPercentWidth(context.getFloatValue(com.soundrecorder.common.R.dimen.screen_width_percent))
            val paddingHorizontal =
                resources.getDimension(R.dimen.edit_preview_bar_padding_horizontal).toInt()
            updatePadding(left = paddingHorizontal, right = paddingHorizontal)
        }
        val btnMiddleMargin =
            resources.getDimension(R.dimen.edit_delete_extract_margin_start_margin_edn).toInt()
        // 删除按钮间距
        dataBindUtil?.extract?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            if (btnMiddleMargin != marginStart) {
                marginStart = btnMiddleMargin
            }
        }
        // 提取按钮间距
        dataBindUtil?.remove?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            if (btnMiddleMargin != marginEnd) {
                marginEnd = btnMiddleMargin
            }
        }
    }

    private fun resetLayoutForConfigChanged() {
        DebugUtil.d(TAG, "当config变化时重新设置布局宽高间距")
        dataBindUtil?.root?.requestLayout()
    }

    private fun initWaveView() {
        mEditViewModel.needSyncRulerView = true
        dataBindUtil?.rulerView?.let { rulerView ->
            rulerView.setAmplitudeList(mEditViewModel.ampList.value)
            rulerView.setMaxAmplitudeSource(mEditViewModel.maxAmplitudeSource)
            rulerView.setDragListener(mEditViewModel.mDragListener)
            dataBindUtil?.preViewBar?.invalidate()
            //onConfigurationChange 调用时需要设置时间，不然绘制会导致不显示item, item的宽度为0
            mEditViewModel.playerController.getDuration().let {
                if (it > 0) {
                    rulerView.totalTime = it
                }
            }
            rulerView.handlerMoveListener = object : HandlerMoveListener {
                override fun onStartMove(time: Long) {
                    mEditViewModel.cutStartTime.postValue(time)
                }

                override fun onEndMove(time: Long) {
                    mEditViewModel.cutEndTime.postValue(time)
                }
            }
            rulerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                var isOverSelectTimeWhenDrag = false // 拖拽波形是否超出选择范围

                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    DebugUtil.i(
                        TAG,
                        "onScrollChanged, newState=$newState,isOverChooseTime=$isOverSelectTimeWhenDrag"
                    )
                    if (isOnTouchHandler) return
                    when (newState) {
                        RecyclerView.SCROLL_STATE_DRAGGING -> {
                            mEditViewModel.playerController.stopTimerNow()
                            mEditViewModel.playerController.scrollFromType =
                                WavePlayerController.SCROLL_FROM_DRAG
                            //手拖动占据控制权后，设置preTimeMillis = -1,恢复后startSmoothScrollBy当次不处理
                            rulerView.setPreTimeMillis(-1)
                        }

                        RecyclerView.SCROLL_STATE_IDLE -> {
                            if (mEditViewModel.playerController.scrollFromType == WavePlayerController.SCROLL_FROM_DRAG) {
                                var curTime = rulerView.getSlideTime("onScrolledChanged idle")
                                if (curTime > mEditViewModel.playerController.getDuration()) {
                                    curTime = mEditViewModel.playerController.getDuration()
                                }
                                if (curTime >= 0) {
                                    val startTime = rulerView.startRecord ?: 0
                                    val endTime = rulerView.endRecord ?: 0
                                    DebugUtil.i(
                                        TAG,
                                        "onScrollStateChanged curTime = $curTime, startTime=$startTime, endTime=$endTime"
                                    )
                                    correctCurrentMillBySelectTime(
                                        curTime,
                                        startTime,
                                        endTime
                                    ) { lessStartTime, overEndTime, newTime: Long ->
                                        mEditViewModel.needSyncRulerView =
                                            lessStartTime || overEndTime
                                        mEditViewModel.playerController.seekTime(newTime)
                                    }

                                    mEditViewModel.playerController.scrollFromType =
                                        WavePlayerController.SCROLL_FROM_DEFAULT
                                    if ((curTime < mEditViewModel.playerController.getDuration())
                                        && mEditViewModel.playerController.isWholePlaying()
                                    ) {
                                        mEditViewModel.playerController.startTimerAsync("onScrollStateChanged")
                                    }
                                }
                            }
                            isOverSelectTimeWhenDrag = false // 重置该标志
                        }

                        RecyclerView.SCROLL_STATE_SETTLING -> {
                            if (isOverSelectTimeWhenDrag) {
                                // drag 滚动超过选择范围，停止滚动
                                dataBindUtil?.rulerView?.stopScroll()
                            }
                        }
                    }
                }

                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    if (mEditViewModel.playerController.scrollFromType != WavePlayerController.SCROLL_FROM_DRAG) {
                        //onScrolled, is auto scrolling, just return
                        if (!isOnTouchHandler && !isOnTouchDragBar && (dx != 0)) {
                            // drag执行IDLE后，还会执行onScrolled，所以在此拦截
                            val curTime = rulerView.getSlideTime("onScrolledNotDrag")
                            val startTime = rulerView.startRecord ?: 0
                            val endTime = rulerView.endRecord ?: 0
                            DebugUtil.i(
                                TAG,
                                "onScrolledNotFromDrag curTime = $curTime, startTime=$startTime, endTime=$endTime"
                            )
                            if (curTime >= 0) {
                                correctCurrentMillBySelectTime(
                                    curTime,
                                    startTime,
                                    endTime
                                ) { lessStartTime, overEndTime, newTime: Long ->
                                    if (lessStartTime || overEndTime) {
                                        rulerView.stopScroll() // 超出范围停止滚动
                                        mEditViewModel.needSyncRulerView = true
                                        mEditViewModel.playerController.setOCurrentTimeMillis(
                                            newTime
                                        )
                                    }
                                }
                            }
                        }
                        return
                    }
                    if (dx != 0) {
                        val curTime = rulerView.getSlideTime("onScrolled")
                        val startTime = rulerView.startRecord ?: 0
                        val endTime = rulerView.endRecord ?: 0
                        DebugUtil.i(
                            TAG,
                            "onScrolled = curTime = $curTime, startTime=$startTime, endTime=$endTime,isOnTouchHandler=$isOnTouchHandler"
                        )
                        if (curTime >= 0) {
                            if (isOnTouchHandler) {
                                mEditViewModel.needSyncRulerView = false
                                mEditViewModel.playerController.setOCurrentTimeMillis(curTime)
                                isOverSelectTimeWhenDrag = false
                            } else {
                                correctCurrentMillBySelectTime(
                                    curTime,
                                    startTime,
                                    endTime
                                ) { lessStartTime, overEndTime, newTime: Long ->
                                    if (lessStartTime || overEndTime) {
                                        if (rulerView.scrollState == RecyclerView.SCROLL_STATE_SETTLING) {
                                            rulerView.stopScroll()
                                        }
                                        mEditViewModel.needSyncRulerView = true
                                        isOverSelectTimeWhenDrag = true // 标记drag超出选择蓝色区域
                                    } else {
                                        isOverSelectTimeWhenDrag = false
                                        mEditViewModel.needSyncRulerView = false
                                    }
                                    mEditViewModel.playerController.setOCurrentTimeMillis(newTime)
                                }
                            }
                        }
                    }
                }
            })

            rulerView.onTouchDownOnWaveBar = {
                DebugUtil.i(TAG, "onTouchDownOnWaveBar")
                isOnTouchHandler = true
                mEditViewModel.playerController.onStartTouchSeekBar()
                mEditViewModel.playerController.pausePlay()
                mEditViewModel.playerController.scrollFromType =
                    WavePlayerController.SCROLL_FROM_DRAG
                rulerView.setPreTimeMillis(-1)
            }
            rulerView.onTouchUpOnWaveBar = {
                DebugUtil.i(TAG, "onTouchUpOnWaveBar")
                isOnTouchHandler = false
                var curTime = rulerView.getSlideTime("onTouchUpOnWaveBar")
                if (curTime > mEditViewModel.playerController.getDuration()) {
                    curTime = mEditViewModel.playerController.getDuration()
                }
                if (curTime < 0) {
                    curTime = 0
                }
                val startCurTime = rulerView.startRecord ?: 0
                val endCutTime = rulerView.endRecord ?: 0
                correctCurrentMillBySelectTime(
                    curTime,
                    startCurTime,
                    endCutTime
                ) { lessStartTime, overEndTime, newTime: Long ->
                    mEditViewModel.needSyncRulerView = lessStartTime || overEndTime
                    mEditViewModel.playerController.onStopTouchSeekBar()
                    mEditViewModel.playerController.seekTime(newTime)
                }
                mEditViewModel.playerController.onResetPlayState()
                mEditViewModel.playerController.scrollFromType =
                    WavePlayerController.SCROLL_FROM_DEFAULT
                if (mEditViewModel.playerController.isWholePlaying()) {
                    mEditViewModel.playerController.startTimerAsync("onTouchUpOnWaveBar")
                }
            }
        }
    }

    private fun correctCurrentMillBySelectTime(
        curTime: Long,
        startTime: Long,
        endTime: Long,
        func: ((lessStartTime: Boolean, overEndTime: Boolean, newTime: Long) -> Unit)
    ) {
        if (curTime < startTime) {
            func.invoke(true, false, startTime)
        } else if (curTime > endTime) {
            func.invoke(false, true, endTime)
        } else {
            func.invoke(false, false, curTime)
        }
    }

    private fun initActionBar() {
        val toolbar = dataBindUtil?.toolbar
        toolbar?.inflateMenu(R.menu.menu_edit_record)
        mSaveMenu = dataBindUtil?.toolbar?.menu?.findItem(R.id.save)
        toolbar?.setTitle(com.soundrecorder.common.R.string.cut_title_new)
        toolbar?.isTitleCenterStyle = true
        toolbar?.setOnMenuItemClickListener { menu ->
            if (ClickUtils.isQuickClick()) {
                return@setOnMenuItemClickListener false
            }
            when (menu.itemId) {
                R.id.cancel -> {
                    createCancelClipDialog()
                    CuttingStaticsUtil.addCutTrimCancle(RecorderUserAction.VALUE_TRIM_CANCLE)
                }

                R.id.save -> {
                    createSaveClipDialog(false)
                    CuttingStaticsUtil.addCutTrimMenuSave()
                    return@setOnMenuItemClickListener false
                }
            }
            return@setOnMenuItemClickListener false
        }
    }

    private fun finish() {
        activity?.finish()
    }

    override fun onFileObserver(event: Int, path: String?, allPath: String?) {
        when (event) {
            FileObserver.MOVED_FROM, FileObserver.DELETE,
            FileObserver.DELETE_SELF, FileObserver.MOVE_SELF -> {
                if (!mIsShow) {
                    DebugUtil.v(TAG, "onFileObserver event=$event Need ReQuerryCursor! path:$path")
                    val playPath = mEditViewModel.playPath
                    if (!allPath.isNullOrEmpty() && !playPath.isNullOrEmpty() && playPath.startsWith(
                            allPath
                        )
                    ) {
                        DebugUtil.d(TAG, "isDeletingFile player.pause")
                        mEditViewModel.playName.value = null
                        mEditViewModel.playerController.releasePlay()
                        activity?.setResult(Constants.RESULT_CODE_FILEOBSERVER_FINISH)
                        finish()
                    }
                }
            }
        }
    }

    private fun hideLoadingWithoutStartPlay() {
        if (!TextUtils.isEmpty(mEditViewModel.playPath)) {
            dataBindUtil?.colorLoadView?.visibility = GONE
            dataBindUtil?.body?.visibility = VISIBLE
        }
    }

    private fun createCancelClipDialog() {
        mEditViewModel.cacheSaveDialogName = null
        EditViewUtils.releaseCancelDialog()
        EditViewUtils.createCancelClipDialog(
            mEditViewModel.isClipped.value
                ?: false,
            activity,
            mEditViewModel.playName.value,
            mEditViewModel.markDataList.value,
            mEditViewModel
        )
    }

    private fun createSaveClipDialog(isRestore: Boolean) {
        if (!isRestore) {
            mEditViewModel.cacheSaveDialogName = null
        }
        EditViewUtils.createSaveClipDialog(
            isRestore,
            activity,
            mEditViewModel.playName.value,
            mEditViewModel.cacheSaveDialogName,
            mEditViewModel.markDataList.value,
            mEditViewModel
        )
    }

    private fun restoreDialog() {
        if (mEditViewModel.isShowSaveDialog.value == true) {
            createSaveClipDialog(true)
        }

        if (mEditViewModel.isShowSavingDialog.value == true) {
            EditViewUtils.showSaveLoadingDialog(activity)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        releaseMarks()
        stopObserver()
        dataBindUtil?.preViewBar?.setListenter(null)
        MultiFileObserver.getInstance().removeFileEventListener(this)
        viewAnimationControl.release()
    }

    private fun stopObserver() {
        mEditViewModel.playerController.playerState.removeObserver(mPlayStateChangeObserver)
        mEditViewModel.playerController.removeTimerTickListener(mTimeTickCallback)
    }

    private fun releaseMarks() {
        mEditViewModel.markDataList.value?.forEach {
            it.release()
        }
    }

    override fun onZoomWindowShow() {
        mMainHandler.post {
            setSpace(GONE)
        }
    }

    override fun onZoomWindowHide() {
        mMainHandler.post {
            setSpace(VISIBLE)
        }
    }

    private fun setSpace(visibility: Int) {
//        dataBindUtil?.spaceGloble?.visibility = visibility
        dataBindUtil?.spaceCut?.visibility = visibility
    }

    override fun onBackPressed(): Boolean {
        createCancelClipDialog()
        return true
    }

    private fun initiateWindowInsets() {
        val window = activity?.window
        val activity = activity
        if ((window != null) && (activity != null)) {
            WindowCompat.setDecorFitsSystemWindows(window, false)
            dataBindUtil?.root?.apply {
                val callback = object : RootViewPersistentInsetsCallback() {
                    override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                        DebugUtil.i(TAG, "initiateWindowInsets invoke checkNeedHideOtherView")
                        super.onApplyInsets(v, insets)
                        resetLayoutForConfigChanged()
                        val stableStatusBarInsets =
                            insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                        initHoverView(stableStatusBarInsets.top + stableStatusBarInsets.bottom)
                        TaskBarUtil.setNavigationColorOnSupportTaskBar(
                            navigationHeight = stableStatusBarInsets.bottom,
                            activity = activity,
                            defaultNoTaskBarColor = (activity as? BaseActivity)?.navigationBarColor()
                        )
                    }
                }
                ViewCompat.setOnApplyWindowInsetsListener(this, callback)
            }
        }
    }

    fun checkNeedHideOtherView(isAnimator: Boolean = false) {
        if (dataBindUtil?.waveLayout?.isLaidOut != true) {
            DebugUtil.i(TAG, "checkNeedHideOtherView return by not layOut ")
            return
        }
        DebugUtil.i(TAG, "checkNeedHideOtherView. isAnimator$isAnimator ")
        val dataBinding = dataBindUtil ?: return
        val waveView = dataBinding.waveLayout
        val animTitleView = dataBinding.animTitle
        val markListView = dataBinding.layoutMarklist
        val dragBarView = dataBinding.preViewBarAndCutLayout
        val middleControl = dataBinding.middleControl
        val contentHeight = middleControl.top - dataBinding.abl.bottom

        val hasMarkData = !isMarksEmpty()

        // 时间名称的高度
        val titleHeight =
            getDimensionPx(com.soundrecorder.common.R.dimen.common_max_time_layout_height)
        // 波形高度
        val waveMargin =
            resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.common_wave_view_margin_top)
        var waveHeight = viewAnimationControl.getWaveHeight(hasMarkData) + waveMargin
        // 标记高度
        val markItemHeight =
            if (hasMarkData) {
                getDimensionPx(com.soundrecorder.wavemark.R.dimen.mark_item_height) + getDimensionPx(
                    R.dimen.mark_area_padding_vertical
                )
            } else {
                0
            }
        val dragBarHeight = dragBarView.height // 小波形+底部时间高度
        val centerDividerHeight =
            if (viewAnimationControl.isHoverState()) dataBinding.viewCenterDivider.height else 0
        /*DebugUtil.i(TAG, "checkNeedHideOtherView contentHeight $contentHeight, waveHeight  $waveHeight, " +
                "minTitleViewHeight $minTitleViewHeight, maxTitleViewHeight $maxTitleViewHeight, " +
                "markItemHeight $markItemHeight, dragBarHeight $dragBarHeight")*/
        if ((contentHeight - waveHeight - titleHeight - markItemHeight - dragBarHeight - centerDividerHeight) >= 0) {
            //DebugUtil.e(TAG, "checkNeedHideOtherView step1" )
            waveView.isVisible = true
            markListView.isVisible = hasMarkData
            animTitleView.isVisible = true
            dragBarView.isVisible = true
            if (hasMarkData) {
                viewAnimationControl.doAnimateTitleViewToTop(isAnimator, dataBinding)
            } else {
                viewAnimationControl.doAnimateTitleViewToCenter(isAnimator, dataBinding)
            }

            return
        }
        markListView.isVisible = false // 隐藏标记
        if (hasMarkData) {
            waveHeight = viewAnimationControl.getWaveHeight() + waveMargin
        }

        if (contentHeight - waveHeight - titleHeight - dragBarHeight >= 0) {
            //DebugUtil.i(TAG, "checkNeedHideOtherView step2" )
            waveView.isVisible = true
            animTitleView.isVisible = true
            dragBarView.isVisible = true
            viewAnimationControl.doAnimateTitleViewToCenter(isAnimator, dataBinding)
            return
        }
        waveView.isVisible = false // 隐藏波形
        if (contentHeight - titleHeight - dragBarHeight >= 0) {
            //DebugUtil.i(TAG, "checkNeedHideOtherView step4" )
            animTitleView.isVisible = true
            dragBarView.isVisible = true
            viewAnimationControl.doAnimateTitleViewToCenter(isAnimator, dataBinding)
            return
        }
        //DebugUtil.i(TAG, "checkNeedHideOtherView step5" )
        animTitleView.isVisible = false // 隐藏时间
        dragBarView.isVisible = contentHeight - dragBarHeight >= 0 // 可能隐藏小波形
        viewAnimationControl.doAnimateTitleViewToCenter(isAnimator, dataBinding)
    }

    private fun isMarksEmpty(): Boolean = mEditViewModel.markDataList.value?.size == 0

    private fun getDimensionPx(@DimenRes dimen: Int): Int {
        return BaseApplication.getAppContext().resources.getDimensionPixelOffset(dimen)
    }

    private fun setTimerTextViewText(currentTimeMillis: Long) {
        val formatTime = TimeUtils.getFormatTimeByMillisecond(currentTimeMillis)
        dataBindUtil?.timerView?.fixTextFlash(formatTime)
    }

    companion object {

        const val TAG = "EditRecordFragment"
        const val READ_MARK_TIMEOUT = 20000
        private const val DIALOG_MIN_TIME = 1000
    }

    override fun onFoldStateChanged(state: Int) {
        viewAnimationControl.foldWindowType = state
        if (dataBindUtil?.waveLayout?.isLaidOut != true) {
            DebugUtil.i(TAG, "onFoldStateChanged return by not layOut foldWindowType=$state")
            return
        }
        viewAnimationControl.doAnimFoldStateChanged(dataBindUtil, !isMarksEmpty())
    }

    override fun onUserChange() {
        mEditViewModel.cancelClipTask(false)
    }
}
