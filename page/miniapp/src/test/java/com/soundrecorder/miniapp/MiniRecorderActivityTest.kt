/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniRecorderActivityTest
 * Description:
 * Version: 1.0
 * Date: 2023/4/19
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/4/19 1.0 create
 */

package com.soundrecorder.miniapp

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.databinding.ActivityMiniRecorderBinding
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import com.soundrecorder.miniapp.view.button.AppCardButton
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.recorder.SaveFileState
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import io.mockk.mockk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import java.lang.ref.WeakReference
import java.util.concurrent.CopyOnWriteArrayList

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MiniRecorderActivityTest {
    private var mController: ActivityController<MiniRecorderActivity>? = null
    private var mContext: Context? = null
    private lateinit var binding: ActivityMiniRecorderBinding

    private val recorderViewModelApi = mockk<RecorderServiceInterface>()

    private val koinApp = koinApplication {
        modules(module {
            single { recorderViewModelApi }
        })
    }

    @Before
    fun setUp() {
        mController = Robolectric.buildActivity(MiniRecorderActivity::class.java)
        mContext = ApplicationProvider.getApplicationContext()
        startKoin(koinApp)
    }

    @After
    fun after() {
        stopKoin()
    }

    @Test
    fun should_when_onCreate() {
        mController ?: return
        val activity = mController!!.get()
        mController!!.create().start().resume()
        binding = ActivityMiniRecorderBinding.inflate(activity.layoutInflater)
        Assert.assertFalse(binding.btnSwitchState.hasOnClickListeners())
        Assert.assertTrue(activity.hasExclude())

        mController!!.pause().stop()
        val listener =
            Whitebox.getInternalState<CopyOnWriteArrayList<WeakReference<RecorderControllerListener>>>(
                RecorderViewModel.getInstance(),
                "listeners"
            )
        Assert.assertFalse(listener.isEmpty())
    }

    @Test
    fun should_when_performClick() {
        mController ?: return
        val activity = mController!!.get()
        mController!!.create().start().resume()
        binding = ActivityMiniRecorderBinding.inflate(activity.layoutInflater)
        val recordButton = binding.btnSwitchState
        recordButton.performClick()
        recordButton.performClick()
        Assert.assertNotEquals(
            activity.getString(com.soundrecorder.common.R.string.recording_start),
            recordButton.contentDescription
        )
    }

    @Test
    fun should_when_updateSaveButtonView() {
        mController ?: return
        val activity = mController!!.get()
        mController!!.create().start().resume()
        binding = ActivityMiniRecorderBinding.inflate(activity.layoutInflater)
        recorderViewModelApi.saveFileState = SaveFileState.INIT
        Whitebox.invokeMethod<Unit>(activity, "updateSaveButtonView")
        Assert.assertTrue(binding.btnSaveFile.isEnabled)

        recorderViewModelApi.saveFileState = SaveFileState.START_LOADING
        Whitebox.invokeMethod<Unit>(activity, "updateSaveButtonView")
        Whitebox.invokeMethod<Unit>(activity, "updateMarkButtonView")
        Assert.assertTrue(binding.btnSaveFile.isEnabled)
        Assert.assertFalse(binding.btnSaveFile.fakeDisable)
    }

    @Test
    fun should_correct_when_isSavingState() {
        mController ?: return
        val activity = mController!!.get()
        mController!!.create().start().resume()
        Assert.assertTrue(Whitebox.invokeMethod<Boolean>(activity, "isSavingState", 0))
        Assert.assertTrue(Whitebox.invokeMethod<Boolean>(activity, "isSavingState", 1))
        Assert.assertFalse(Whitebox.invokeMethod<Boolean>(activity, "isSavingState", -1))
    }

    @Test
    fun should_correct_when_updateRecordControlView() {
        mController ?: return
        val activity = mController!!.get()
        mController!!.create().start().resume()

        Whitebox.invokeMethod<Unit>(activity, "updateRecordControlView", 0)
        Assert.assertFalse(activity.findViewById<AppCardButton>(R.id.btnSaveFile).fakeDisable)

        Whitebox.invokeMethod<Unit>(activity, "updateRecordControlView", 1)
        Assert.assertFalse(activity.findViewById<AppCardButton>(R.id.btnSaveFile).fakeDisable)

        Whitebox.invokeMethod<Unit>(activity, "updateRecordControlView", 2)
        Assert.assertFalse(activity.findViewById<AppCardButton>(R.id.btnSaveFile).fakeDisable)

        recorderViewModelApi.saveFileState = SaveFileState.START_LOADING
        Whitebox.invokeMethod<Unit>(activity, "updateRecordControlView", 2)
        Assert.assertFalse(activity.findViewById<AppCardButton>(R.id.btnSaveFile).fakeDisable)
    }

}