/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniRecorderActivity
 * Description:
 * Version: 1.0
 * Date: 2023/4/14
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/4/13 1.0 create
 */

package com.soundrecorder.miniapp

import android.app.RecoverableSecurityException
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.View.OnClickListener
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDelegate
import androidx.appcompat.widget.AppCompatButton
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.MiniAppStaticUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.task.ExcludeActivityTask
import com.soundrecorder.common.utils.FoldStateLiveData
import com.soundrecorder.miniapp.base.MiniPermissionActivity
import com.soundrecorder.miniapp.databinding.ActivityMiniRecorderBinding
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.INIT
import com.soundrecorder.modulerouter.recorder.PAUSED
import com.soundrecorder.modulerouter.recorder.RECORDING
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.recorder.SaveFileState
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import com.soundrecorder.recorderservice.api.RecorderViewModelApi.cancel
import com.soundrecorder.recorderservice.api.RecorderViewModelApi.cancelRecordNotification
import com.soundrecorder.recorderservice.api.RecorderViewModelApi.getCurrentStatus
import com.soundrecorder.recorderservice.api.RecorderViewModelApi.hasInitAmplitude
import com.soundrecorder.recorderservice.api.RecorderViewModelApi.hasInitRecorderService

class MiniRecorderActivity : MiniPermissionActivity(), OnClickListener, RecorderControllerListener, ExcludeActivityTask {
    companion object {
        private const val AMP_LIST_SIZE = 50
    }

    override val logTag = "MiniRecorderActivity"
    private var mAnimUtil: RecorderViewAnimUtil? = null
        get() {
            if (field == null) {
                field = RecorderViewAnimUtil(binding.btnSwitchState, binding.btnAddTextMark, binding.btnSaveFile)
            }
            return field
        }

    /*用于添加标记的显示，后续若其他使用，注意removeCallbacksAndMessages调用时机*/
    private val mMainHandler = Handler(Looper.getMainLooper())
    private val miniViewHelper = MiniRecorderViewHelper()

    private val foldStateLiveData = FoldStateLiveData()
    private lateinit var binding: ActivityMiniRecorderBinding
    private var mCancelDialog: AlertDialog? = null
    private var mReceiver: MyBroadcastReceiver? = null
    private var isResume = false
    private var isStop = false

    private val foldStateObserver = Observer<Boolean> { isFoldClose: Boolean ->
        DebugUtil.i(logTag, "foldStateObserver changed, state = $isFoldClose")
        if (!isFoldClose) {
            continueToMainScreenWhenExpandScreen() //关闭
            if (RecorderViewModelApi.isAlreadyRecording()) {
                finish() //内屏开始录音页面，从外屏展开后接续，若RemoveTask会导致service onTaskRemove调用，从而胶囊和通知不可用
            } else {
                finishAndRemoveTask() //需要将运行权限弹窗activity一并finish
            }
        }
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DebugUtil.i(logTag, "onCreate")
        binding = ActivityMiniRecorderBinding.inflate(layoutInflater)
        setContentView(binding.root)
        miniViewHelper.hideStatusBar(this)
        delegate.localNightMode = AppCompatDelegate.MODE_NIGHT_NO
        registerReceiver()
        binding.btnSwitchState.setOnClickListener(this)
        binding.btnAddTextMark.setOnClickListener(this)
        binding.btnSaveFile.setOnClickListener(this)
        binding.imageRecorderClose.setOnClickListener(this)
        foldStateLiveData.observeForever(foldStateObserver)
        if (hasInitRecorderService()) {
            updateRecorderTitle(false, getRecorderTitle())
        }
        setPermissionGrantedListener {
            if (recorderViewModelApi?.isAlreadyRecording() == true) {
                DebugUtil.d(logTag, "onRequestPermissionAllGranted, isAlreadyRecording return")
                return@setPermissionGrantedListener
            }
            miniViewHelper.handleClickSwitchButton(this, noPermissionFun = {
                DebugUtil.e(logTag, "onRequestPermissionAllGranted, noPermission callback")
            }, startServiceFun = {
                lifecycleScope.launchWhenResumed {
                    binding.waveRecyclerView.doEnterAnimator()
                    mAnimUtil?.showMarkAndSaveFileView()
                }
            })
        }
        // 添加进入miniapp埋点
        MiniAppStaticUtil.addEnterMiniAppEvent()
    }

    override fun onResume() {
        isResume = true
        isStop = false
        initUI()
        super.onResume()
        DebugUtil.i(logTag, "onResume")
        RecorderViewModelApi.addListener(this)
    }

    override fun onPause() {
        isResume = false
        super.onPause()
        DebugUtil.i(logTag, "onPause")
        RecorderViewModelApi.removeListener(this)
    }

    override fun onStop() {
        super.onStop()
        isStop = true
        DebugUtil.i(logTag, "onStop")
    }

    override fun onDestroy() {
        super.onDestroy()
        DebugUtil.i(logTag, "onDestroy")
        mMainHandler.removeCallbacksAndMessages(null)
        foldStateLiveData.removeObserver(foldStateObserver)
        releaseCancelDialog()
        unregisterReceivers()
    }

    override fun onRecordNameSet(recordName: String) {
        updateRecorderTitle(false, getRecorderTitle())
    }

    override fun hasExclude(): Boolean {
        return true
    }

    override fun onClick(v: View?) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        if (mAnimUtil?.isAnimRunning() == true) {
            return
        }
        when (v?.id) {
            R.id.btnSwitchState -> {
                miniViewHelper.handleClickSwitchButton(this, noPermissionFun = {
                    requestPermission()
                }, startServiceFun = {
                    binding.waveRecyclerView.doEnterAnimator()
                    mAnimUtil?.showMarkAndSaveFileView()
                })
            }
            R.id.btnSaveFile -> miniViewHelper.handleClickSaveButton()
            R.id.btnAddTextMark -> miniViewHelper.handleClickMarkButton(this)
            R.id.imageRecorderClose -> {
                showCancelDialog()
                BuryingPoint.addActionMiniCancelRecord(RecorderUserAction.VALUE_MINI_CANCEL_RECORD_CLOSE)
            }
        }
    }

    override fun doRequestPermissionsFromSystem(permissions: Array<String>) {
        DebugUtil.d(logTag, "doRequestPermissionsFromSystem, isExp=${BaseUtil.isEXP()}")
        if (BaseUtil.isEXP()) {
            // 外销，不支持运行时权限弹窗，走内屏接续授权逻辑
            MiniRecordContinueUtil.continueToRecorderActivityByContinueFlag(this)
        } else {
            // 内销，走运行时权限逻辑
            super.doRequestPermissionsFromSystem(permissions)
        }
    }

    override fun onReadyService() {
        DebugUtil.i(logTag, "onReadyService ")
        initUI(false)
        val data: List<MarkDataBean>? = recorderViewModelApi?.getMarkData()
        if (data?.isNotEmpty() == true) {
            binding.waveRecyclerView.setMarkTimeList(data)
        }
        checkShowNotificationDialog()
    }

    override fun onCloseService() {
        DebugUtil.i(logTag, "onCloseService ")
        //刷新录制时间
        binding.tvTimeText.setTimerTextViewText()
        //刷新波形数据
        if (mAnimUtil?.isAnimRunning() != true) {
            refreshWaveData()
        }
        binding.waveRecyclerView.clearMarkTimeList()
        //刷新录制按钮UI
        updateRecordControlView()
        //刷新标题
        updateRecorderTitle(true, "")
        //刷新标记按钮
        updateMarkButtonView()
        // 刷新保存按钮
        updateSaveButtonView()
        mAnimUtil?.hideMarkAndSaveFileViewWithAnimation(false)
        cancelRecordNotification()
    }

    /**
     * 录制状态发生变化
     */
    override fun onRecordStatusChange(state: Int) {
        DebugUtil.i(logTag, "onRecordStatusChange state=$state")
        // 这里必须要加post，否则在录制过程中，添加标记后，快速暂停，标记按钮不置灰
        mMainHandler.post {
            updateRecordControlView(state)
            binding.tvStateText.setStateTextViewText(isSavingState = isSavingState())
            updateMarkButtonView()
        }
    }

    /**
     * 来电状态发生变化
     */
    override fun onRecordCallConnected() {
        DebugUtil.i(logTag, "onRecordCallConnected")
        updateRecordControlView()
        updateMarkButtonView()
    }

    /**
     * 波形发生变化
     */
    override fun onWaveStateChange(state: Int) {
        if (!hasInitAmplitude()) {
            return
        }
        binding.tvTimeText.setTimerTextViewText()
        refreshWaveData()
        updateMarkButtonView()
    }

    /**
     * 标记发生变化
     */
    override fun onMarkDataChange(markAction: Int, errorOrIndex: Int) {
        DebugUtil.i(logTag, "onMarkDataChange markAction=$markAction")
        updateMarkButtonView()
        if (errorOrIndex < 0) {
            return
        }
        binding.waveRecyclerView.setMarkTimeList(recorderViewModelApi?.getMarkData())
    }

    /**
     * 保存过程对应状态
     */
    override fun onSaveFileStateChange(
        state: Int,
        fileName: String,
        fullPath: String?,
        e: RecoverableSecurityException?
    ) {
        DebugUtil.i(logTag, "onSaveFileStateChange state=$state ")
        updateSaveButtonView()
        updateMarkButtonView()
        updateRecordControlView()
        binding.tvStateText.setStateTextViewText(isSavingState = isSavingState(state))
        when (state) {
            SaveFileState.START_LOADING,
            SaveFileState.SHOW_LOADING_DIALOG,
            SaveFileState.ERROR -> {
            }

            SaveFileState.SUCCESS -> {
                mAnimUtil?.hideMarkAndSaveFileViewWithAnimation() {
                    refreshWaveData()
                }
                binding.waveRecyclerView.doEndAnimator()
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(),
                    resources.getString(com.soundrecorder.common.R.string.dragon_fly_save_record_success))
            }
        }
    }

    private fun initUI(checkViewPositionAnim: Boolean = true) {
        //刷新录制时间
        binding.tvTimeText.setTimerTextViewText()
        // 刷新录制状态
        binding.tvStateText.setStateTextViewText(isSavingState = isSavingState())
        //刷新波形数据
        refreshWaveData()
        //刷新录制按钮UI
        updateRecordControlView()
        //刷新标记按钮
        updateMarkButtonView()
        // 刷新保存按钮
        updateSaveButtonView()
        if (checkViewPositionAnim) {
            // 刷新按钮显示位置
            if (recorderViewModelApi?.isAlreadyRecording() == true) {
                mAnimUtil?.showMarkAndSaveFileView(false)
            } else {
                mAnimUtil?.hideMarkAndSaveFileViewWithAnimation(false)
            }
        }
    }

    private fun updateRecorderTitle(isBackInit: Boolean, recordName: String) {
        if (isBackInit) {
            binding.tvRecorderName.visibility = View.VISIBLE
            binding.toolbarLayout.visibility = View.GONE
        } else {
            binding.tvRecorderName.visibility = View.INVISIBLE
            binding.toolbarLayout.visibility = View.VISIBLE
        }
        binding.tvRecorderTitle.text = recordName
    }

    private fun showCancelDialog() {
        if (mCancelDialog?.isShowing == true) {
            return
        }
        val cancelConfirmListener = DialogInterface.OnClickListener { dialog: DialogInterface, _: Int ->
            BuryingPoint.addActionMiniCancelRecord(RecorderUserAction.VALUE_MINI_CANCEL_RECORD_BTN_DELETE)
            cancelRecord()
            dialog.dismiss()
        }
        mCancelDialog =
            COUIAlertDialogBuilder(
                this,
                com.support.dialog.R.style.COUIAlertDialog_Bottom_Tiny
            )
                .setBlurBackgroundDrawable(true)
                .setTitle(com.soundrecorder.common.R.string.recording_exit_confirm_save)
                .setNeutralButton(
                    com.soundrecorder.common.R.string.recording_exit_save,
                ) { _: DialogInterface, _: Int -> miniViewHelper.handleClickSaveButton() }
                .setPositiveButton(
                    com.soundrecorder.common.R.string.recording_exit_do_not_save,
                    cancelConfirmListener,
                )
                .setNegativeButton(com.soundrecorder.common.R.string.cancel) { _: DialogInterface, _: Int ->
                    BuryingPoint.addActionMiniCancelRecord(RecorderUserAction.VALUE_MINI_CANCEL_RECORD_BTN_CANCEL)
                }
                .setCancelable(true)
                .show()
        mCancelDialog?.findViewById<AppCompatButton>(android.R.id.button3).apply {
            (this as COUIButton).drawableColor =
                getColor(com.support.appcompat.R.color.coui_color_container_theme_red)
            setTextColor(getColor(com.support.appcompat.R.color.coui_color_white))
        }
        val button = mCancelDialog!!.findViewById<AppCompatButton>(android.R.id.button2)
        button?.setTextColor(getColor(com.support.appcompat.R.color.coui_color_primary_neutral))
    }

    private fun cancelRecord() {
        cancel()
    }

    private fun refreshWaveData() {
        val ampList = recorderViewModelApi?.getAmplitudeList() ?: listOf()
        val size = ampList.size
        val amps = ampList.run {
            if (size <= AMP_LIST_SIZE) {
                this
            } else {
                subList(size - AMP_LIST_SIZE, size)
            }.reversed()
        }
        if (getCurrentStatus() == RECORDING) {
            binding.waveRecyclerView.recorderIntervalUpdate(size, amps, true)
        } else {
            binding.waveRecyclerView.stopRecorderMove(size, amps)
        }
    }

    private fun updateRecordControlView(state: Int? = getCurrentStatus()) {
        if (isSavingState()) {
            /*点击保存后，保存中、保存完成结果状态下，不改变录制状态图标，在原有基础上直接置灰
            * 若不加RESULT_SUCCESS/ERROR这2个状态，会闪现暂停状态按钮*/
            setRecordViewInSavingState()
            return
        }
        var fakeDisable = false
        var resId = getRecordSrcAndContentDec(state).run {
            binding.btnSwitchState.contentDescription = second
            return@run first
        }
        if (recorderViewModelApi?.isAudioModeChangePause() == true) {
            resId = R.drawable.icon_pause_state_disable
            fakeDisable = true
        }
        binding.btnSwitchState.setImageViewResource(resId)
        binding.btnSwitchState.fakeDisable = fakeDisable
    }

    private fun setRecordViewInSavingState() {
        getRecordSrcAndContentDec(
            recorderViewModelApi?.getRecordStatusBeforeSaving(),
            false
        ).run {
            binding.btnSwitchState.setImageViewResource(first)
            binding.btnSwitchState.contentDescription = second
            binding.btnSwitchState.fakeDisable = true
        }
    }

    private fun getRecordSrcAndContentDec(
        state: Int? = getCurrentStatus(),
        normalState: Boolean = true
    ): Pair<Int, String> {
        val imageResId: Int
        val contentDec: String
        when (state) {
            RECORDING -> {
                contentDec = getString(com.soundrecorder.common.R.string.recording_notify_talk_back)
                imageResId = if (normalState) R.drawable.icon_play_state_normal else R.drawable.icon_play_state_disable
            }

            PAUSED -> {
                contentDec = getString(com.soundrecorder.common.R.string.record_pause_tips)
                imageResId = if (normalState) R.drawable.icon_pause_state_normal else R.drawable.icon_pause_state_disable
            }
            else -> {
                contentDec = getString(com.soundrecorder.common.R.string.recording_start)
                imageResId = R.drawable.icon_record_state_normal
            }
        }
        return Pair(imageResId, contentDec)
    }

    private fun updateMarkButtonView() {
        if (false == recorderViewModelApi?.isMarkEnabledFull()) {
            binding.btnAddTextMark.setImageViewResource(R.drawable.icon_mark_disable)
            binding.btnAddTextMark.fakeDisable = true
        } else {
            binding.btnAddTextMark.setImageViewResource(R.drawable.icon_mark_normal)
            binding.btnAddTextMark.fakeDisable = false
        }
    }

    private fun updateSaveButtonView() {
        if (isSavingState()) {
            binding.btnSaveFile.isEnabled = false
            binding.btnSaveFile.setImageViewResource(R.drawable.icon_save_disable)
        } else {
            binding.btnSaveFile.isEnabled = true
            binding.btnSaveFile.setImageViewResource(R.drawable.icon_save_normal)
        }
    }

    private fun isSavingState(state: Int = recorderViewModelApi?.saveFileState ?: INIT): Boolean =
        (state != SaveFileState.INIT)

    private fun getRecorderTitle(): String = RecorderViewModelApi.getRecordModeName()

    private fun releaseCancelDialog() {
        if (mCancelDialog?.isShowing == true) {
            mCancelDialog?.dismiss()
            mCancelDialog = null
        }
    }

    private fun registerReceiver() {
        mReceiver = MyBroadcastReceiver()
        val intentFilter = IntentFilter()
        intentFilter.addAction(RecorderDataConstant.ACTION_RECORDER_STOP_CANCEL)
        LocalBroadcastManager.getInstance(applicationContext)
            .registerReceiver(mReceiver!!, intentFilter)
    }

    private fun unregisterReceivers() {
        if (mReceiver != null) {
            LocalBroadcastManager.getInstance(applicationContext).unregisterReceiver(mReceiver!!)
            mReceiver = null
        }
    }

    /**
     * 展开手机接续到内屏
     * 1.用户须知：到首页用户须知，弹窗需同副屏一致
     * 2.权限运行时弹窗(点击录制触发，点击事件中已经清栈了)：
     * - 麦克风、存储、通知：录制页面
     * - 通知：说明已有麦克风存储权限，录制页面
     * 3.权限弹窗(点击录制触发，点击事件中已经清栈了)：
     * -通知权限弹窗
     *  - 录制已开始：接续内屏录制页面，底部弹snack bar
     *  - 录制未开始：接续内屏首页，显示snack bar
     * -麦克风、存储权限弹窗(不代表有通知权限)：接续内屏录制页面
     * 4.无弹窗&&录制中：
     * - 通话录音录制：内屏接续到桌面录音录制页面，保留通话录音
     * - 桌面录音录制/其他后台录音，内屏接续到桌面录音录制页面，（若从桌面录音入口起的，不销毁首页）
     * 5.默认页面：到首页（若内屏主进程已在，且再播放、裁切、快捷播放，则保持内屏不变化）
     */
    private fun continueToMainScreenWhenExpandScreen() {
        DebugUtil.d(logTag, "continueToMainScreenWhenExpandScreen, isResume=$isResume,isStop=$isStop " +
                "requestPermissionNoResponse=$requestPermissionNoResponse,permissionDialogType=$permissionDialogType，" +
                "isPermissionDialogShowing=${isPermissionDialogShowing()}")
        if (!isResume) {
            handleContinueWhenActivityNotResume()
            return
        }
        val showingPrivacyDialogType = getLastShowPolicyDialogType()
        DebugUtil.d(logTag, "continueToMainScreenWhenExpandScreen,showingPrivacyDialogType= $showingPrivacyDialogType ")
        if (showingPrivacyDialogType != null) {
            /*1.用户须知弹窗显示中*/
            ActivityTaskUtils.clearAllTask()
            MiniRecordContinueUtil.continueBrowseFileByExpand(this,
                showNotificationSnackBar = false, canShowAllFileDialogWhenResume = true)
            MiniAppStaticUtil.addMiniAppContinueEvent(MiniAppStaticUtil.MINI_APP_CONTINUE_FROM_USER_NOTICE)
            return
        }
        val permissionDialogShowing = isPermissionDialogShowing()
        DebugUtil.d(logTag, "continueToMainScreenWhenExpandScreen,isRecording=${RecorderViewModelApi.isAlreadyRecording()}")
        if (permissionDialogShowing) {
            // 3.权限弹窗
            handleContinueWhenPermissionDialogShow()
            return
        }

        if (RecorderViewModelApi.isAlreadyRecording()) { // 4.无权限隐私弹窗&&录制中：-->到内屏录制页面，若为通话录音录制，则清主栈
            MiniRecordContinueUtil.continueRecorderActivityByExpand(
                this, clearTask = RecorderViewModelApi.isFromOtherApp(), showNotificationSnackBar = false)
            MiniAppStaticUtil.addMiniAppContinueEvent(MiniAppStaticUtil.MINI_APP_CONTINUE_FROM_RECORDING)
        } else { // 5.默认页面：到首页（若内屏主进程已在，且再播放、裁切、快捷播放，则保持内屏不变化）
            MiniRecordContinueUtil.continueByExpandWhenDefaultState(this)
            MiniAppStaticUtil.addMiniAppContinueEvent(MiniAppStaticUtil.MINI_APP_CONTINUE_FROM_DEFAULT)
        }
    }

    /**
     * 处理外屏录制页面非resume下接续内屏逻辑
     */
    private fun handleContinueWhenActivityNotResume() {
        /**
         * isResume为false有以下场景： 1.锁屏或切后台  -- 不需要处理； 2.运行时权限弹窗显示  -- 需要处理； 3.跳转到到了接续页面暂态层  --不需要处理
         */
        if (!isStop && requestPermissionNoResponse) { // 2.运行时权限
            MiniRecordContinueUtil.continueRecorderActivityByExpand(
                this,
                clearTask = true,
                showNotificationSnackBar = false,
                canShowAllFileDialogWhenResume = false
            )
            MiniAppStaticUtil.addMiniAppContinueEvent(MiniAppStaticUtil.MINI_APP_CONTINUE_FROM_PERMISSION_DIALOG)
        } else if (isPermissionDialogShowing() && permissionDialogType == PermissionDialogUtils.TYPE_PERMISSION_POST_NOTIFICATION) {
            // 拒绝通知权限，显示通知权限引导弹窗，清空通知权限申请SP值，下次进入录音再申请一次
            StorageManager.setIntPref(
                applicationContext,
                PermissionUtils.POST_NOTIFICATIONS,
                PermissionUtils.PERMISSION_NOT_APPLY
            )
        }
    }

    /**
     * 处理当权限弹窗显示的时候接续到内屏逻辑
     */
    private fun handleContinueWhenPermissionDialogShow() {
        if (permissionDialogType == PermissionDialogUtils.TYPE_PERMISSION_POST_NOTIFICATION) {
            if (RecorderViewModelApi.isAlreadyRecording()) { // 通知权限弹窗&&录制中--->内屏录制页面
                MiniRecordContinueUtil.continueRecorderActivityByExpand(
                    this,
                    clearTask = true,
                    showNotificationSnackBar = true,
                    canShowAllFileDialogWhenResume = false
                )
            } else { // 通知权限弹窗&&未开始录制--->内屏首页
                MiniRecordContinueUtil.continueBrowseFileByExpand(
                    context = this,
                    showNotificationSnackBar = true,
                    canShowAllFileDialogWhenResume = false
                )
            }
        } else { // 其他录制必须权限弹窗(麦克风、存储)--->内屏录制页面
            MiniRecordContinueUtil.continueRecorderActivityByExpand(
                this,
                clearTask = true,
                showNotificationSnackBar = false,
                canShowAllFileDialogWhenResume = false
            )
        }
        MiniAppStaticUtil.addMiniAppContinueEvent(MiniAppStaticUtil.MINI_APP_CONTINUE_FROM_PERMISSION_DIALOG)
    }


    private class MyBroadcastReceiver : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                RecorderDataConstant.ACTION_RECORDER_STOP_CANCEL ->
                    RecorderViewModelApi.stopService()
                else -> {}
            }
        }
    }
}