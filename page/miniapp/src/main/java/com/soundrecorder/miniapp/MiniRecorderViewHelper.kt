/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniRecorderViewHelper
 * Description:
 * Version: 1.0
 * Date: 2023/4/14
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/4/14 1.0 create
 */

package com.soundrecorder.miniapp

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import android.view.WindowInsets
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.utils.ViewUtils.fixTextFlash
import com.soundrecorder.modulerouter.recorder.FROM_SWITCH_RECORD_STATUS_MINI
import com.soundrecorder.modulerouter.recorder.PAUSED
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector

class MiniRecorderViewHelper {
    private val logTag = "MiniRecorderViewHelper"

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    fun hideStatusBar(activity: Activity) {
        runCatching {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                activity.window.decorView.windowInsetsController?.hide(WindowInsets.Type.statusBars())
            }
        }
    }

    /**
     * 录制按钮点击事件
     */
    fun handleClickSwitchButton(context: Context, noPermissionFun: () -> Unit, startServiceFun: () -> Unit) {
        if (recorderViewModelApi?.isAlreadyRecording() == true) {
            // 处于录制状态，暂停/继续--通话拦截在service中有处理
            recorderViewModelApi?.switchRecorderStatus(FROM_SWITCH_RECORD_STATUS_MINI)
            return
        }
        //check权限
        if (!checkRecorderPermission()) {
            noPermissionFun.invoke()
            return
        }
        // check 是否处于通话中，通话中拦截
        if (false == recorderViewModelApi?.checkModeCanRecord(true)) {
            return
        }
        startForegroundRecorderService(context, startServiceFun)
    }

    /**
     * 保存的点击事件
     */
    fun handleClickSaveButton() {
        if (recorderViewModelApi?.hasInitRecorderService() == true) {
            BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD_MINI_CARD)
            recorderViewModelApi?.saveRecordInfo(saveRecordFromWhere = RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_APP_CARD)
        }
    }

    /**
     * 标记点击事件
     */
    fun handleClickMarkButton(context: Context) {
        if (false == recorderViewModelApi?.hasInitRecorderService()) {
            return
        } else if (false == recorderViewModelApi?.isAlreadyRecording()) {
            return
        } else if (recorderViewModelApi?.checkMarkDataMoreThanMax() == true) {
            ToastManager.showShortToast(
                context.applicationContext,
                com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit
            )
            return
        } else if (false == recorderViewModelApi?.isMarkEnabledFull()) {
            return
        } else {
            val mark =
                MarkMetaData(currentTimeMillis = recorderViewModelApi?.getAmplitudeCurrentTime() ?: 0)
            recorderViewModelApi?.addMark(mark)
        }
        BuryingPoint.addClickRecordTextMarkInMiniCard()
    }

    private fun checkRecorderPermission(): Boolean {
        ActivityTaskUtils.clearAllTask()
        if (PermissionUtils.getNextAction() == PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            DebugUtil.i(logTag, "SHOULD_SHOW_USER_NOTICE")
            return false
        }
        if (!PermissionUtils.hasReadAudioPermission()) {
            DebugUtil.i(logTag, "hasReadAudioPermission")
            return false
        }
        if (!PermissionUtils.hasRecordAudioPermission()) {
            DebugUtil.i(logTag, "hasRecordAudioPermission")
            return false
        }
        return true
    }

    private fun startForegroundRecorderService(context: Context, startServiceFun: () -> Unit) {
        try {
            context.startForegroundService(Intent().apply {
                action = "oplus.intent.action.RECORDER_SERVICE"
                setPackage(context.packageName)
                putExtra(RecorderDataConstant.SERVICE_AUTO_START_RECORD, true)
                putExtra(
                    RecorderDataConstant.PAGE_FROM_NAME,
                    RecorderDataConstant.PAGE_FROM_MINI_APP
                )
            })
            startServiceFun.invoke()
        } catch (ignored: Exception) {
            DebugUtil.d(logTag, "startForegroundRecorderService error $ignored")
        }
    }
}

/**
 * 录制状态textView
 */
fun TextView.setStateTextViewText(isSavingState: Boolean = false) {
    // 默认 开始录音 状态
    var textContent = ""

    DebugUtil.d("isSavingState", "isSavingState= $isSavingState")
    if (isSavingState) {
        textContent = context.getString(com.soundrecorder.common.R.string.is_saving)
    } else if (Injector.injectFactory<RecorderServiceInterface>()?.getCurrentStatus() == PAUSED) {
        if (Injector.injectFactory<RecorderServiceInterface>()?.isAudioModeChangePause() == true) {
            textContent = context.getString(com.soundrecorder.common.R.string.state_call_record_paused)
        }
    }
    if (text != textContent) {
        text = textContent
    }
}

/**
 * 时长view
 */
fun TextView.setTimerTextViewText() {
    val time = Injector.injectFactory<RecorderServiceInterface>()?.getAmplitudeCurrentTime() ?: 0
    fixTextFlash(TimeUtils.getFormatTimeByMillisecond(time))
    contentDescription = TimeUtils.getDurationHint(context, time)
}

fun TextView.isFakeBoldText(flag: Boolean) {
    try {
        if (paint.isFakeBoldText != flag) {
            paint.isFakeBoldText = flag
        }
    } catch (_: Exception) {
    }
}

fun ImageView.setImageViewResource(@DrawableRes resId: Int) {
    if (resId != getTag(R.id.tag_imageview_res_id)) {
        setImageResource(resId)
        setTag(R.id.tag_imageview_res_id, resId)
    }
}