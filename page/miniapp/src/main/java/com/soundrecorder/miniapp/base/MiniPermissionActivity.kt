/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniPermissionActivity
 * Description:
 * Version: 1.0
 * Date: 2023/8/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/18 1.0 create
 */
package com.soundrecorder.miniapp.base

import android.Manifest
import android.app.Dialog
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.common.permission.NotificationPermissionSnackBarTransparentActivity
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.miniapp.MiniRecordContinueUtil
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.miniapp.MiniAppConstant
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.api.RecorderViewModelApi

open class MiniPermissionActivity : PermissionActivity() {
    open val logTag = "MiniPermissionActivity"
    var permissionDialogType: Int? = null

    /**
     * 记录，调用了系统requestPermission，还没有收到处理结果
     * true：调用了requestPermission。未收到结果回调
     * false：未调用requestPermission or 收到了请求结果
     */
    var requestPermissionNoResponse = false

    /**
     * 内屏录制页面显示通知引导弹窗，折叠到外屏显示
     */
    private var notificationContinueDialog: Dialog? = null

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val recordApi by lazy {
        Injector.injectFactory<RecordInterface>()
    }

    override fun policyType(): Int = IPrivacyPolicyDelegate.POLICY_TYPE_MINI

    override fun afterCheckAndShowPrivacyPolicyDialogOnResume(nextAction: Int) {
        /**onResume不请求权限*/
    }

    override fun onAgreeClick() {
        /**同意用户须知弹窗不请求权限*/
    }

    override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
        /*不同意用户须知等弹窗，具体逻辑已有，这里主要是处理升级弹窗后的下一步操作，外屏不需要处理请求权限等*/
    }
    /**
     * 权限弹窗，点击设置，调到设置-录音详情
     * @param alertType TYPE_PERMISSION_OTHER or TYPE_PERMISSION_POST_NOTIFICATION
     * TYPE_PERMISSION_OTHER:麦克风/存储权限；返回到录制页面
     * TYPE_PERMISSION_POST_NOTIFICATION:若已经开始录制，到录制页面，否则到首页列表
     */
    override fun goToAppSettingConfigurePermissions(alertType: Int, permissions: ArrayList<String>?) {
        when (alertType) {
            PermissionDialogUtils.TYPE_PERMISSION_OTHER -> { // 接续到内屏录制页-录音应用详情
                recordApi?.getRecorderActivityClass()?.let {
                    MiniRecordContinueUtil.continueToRecordSettingDetailByContinueFlag(this, it, permissions)
                }
            }
            PermissionDialogUtils.TYPE_PERMISSION_POST_NOTIFICATION -> {
                if (RecorderViewModelApi.isAlreadyRecording()) {
                    MiniRecordContinueUtil.continueToSettingByContinueFlagOnRecordingState(this, permissions)
                } else {
                    browseFileApi?.getBrowseFileClass()?.let {
                        MiniRecordContinueUtil.continueToRecordSettingDetailByContinueFlag(this, it, permissions)
                    }
                }
            }
        }
    }

    override fun dialogPermissionType(dialogPermissionType: Int) {
        permissionDialogType = dialogPermissionType
    }

    /**
     * 用户须知clickSpan点击事件（信息保护政策）
     * @param type SPAN_TYPE_RECORDER_POLICY or SPAN_TYPE_PERSONAL_POLICY
     * SPAN_TYPE_RECORDER_POLICY：录音个人信息保护政策，返回到首页用户须知弹窗，弹窗与副屏一致
     * SPAN_TYPE_PERSONAL_POLICY：开机向导，返回到首页用户须知弹窗，弹窗与副屏一致
     */
    override fun onClickSpan(type: Int) {
        when (type) {
            IPrivacyPolicyDelegate.SPAN_TYPE_RECORDER_POLICY -> {
                val nextActionValue = MiniAppConstant.NEXT_ACTION_RECORDER_PRIVACY
                MiniRecordContinueUtil.continueGotoPrivacyPageByContinueFlag(this, nextActionValue)
            }
            IPrivacyPolicyDelegate.SPAN_TYPE_PERSONAL_POLICY -> {
                MiniRecordContinueUtil.continueGotoPrivacyPageByContinueFlag(
                    this, MiniAppConstant.NEXT_ACTION_BOOT_PRIVACY)
            }
            else -> super.onClickSpan(type)
        }
    }


    override fun doFinishActivityWhenRefusePermission() {
        /**拒绝所有权限不退出页面*/
    }

    /**
     * 权限弹窗显示样式
     */
    override fun permissionDialogStyleType(): Int {
        return PermissionDialogUtils.TYPE_DIALOG_TINY
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>, grantResults: IntArray) {
        requestPermissionNoResponse = false
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun requestCode(): Int = PermissionUtils.REQUEST_CODE_MINI_RECORDER

    override fun checkCanRequestPermission(): Boolean {
        return true
    }

    override fun doRequestPermissionsFromSystem(permissions: Array<String>) {
        super.doRequestPermissionsFromSystem(permissions)
        requestPermissionNoResponse = true
    }

    override fun configPermission(): Array<String> {
        val permissionList = ArrayList<String>()
        if (!PermissionUtils.hasReadAudioPermission()) {
            permissionList.add(PermissionUtils.READ_AUDIO_PERMISSION())
        }
        if (!PermissionUtils.hasRecordAudioPermission()) {
            permissionList.add(PermissionUtils.RECORD_AUDIO)
        }
        if (BaseUtil.isAndroidTOrLater && !PermissionUtils.hasNotificationPermission() && !PermissionUtils.hasRequestNotificationPermission(this)) {
            permissionList.add(PermissionUtils.POST_NOTIFICATIONS)
        }
        val size = permissionList.size
        return if (size <= 0) {
            arrayOf()
        } else {
            permissionList.toTypedArray()
        }
    }

    /**
     * check是否需要显示通知权限弹窗
     * 内屏进入录制页面，弹窗通知底部snackBar，在未消失过程中折叠到外屏，外屏也要显示
     */
    fun checkShowNotificationDialog() {
        if (NotificationPermissionSnackBarTransparentActivity.isNotificationSnackBarShowing && RecorderViewModelApi.isAlreadyRecording()) {
            notificationContinueDialog =
                PermissionDialogUtils.showPermissionsDialog(this, this, arrayOf(Manifest.permission.POST_NOTIFICATIONS), permissionDialogStyleType())
        }
    }

    override fun onDestroy() {
        notificationContinueDialog?.dismiss()
        notificationContinueDialog = null
        super.onDestroy()
    }
}