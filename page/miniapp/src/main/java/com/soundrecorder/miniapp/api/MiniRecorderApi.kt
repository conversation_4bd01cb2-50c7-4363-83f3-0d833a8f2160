/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrowseFileApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/8/9
 * * Author      : v-yanx<PERSON><PERSON><PERSON>@oppo.com
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.miniapp.api

import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.soundrecorder.base.ext.IntentExt.getBooleanValue
import com.soundrecorder.base.ext.IntentExt.getIntValue
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.miniapp.MiniRecorderActivity
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.miniapp.MiniAppConstant
import com.soundrecorder.modulerouter.miniapp.MiniAppInterface
import com.soundrecorder.modulerouter.utils.Injector

object MiniRecorderApi : MiniAppInterface {

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    override fun createMiniAppIntent(context: Context): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClass(context, MiniRecorderActivity::class.java)
        return jumpIntent
    }

    override fun isMiniRecorderActivity(context: Context): Boolean = context is MiniRecorderActivity

    override fun checkMiniAppContinueAction(context: AppCompatActivity, intent: Intent, disableDialogFun: ((dialog: AlertDialog) -> Unit)) {
        // 1. check 点击事件行为接续
        when (intent.getIntValue(MiniAppConstant.EXTRA_NAME_NEXT_ACTION, -1)) {
            MiniAppConstant.NEXT_ACTION_BOOT_PRIVACY -> settingApi?.launchBootRegPrivacy(context, disableDialogFun)
            MiniAppConstant.NEXT_ACTION_RECORDER_PRIVACY -> {
                settingApi?.launchRecordPrivacy(
                    context,
                    com.soundrecorder.common.R.string.privacy_policy_settings_policy_key
                )
            }

            MiniAppConstant.NEXT_ACTION_APP_SETTING -> {
                PermissionUtils.goToAppSettingConfigurePermissions(context, intent.extras?.getStringArrayList(MiniAppConstant.MINI_PERMISSION_KEY))
            }
        }
        // 2. check 底部通知引导snackBar接续
        if (intent.getBooleanValue(MiniAppConstant.EXTRA_NAME_CHECK_SHOW_NOTIFICATION_SNACK_BAR, false)) {
            PermissionActivity.showRequestNotificationPermissionSnackBarWithoutCheck(context)
        }
    }
}