/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleMarkInsertHelperTest.kt
 * Description:
 *     Test cases for SubtitleMarkInsertHelper
 *
 * Version: 1.0
 * Date: 2025-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2025-05-29   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record.subtitle

import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.markdata.MarkDataBean
import org.junit.Assert
import org.junit.Before
import org.junit.Test

/**
 * 此代码文件为AI生成产物，生成工具为Augment
 */
class SubtitleMarkInsertHelperTest : Assert() {

    private lateinit var helper: SubtitleMarkInsertHelper

    @Before
    fun setUp() {
        helper = SubtitleMarkInsertHelper()
    }

    // ========== calculateMarkFlagInsertPosition方法的测试用例 ==========

    @Test
    fun should_return_negative_one_when_calculateMarkFlagInsertPosition_with_invalid_time_range() {
        // Given: 无效的时间范围（结束时间小于等于开始时间）
        val asrContent = "测试内容"
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 1000L // 等于开始时间
        val markTimeOffset = 1500L

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回-1
        assertEquals(-1, result)
    }

    @Test
    fun should_return_zero_when_calculateMarkFlagInsertPosition_with_mark_before_start_time() {
        // Given: 标记时间点在开始时间之前
        val asrContent = "测试内容"
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 2000L
        val markTimeOffset = 500L // 在开始时间之前

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回0
        assertEquals(0, result)
    }

    @Test
    fun should_return_content_length_when_calculateMarkFlagInsertPosition_with_mark_after_end_time() {
        // Given: 标记时间点在结束时间之后
        val asrContent = "测试内容"
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 2000L
        val markTimeOffset = 2500L // 在结束时间之后

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回内容长度
        assertEquals(asrContent.length, result)
    }

    @Test
    fun should_return_middle_position_when_calculateMarkFlagInsertPosition_with_mark_in_middle() {
        // Given: 标记时间点在中间位置
        val asrContent = "测试内容" // 4个字符
        val asrStartTimeOffset = 1000L
        val asrEndTimeOffset = 2000L
        val markTimeOffset = 1500L // 正好在中间

        // When: 调用计算方法
        val result = helper.calculateMarkFlagInsertPosition(
            asrContent, asrStartTimeOffset, asrEndTimeOffset, markTimeOffset
        )

        // Then: 返回中间位置（2）
        assertEquals(2, result)
    }

    // ========== getDisplaySubtitleEntries方法的测试用例 ==========

    @Test
    fun should_return_empty_list_when_getDisplaySubtitleEntries_with_no_entries() {
        // Given: 没有添加任何条目

        // When: 获取条目列表
        val result = helper.getDisplaySubtitleEntries()

        // Then: 返回空列表
        assertTrue(result.isEmpty())
    }

    @Test
    fun should_return_copy_of_entries_when_getDisplaySubtitleEntries_with_existing_entries() {
        // Given: 已添加一些条目
        val convertContentItem = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "测试内容"
        )
        helper.updateSubtitleContent(listOf(convertContentItem), emptyList())

        // When: 获取条目列表
        val result = helper.getDisplaySubtitleEntries()

        // Then: 返回包含条目的列表副本
        assertEquals(1, result.size)
        assertEquals("测试内容", result[0].displayContent)
    }

    // ========== getMarkDataBeans方法的测试用例 ==========

    @Test
    fun should_return_empty_list_when_getMarkDataBeans_with_no_marks() {
        // Given: 没有添加任何标记

        // When: 获取标记列表
        val result = helper.getMarkDataBeans()

        // Then: 返回空列表
        assertTrue(result.isEmpty())
    }

    @Test
    fun should_return_copy_of_marks_when_getMarkDataBeans_with_existing_marks() {
        // Given: 已添加一些标记
        val markDataBeans = listOf(
            MarkDataBean(1500L),
            MarkDataBean(2500L)
        )
        helper.updateMarkDataBeans(markDataBeans)

        // When: 获取标记列表
        val result = helper.getMarkDataBeans()

        // Then: 返回包含标记的列表副本
        assertEquals(2, result.size)
        assertEquals(1500L, result[0].timeInMills)
        assertEquals(2500L, result[1].timeInMills)
    }

    // ========== updateMarkDataBeans方法的测试用例 ==========

    @Test
    fun should_update_marks_and_redistribute_when_updateMarkDataBeans_with_new_marks() {
        // Given: 已有字幕条目
        val subtitle1 = ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一段")
        val subtitle2 = ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "第二段")
        helper.updateSubtitleContent(listOf(subtitle1, subtitle2), emptyList())

        val newMarkDataBeans = listOf(
            MarkDataBean(1500L), // 在第一段中
            MarkDataBean(3500L)  // 在第二段中
        )

        // When: 更新标记数据
        helper.updateMarkDataBeans(newMarkDataBeans)

        // Then: 标记应该被重新分配到对应的字幕条目中
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 第一段应该有一个标记，插入在中间位置
        assertEquals(1, entries[0].insertedMarks.size)
        assertEquals(1500L, entries[0].insertedMarks[0].timeOffset)
        assertEquals("第\u2691一段", entries[0].displayContent)

        // 第二段应该有一个标记，插入在中间位置
        assertEquals(1, entries[1].insertedMarks.size)
        assertEquals(3500L, entries[1].insertedMarks[0].timeOffset)
        assertEquals("第\u2691二段", entries[1].displayContent)
    }

    @Test
    fun should_clear_existing_marks_when_updateMarkDataBeans_with_empty_list() {
        // Given: 已有字幕条目和标记
        val subtitle = ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "测试内容")
        helper.updateSubtitleContent(listOf(subtitle), emptyList())
        helper.updateMarkDataBeans(listOf(MarkDataBean(1500L)))

        // 验证标记已添加
        val entriesBeforeUpdate = helper.getDisplaySubtitleEntries()
        assertEquals(1, entriesBeforeUpdate[0].insertedMarks.size)
        assertEquals("测试\u2691内容", entriesBeforeUpdate[0].displayContent)

        // When: 更新为空的标记列表
        helper.updateMarkDataBeans(emptyList())

        // Then: 所有标记应该被清空
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        assertEquals(0, entries[0].insertedMarks.size)
        assertEquals("测试内容", entries[0].displayContent)
    }

    @Test
    fun should_redistribute_marks_when_updateMarkDataBeans_with_marks_for_different_entries() {
        // Given: 已有字幕条目
        val subtitle1 = ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一段")
        val subtitle2 = ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "第二段")
        helper.updateSubtitleContent(listOf(subtitle1, subtitle2), emptyList())

        val markDataBeans = listOf(
            MarkDataBean(500L),   // 在第一段之前
            MarkDataBean(1500L),  // 在第一段中
            MarkDataBean(2500L),  // 在两段之间，应该分配给第一段
            MarkDataBean(3500L),  // 在第二段中
            MarkDataBean(5000L)   // 在第二段之后
        )

        // When: 更新标记数据
        helper.updateMarkDataBeans(markDataBeans)

        // Then: 标记应该被正确分配
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 第一段应该有3个标记（500L在开头, 1500L在中间, 2500L在末尾）
        assertEquals(3, entries[0].insertedMarks.size)
        val firstEntryTimeOffsets = entries[0].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(500L, 1500L, 2500L), firstEntryTimeOffsets)
        assertEquals("\u2691第\u2691一段\u2691", entries[0].displayContent)

        // 第二段应该有2个标记（3500L在中间, 5000L在末尾）
        assertEquals(2, entries[1].insertedMarks.size)
        val secondEntryTimeOffsets = entries[1].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(3500L, 5000L), secondEntryTimeOffsets)
        assertEquals("第\u2691二段\u2691", entries[1].displayContent)
    }

    // ========== updateSubtitleContent方法的测试用例 ==========

    @Test
    fun should_add_new_entries_when_updateSubtitleContent_with_completed_subtitles() {
        // Given: 已完成的字幕列表
        val completedSubtitles = listOf(
            ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一段"),
            ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "第二段")
        )

        // When: 更新字幕内容
        helper.updateSubtitleContent(completedSubtitles, emptyList())

        // Then: 应该添加所有字幕条目
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("第一段", entries[0].displayContent)
        assertEquals("第二段", entries[1].displayContent)
    }

    @Test
    fun should_add_recognizing_subtitle_when_updateSubtitleContent_with_recognizing_subtitles() {
        // Given: 已完成的字幕和正在识别的字幕
        val completedSubtitles = listOf(
            ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "已完成")
        )
        val recognizingSubtitles = listOf(
            ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "正在识别")
        )

        // When: 更新字幕内容
        helper.updateSubtitleContent(completedSubtitles, recognizingSubtitles)

        // Then: 应该包含所有字幕条目
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("已完成", entries[0].displayContent)
        assertEquals("正在识别", entries[1].displayContent)
    }

    @Test
    fun should_update_recognizing_subtitle_when_updateSubtitleContent_with_same_start_time() {
        // Given: 已有已完成字幕和正在识别字幕
        val completedSubtitle = ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "已完成内容")
        val initialRecognizing = ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "初始识别")
        helper.updateSubtitleContent(listOf(completedSubtitle), listOf(initialRecognizing))

        val updatedRecognizing = ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "更新识别内容")

        // When: 更新正在识别的字幕内容
        helper.updateSubtitleContent(listOf(completedSubtitle), listOf(updatedRecognizing))

        // Then: 应该更新正在识别的条目，已完成的条目保持不变
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("已完成内容", entries[0].displayContent) // 已完成的不变
        assertEquals("更新识别内容", entries[1].displayContent) // 正在识别的更新
    }

    @Test
    fun should_remove_recognizing_subtitle_when_updateSubtitleContent_with_empty_recognizing() {
        // Given: 已有已完成字幕和正在识别字幕
        val completedSubtitles = listOf(
            ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一段"),
            ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "第二段")
        )
        val recognizingSubtitle = ConvertContentItem(startTime = 5000L, endTime = 6000L, textContent = "正在识别")
        helper.updateSubtitleContent(completedSubtitles, listOf(recognizingSubtitle))

        // When: 识别完成，移除正在识别的字幕（模拟识别完成后移到已完成列表的情况）
        helper.updateSubtitleContent(completedSubtitles, emptyList())

        // Then: 应该移除正在识别的条目，已完成的条目保持不变
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("第一段", entries[0].displayContent)
        assertEquals("第二段", entries[1].displayContent)
    }

    @Test
    fun should_append_new_completed_subtitle_when_updateSubtitleContent_with_additional_completed() {
        // Given: 已有已完成字幕
        val initialCompletedSubtitles = listOf(
            ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一段")
        )
        helper.updateSubtitleContent(initialCompletedSubtitles, emptyList())

        // When: 追加新的已完成字幕（模拟识别完成后从recognizing移到completed的情况）
        val extendedCompletedSubtitles = initialCompletedSubtitles + listOf(
            ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "第二段")
        )
        helper.updateSubtitleContent(extendedCompletedSubtitles, emptyList())

        // Then: 应该追加新的已完成字幕，原有字幕保持不变
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("第一段", entries[0].displayContent)
        assertEquals("第二段", entries[1].displayContent)
    }

    @Test
    fun should_redistribute_marks_when_updateSubtitleContent_with_existing_marks() {
        // Given: 已有标记数据
        val markDataBeans = listOf(
            MarkDataBean(1500L),
            MarkDataBean(3500L)
        )
        helper.updateMarkDataBeans(markDataBeans)

        val subtitles = listOf(
            ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "第一段"),
            ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "第二段")
        )

        // When: 更新字幕内容
        helper.updateSubtitleContent(subtitles, emptyList())

        // Then: 标记应该被重新分配到对应的字幕条目中
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 验证标记被正确分配
        assertEquals(1, entries[0].insertedMarks.size)
        assertEquals(1500L, entries[0].insertedMarks[0].timeOffset)
        assertEquals("第\u2691一段", entries[0].displayContent)

        assertEquals(1, entries[1].insertedMarks.size)
        assertEquals(3500L, entries[1].insertedMarks[0].timeOffset)
        assertEquals("第\u2691二段", entries[1].displayContent)
    }

    @Test
    fun should_handle_recognizing_subtitle_update_when_updateSubtitleContent_with_changing_recognizing_content() {
        // Given: 已完成的字幕和初始的正在识别字幕
        val completedSubtitle = ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "已完成")
        val initialRecognizing = ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "正在")
        helper.updateSubtitleContent(listOf(completedSubtitle), listOf(initialRecognizing))

        val updatedRecognizing = ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "正在识别中")

        // When: 更新正在识别的字幕内容
        helper.updateSubtitleContent(listOf(completedSubtitle), listOf(updatedRecognizing))

        // Then: 正在识别的字幕应该被更新
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("已完成", entries[0].displayContent)
        assertEquals("正在识别中", entries[1].displayContent)
    }

    // ========== 集成测试用例 - 使用场景示例 ==========

    @Test
    fun integration_test_asr_recognition_process_with_mark_redistribution() {
        /**
         * 使用场景示例：模拟完整的ASR识别过程中的字幕更新和标记重新分配
         *
         * 这个集成测试展示了SubtitleMarkInsertHelper在实际使用中的完整流程：
         * 1. ASR识别过程中字幕内容的动态更新
         * 2. 标记数据的管理和重新分配
         * 3. 字幕内容变化时标记位置的自动调整
         */

        // Given: 初始化helper
        val helper = SubtitleMarkInsertHelper()

        // ========== 阶段1：初始化字幕内容 ==========
        // 模拟ASR识别过程 - 第一阶段：已完成的字幕
        val completedSubtitles = listOf(
            ConvertContentItem(
                startTime = 1000L,
                endTime = 5000L,
                textContent = "这是第一段已完成的字幕内容"
            )
        )

        // 正在识别中的字幕（内容会逐渐变化）
        val recognizingSubtitle = ConvertContentItem(
            startTime = 6000L,
            endTime = 10000L,
            textContent = "这是第二段正在"
        )

        // When: 更新字幕内容
        helper.updateSubtitleContent(completedSubtitles, listOf(recognizingSubtitle))

        // Then: 验证初始字幕状态
        var entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("这是第一段已完成的字幕内容", entries[0].displayContent)
        assertEquals("这是第二段正在", entries[1].displayContent)

        // ========== 阶段2：添加标记数据 ==========
        val markDataBeans = listOf(
            MarkDataBean(2000L),  // 在第一段中
            MarkDataBean(3500L),  // 在第一段中
            MarkDataBean(7000L),  // 在第二段中
            MarkDataBean(11000L)  // 在第二段之后
        )

        // When: 更新标记数据
        helper.updateMarkDataBeans(markDataBeans)

        // Then: 验证标记被正确分配
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 第一段应该有2个标记
        assertEquals(2, entries[0].insertedMarks.size)
        val firstEntryMarks = entries[0].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(2000L, 3500L), firstEntryMarks)
        assertEquals("这是第\u2691一段已完成\u2691的字幕内容", entries[0].displayContent)

        // 第二段应该有2个标记（7000L在段内，11000L在段后）
        assertEquals(2, entries[1].insertedMarks.size)
        val secondEntryMarks = entries[1].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(7000L, 11000L), secondEntryMarks)
        assertEquals("这\u2691是第二段正在\u2691", entries[1].displayContent)

        // ========== 阶段3：模拟ASR识别过程中的字幕内容更新 ==========
        // 正在识别的字幕内容逐渐增加
        val updatedRecognizing1 = ConvertContentItem(
            startTime = 6000L,
            endTime = 10000L,
            textContent = "这是第二段正在识别的"
        )

        // When: 更新正在识别的字幕
        helper.updateSubtitleContent(completedSubtitles, listOf(updatedRecognizing1))

        // Then: 验证字幕内容更新，标记重新分配
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("这是第\u2691一段已完成\u2691的字幕内容", entries[0].displayContent)
        assertEquals("这是第二段正在识别的", entries[1].originContent.textContent)

        // 标记应该保持不变
        assertEquals(2, entries[0].insertedMarks.size)
        assertEquals(2, entries[1].insertedMarks.size)
        assertEquals("这是\u2691第二段正在识别的\u2691", entries[1].displayContent)

        // ========== 阶段4：继续识别，内容进一步增加 ==========
        val updatedRecognizing2 = ConvertContentItem(
            startTime = 6000L,
            endTime = 10000L,
            textContent = "这是第二段正在识别的字幕内容"
        )

        // When: 继续更新识别内容
        helper.updateSubtitleContent(completedSubtitles, listOf(updatedRecognizing2))

        // Then: 验证内容更新，标记位置重新计算
        entries = helper.getDisplaySubtitleEntries()
        assertEquals("这是第二段正在识别的字幕内容", entries[1].originContent.textContent)
        assertEquals(2, entries[1].insertedMarks.size)
        assertEquals("这是第\u2691二段正在识别的字幕内容\u2691", entries[1].displayContent)

        // ========== 阶段5：识别完成，字幕移到已完成列表 ==========
        val finalCompletedSubtitles = listOf(
            ConvertContentItem(
                startTime = 1000L,
                endTime = 5000L,
                textContent = "这是第一段已完成的字幕内容"
            ),
            ConvertContentItem(
                startTime = 6000L,
                endTime = 10000L,
                textContent = "这是第二段正在识别的字幕内容"
            )
        )

        // When: 识别完成，移到已完成列表
        helper.updateSubtitleContent(finalCompletedSubtitles, emptyList())

        // Then: 验证最终状态
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals("这是第\u2691一段已完成\u2691的字幕内容", entries[0].displayContent)
        assertEquals("这是第二段正在识别的字幕内容", entries[1].originContent.textContent)

        // 所有标记应该保持正确分配
        assertEquals(2, entries[0].insertedMarks.size)
        assertEquals(2, entries[1].insertedMarks.size)

        // 验证标记数据与helper中的一致
        val marks = helper.getMarkDataBeans()
        assertEquals(4, marks.size)
        val allMarkTimes = marks.map { it.timeInMills }.sorted()
        assertEquals(listOf(2000L, 3500L, 7000L, 11000L), allMarkTimes)

        // ========== 阶段6：添加新的标记 ==========
        val newMarkDataBeans = markDataBeans + listOf(
            MarkDataBean(4000L),  // 在第一段中新增
            MarkDataBean(8000L)   // 在第二段中新增
        )

        // When: 添加新标记
        helper.updateMarkDataBeans(newMarkDataBeans)

        // Then: 验证新标记被正确分配
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 第一段现在应该有3个标记
        assertEquals(3, entries[0].insertedMarks.size)
        val updatedFirstEntryMarks = entries[0].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(2000L, 3500L, 4000L), updatedFirstEntryMarks)
        assertEquals("这是第\u2691一段已完成\u2691的\u2691字幕内容", entries[0].displayContent)

        // 第二段现在应该有3个标记
        assertEquals(3, entries[1].insertedMarks.size)
        val updatedSecondEntryMarks = entries[1].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(7000L, 8000L, 11000L), updatedSecondEntryMarks)
        assertEquals("这是第\u2691二段正在\u2691识别的字幕内容\u2691", entries[1].displayContent)

        // ========== 阶段7：演示新增第三段字幕的标记重新分配 ==========
        // 模拟继续录音，新增第三段字幕
        val extendedCompletedSubtitles = finalCompletedSubtitles + listOf(
            ConvertContentItem(
                startTime = 11000L,
                endTime = 15000L,
                textContent = "这是新增的第三段字幕内容"
            )
        )

        // 添加针对第三段的新标记
        val extendedMarkDataBeans = newMarkDataBeans + listOf(
            MarkDataBean(12000L),  // 在第三段中
            MarkDataBean(13500L)   // 在第三段中
        )

        // When: 添加新字幕和新标记
        helper.updateMarkDataBeans(extendedMarkDataBeans)
        helper.updateSubtitleContent(extendedCompletedSubtitles, emptyList())

        // Then: 验证新字幕和标记被正确处理
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(3, entries.size)

        // 前两段字幕内容和标记保持不变
        assertEquals("这是第一段已完成的字幕内容", entries[0].originContent.textContent)
        assertEquals("这是第二段正在识别的字幕内容", entries[1].originContent.textContent)
        assertEquals("这是新增的第三段字幕内容", entries[2].originContent.textContent)

        // 验证标记分配
        assertEquals(3, entries[0].insertedMarks.size) // 第一段：2000L, 3500L, 4000L
        assertEquals(2, entries[1].insertedMarks.size) // 第二段：7000L, 8000L
        assertEquals(3, entries[2].insertedMarks.size) // 第三段：11000L, 12000L, 13500L

        // 验证第二段的标记
        val finalSecondEntryMarks = entries[1].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(7000L, 8000L), finalSecondEntryMarks)

        // 验证第三段的标记位置
        val thirdEntryMarks = entries[2].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(11000L, 12000L, 13500L), thirdEntryMarks)
        assertEquals("\u2691这是新\u2691增的第三\u2691段字幕内容", entries[2].displayContent)
    }

    /**
     * 使用场景示例：基本使用流程
     *
     * 这个测试展示了SubtitleMarkInsertHelper的基本使用方式：
     * 1. 初始化字幕内容
     * 2. 添加标记数据
     * 3. 获取带标记的字幕显示内容
     */
    @Test
    fun integration_test_basic_usage_scenario() {
        // Given: 初始化helper
        val helper = SubtitleMarkInsertHelper()

        // When: 设置字幕内容
        val subtitles = listOf(
            ConvertContentItem(startTime = 1000L, endTime = 3000L, textContent = "第一段字幕"),
            ConvertContentItem(startTime = 4000L, endTime = 6000L, textContent = "第二段字幕")
        )
        helper.updateSubtitleContent(subtitles, emptyList())

        // And: 添加标记
        val marks = listOf(
            MarkDataBean(1500L), // 在第一段中间
            MarkDataBean(5000L)  // 在第二段中间
        )
        helper.updateMarkDataBeans(marks)

        // Then: 验证结果
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 第一段字幕应该包含标记
        assertEquals("第一段字幕", entries[0].originContent.textContent)
        assertEquals("第\u2691一段字幕", entries[0].displayContent)
        assertEquals(1, entries[0].insertedMarks.size)
        assertEquals(1500L, entries[0].insertedMarks[0].timeOffset)

        // 第二段字幕应该包含标记
        assertEquals("第二段字幕", entries[1].originContent.textContent)
        assertEquals("第二\u2691段字幕", entries[1].displayContent)
        assertEquals(1, entries[1].insertedMarks.size)
        assertEquals(5000L, entries[1].insertedMarks[0].timeOffset)

        // 验证标记数据
        val markData = helper.getMarkDataBeans()
        assertEquals(2, markData.size)
        assertEquals(listOf(1500L, 5000L), markData.map { it.timeInMills }.sorted())
    }

    /**
     * 使用场景示例：实时字幕更新场景
     *
     * 模拟录音过程中实时字幕更新的场景：
     * 1. 已有标记数据
     * 2. ASR实时输出字幕内容
     * 3. 标记自动插入到合适位置
     */
    @Test
    fun integration_test_real_time_subtitle_update_scenario() {
        // Given: 初始化helper并设置标记
        val helper = SubtitleMarkInsertHelper()
        val marks = listOf(
            MarkDataBean(2000L),
            MarkDataBean(4000L),
            MarkDataBean(6000L)
        )
        helper.updateMarkDataBeans(marks)

        // When: 第一次ASR输出
        val firstSubtitle = ConvertContentItem(
            startTime = 1000L,
            endTime = 3000L,
            textContent = "你好"
        )
        helper.updateSubtitleContent(listOf(firstSubtitle), emptyList())

        // Then: 验证所有标记都被插入到第一个字幕中（因为只有一个字幕）
        var entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        assertEquals(3, entries[0].insertedMarks.size) // 所有3个标记都会分配给第一个字幕
        val timeOffsets = entries[0].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(2000L, 4000L, 6000L), timeOffsets)
        assertEquals("你\u2691好\u2691\u2691", entries[0].displayContent) // 2000L在中间，4000L和6000L在末尾

        // When: 第二次ASR输出，新增字幕
        val secondSubtitle = ConvertContentItem(
            startTime = 3500L,
            endTime = 5500L,
            textContent = "世界"
        )
        helper.updateSubtitleContent(listOf(firstSubtitle, secondSubtitle), emptyList())

        // Then: 验证标记重新分配
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)
        assertEquals(1, entries[0].insertedMarks.size) // 第一段现在只有2000L
        assertEquals("你\u2691好", entries[0].displayContent)
        assertEquals(2, entries[1].insertedMarks.size) // 第二段有4000L和6000L
        val secondEntryTimeOffsets = entries[1].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(4000L, 6000L), secondEntryTimeOffsets)
        assertEquals("\u2691世界\u2691", entries[1].displayContent)

        // When: 第三次ASR输出，继续新增字幕
        val thirdSubtitle = ConvertContentItem(
            startTime = 6500L,
            endTime = 8500L,
            textContent = "欢迎使用"
        )
        helper.updateSubtitleContent(
            listOf(firstSubtitle, secondSubtitle, thirdSubtitle),
            emptyList()
        )

        // Then: 验证标记重新分配
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(3, entries.size)
        assertEquals(1, entries[0].insertedMarks.size) // 2000L
        assertEquals("你\u2691好", entries[0].displayContent)
        assertEquals(2, entries[1].insertedMarks.size) // 4000L和6000L (6000L在6500L之前，分配给第二段)
        val secondEntryMarks = entries[1].insertedMarks.map { it.timeOffset }.sorted()
        assertEquals(listOf(4000L, 6000L), secondEntryMarks)
        assertEquals("\u2691世界\u2691", entries[1].displayContent)
        assertEquals(0, entries[2].insertedMarks.size) // 第三段没有标记
        assertEquals("欢迎使用", entries[2].displayContent)
    }

    // ========== isFromCompletedSubtitles标志的测试用例 ==========

    @Test
    fun should_set_correct_source_flag_when_updateSubtitleContent_with_completed_and_recognizing() {
        // Given: 已完成的字幕和正在识别的字幕
        val completedSubtitles = listOf(
            ConvertContentItem(startTime = 1000L, endTime = 2000L, textContent = "已完成字幕")
        )
        val recognizingSubtitles = listOf(
            ConvertContentItem(startTime = 3000L, endTime = 4000L, textContent = "正在识别字幕")
        )

        // When: 更新字幕内容
        helper.updateSubtitleContent(completedSubtitles, recognizingSubtitles)

        // Then: 验证来源标志设置正确
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 已完成的字幕标志应该为true
        assertTrue("已完成字幕的isFromCompletedSubtitles应该为true", entries[0].isFromCompletedSubtitles)
        assertEquals("已完成字幕", entries[0].displayContent)

        // 正在识别的字幕标志应该为false
        assertFalse("正在识别字幕的isFromCompletedSubtitles应该为false", entries[1].isFromCompletedSubtitles)
        assertEquals("正在识别字幕", entries[1].displayContent)
    }

    @Test
    fun should_update_source_flag_when_recognizing_subtitle_becomes_completed() {
        // Given: 初始状态 - 一个正在识别的字幕
        val recognizingSubtitle = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "正在识别的内容"
        )
        helper.updateSubtitleContent(emptyList(), listOf(recognizingSubtitle))

        // 验证初始状态
        var entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        assertFalse("初始状态应该是正在识别", entries[0].isFromCompletedSubtitles)

        // When: 识别完成，移动到已完成列表
        val completedSubtitle = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "已完成的内容"
        )
        helper.updateSubtitleContent(listOf(completedSubtitle), emptyList())

        // Then: 验证标志已更新
        entries = helper.getDisplaySubtitleEntries()
        assertEquals(1, entries.size)
        assertTrue("识别完成后应该标记为已完成", entries[0].isFromCompletedSubtitles)
        assertEquals("已完成的内容", entries[0].displayContent)
    }

    @Test
    fun should_maintain_source_flag_when_updating_existing_entries() {
        // Given: 已有已完成和正在识别的字幕
        val completedSubtitle = ConvertContentItem(
            startTime = 1000L,
            endTime = 2000L,
            textContent = "已完成内容"
        )
        val recognizingSubtitle = ConvertContentItem(
            startTime = 3000L,
            endTime = 4000L,
            textContent = "正在识别"
        )
        helper.updateSubtitleContent(listOf(completedSubtitle), listOf(recognizingSubtitle))

        // When: 更新正在识别的字幕内容
        val updatedRecognizing = ConvertContentItem(
            startTime = 3000L,
            endTime = 4000L,
            textContent = "正在识别更新内容"
        )
        helper.updateSubtitleContent(listOf(completedSubtitle), listOf(updatedRecognizing))

        // Then: 验证标志保持正确
        val entries = helper.getDisplaySubtitleEntries()
        assertEquals(2, entries.size)

        // 已完成的字幕标志保持不变
        assertTrue("已完成字幕标志应该保持为true", entries[0].isFromCompletedSubtitles)
        assertEquals("已完成内容", entries[0].displayContent)

        // 正在识别的字幕标志保持不变
        assertFalse("正在识别字幕标志应该保持为false", entries[1].isFromCompletedSubtitles)
        assertEquals("正在识别更新内容", entries[1].displayContent)
    }
}
