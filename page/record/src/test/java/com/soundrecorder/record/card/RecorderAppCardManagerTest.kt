/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderAppCardManagerTest
 Description:
 Version: 1.0
 Date: 2022/9/22
 Author: W9013333(v-zhengt<PERSON><PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/9/22 1.0 create
 */

package com.soundrecorder.record.card

import android.os.Build
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.recorder.MarkAction
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.recorder.SaveFileState
import com.soundrecorder.modulerouter.recorder.WaveState
import com.soundrecorder.record.shadows.ShadowClearDataUtils
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.record.shadows.ShadowOplusCompactUtil
import com.soundrecorder.record.shadows.ShadowOplusUsbEnvironment
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import io.mockk.every
import io.mockk.mockk
import oplus.multimedia.soundrecorder.card.dragonFly.RecorderAppCardManager
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.mockito.Mockito
import org.mockito.Mockito.spy
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowOS12FeatureUtil::class,
        ShadowFeatureOption::class, ShadowClearDataUtils::class, ShadowOplusCompactUtil::class]
)
class RecorderAppCardManagerTest {
    private val mockStatic = Mockito.mockStatic(PermissionUtils::class.java)

    private val recorderViewModelApi = mockk<RecorderServiceInterface>()

    private val koinApp = koinApplication {
        modules(module {
            single { recorderViewModelApi }
        })
    }

    @After
    fun release() {
        mockStatic.close()
        stopKoin()
    }

    @Before
    fun setUp() {
        startKoin(koinApp)
    }

    @Test
    fun onPack() {
        RecordStatusManager.setCurrentStatus(RecordStatusManager.INIT)
        RecorderAppCardManager.getCustomData("0&0&0")
        RecordStatusManager.setCurrentStatus(RecordStatusManager.PAUSED)
        RecorderAppCardManager.getCustomData("0&0&0")
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING)
        RecorderAppCardManager.getCustomData("0&0&0")
        RecordStatusManager.setCurrentStatus(RecordStatusManager.HALT_ON)
        RecorderAppCardManager.getCustomData("0&0&0")
        recorderViewModelApi.saveFileState = SaveFileState.INIT
        RecorderAppCardManager.getCustomData("0&0&0")
        recorderViewModelApi.saveFileState = SaveFileState.ERROR
        RecorderAppCardManager.getCustomData("0&0&0")
        recorderViewModelApi.saveFileState = SaveFileState.START_LOADING
        RecorderAppCardManager.getCustomData("0&0&0")
        recorderViewModelApi.saveFileState = SaveFileState.SHOW_LOADING_DIALOG
        RecorderAppCardManager.getCustomData("0&0&0")
        recorderViewModelApi.saveFileState = SaveFileState.SUCCESS
        RecorderAppCardManager.getCustomData("0&0&0")
    }

    @Test
    fun parseMethod() {
        mockStatic.`when`<Boolean> { PermissionUtils.hasReadAudioPermission() }.thenReturn(true, true, false)
        mockStatic.`when`<Boolean> { PermissionUtils.hasRecordAudioPermission() }.thenReturn(true, true, false)
        RecorderAppCardManager.parseMethod("pull", "0&0&0")
        RecorderAppCardManager.parseMethod("start_recorder_service", "0&0&0")
        RecorderAppCardManager.parseMethod("switch_recorder_status", "0&0&0")
        RecorderAppCardManager.parseMethod("add_text_mark", "0&0&0")
        RecorderAppCardManager.parseMethod("save_recorder_file", "0&0&0")
        RecorderAppCardManager.parseMethod("on_visible", "")
        RecorderAppCardManager.parseMethod("on_in_visible", "")
        RecorderAppCardManager.parseMethod("check_start_service", "0&0&0")
        RecorderAppCardManager.parseMethod("check_recorder_permission", "0&0&0")
        mockStatic.reset()
    }

    @Test
    fun switchRecorderStatus() {
        RecorderAppCardManager.parseMethod("switch_recorder_status", "0&0&0")
        AudioModeChangeManager.changeAudioPause(true)
        RecorderAppCardManager.parseMethod("switch_recorder_status", "0&0&0")
    }

    @Test
    fun addWidgetCodesOnResume() {
        RecorderAppCardManager.addWidgetCodesOnResume("")
    }

    @Test
    fun removeWidgetCodeOnPause() {
        RecorderAppCardManager.addWidgetCodesOnResume("")
        RecorderAppCardManager.removeWidgetCodeOnPause("xxxxxx")
    }

    @Test
    fun checkRecorderPermission() {
        mockStatic.`when`<Int> { PermissionUtils.getNextAction() }.thenReturn(PermissionUtils.SHOULD_SHOW_USER_NOTICE)
        RecorderAppCardManager.parseMethod("check_recorder_permission", "0&0&0")

        mockStatic.`when`<Int> { PermissionUtils.getNextAction() }.thenReturn(PermissionUtils.SHOULD_SHOW_ALL_FILE_PERMISSION)
        mockStatic.`when`<Boolean> { PermissionUtils.hasReadAudioPermission() }.thenReturn(false)
        RecorderAppCardManager.parseMethod("check_recorder_permission", "0&0&0")

        mockStatic.`when`<Boolean> { PermissionUtils.hasReadAudioPermission() }.thenReturn(true)
        mockStatic.`when`<Boolean> { PermissionUtils.hasRecordAudioPermission() }.thenReturn(false)
        RecorderAppCardManager.parseMethod("check_recorder_permission", "0&0&0")

        mockStatic.`when`<Boolean> { PermissionUtils.hasRecordAudioPermission() }.thenReturn(true)
        RecorderAppCardManager.parseMethod("check_recorder_permission", "0&0&0")
        mockStatic.reset()
    }

    @Test
    fun onCloseService() {
        RecorderAppCardManager.onCloseService()
        RecorderAppCardManager.onWaveStateChange(WaveState.START)
        RecorderAppCardManager.onSaveFileStateChange(SaveFileState.SUCCESS)

        RecordStatusManager.changeRecordStatus(RecordStatusManager.INIT)
        BaseApplication.sNeedToNormalRingMode = false
        RecorderAppCardManager.onRecordStatusChange(RecordStatusManager.RECORDING)
        BaseApplication.sNeedToNormalRingMode = true
        RecorderAppCardManager.onRecordStatusChange(RecordStatusManager.RECORDING)
        RecordStatusManager.setCurrentStatus(RecordStatusManager.RECORDING)
        RecorderAppCardManager.onRecordCallConnected()

        RecorderAppCardManager.onMarkDataChange(MarkAction.SINGLE_ADD, 0)

        RecordStatusManager.setCurrentStatus(RecordStatusManager.PAUSED)
        AudioModeChangeManager.changeAudioPause(true)
        AudioModeChangeManager.changeNeedResume(true)
        RecorderAppCardManager.onRecordCallConnected()

        AudioModeChangeManager.changeAudioPause(true)
        AudioModeChangeManager.changeNeedResume(false)
        RecorderAppCardManager.onRecordCallConnected()

        AudioModeChangeManager.changeAudioPause(false)
        AudioModeChangeManager.changeNeedResume(false)
        RecorderAppCardManager.onRecordCallConnected()

        AudioModeChangeManager.changeAudioPause(false)
        AudioModeChangeManager.changeNeedResume(true)
        RecorderAppCardManager.onRecordCallConnected()

        AudioModeChangeManager.changeAudioPause(false)
        AudioModeChangeManager.changeNeedResume(false)
        RecorderAppCardManager.onRecordCallConnected()
    }

    @Test
    fun addTextMark() {
        val recorderService = spy(Mockito.mock(RecorderService::class.java))//mockk<RecorderService>()
        val markEnable = mockk<MutableLiveData<Boolean>>()//MutableLiveData<Boolean>()
        val list = mutableListOf<MarkDataBean>()
        Mockito.`when`(recorderService.markEnable).thenReturn(markEnable)

        every { markEnable.value } answers { false }
        Mockito.`when`(recorderService.getCurrentPlayerTime()).thenReturn(0)

        Mockito.`when`(recorderService.markDatas).thenReturn(list)
        Assert.assertFalse(RecorderViewModel.getInstance().hasInitRecorderService())
        switchRecorderStatus()
        RecorderAppCardManager.parseMethod("save_recorder_file", "0&0&0")
        RecorderAppCardManager.parseMethod("add_text_mark", "")

        RecorderViewModel.getInstance().onServiceCreate(recorderService)
        Assert.assertTrue(RecorderViewModel.getInstance().hasInitRecorderService())
        switchRecorderStatus()

        AudioModeChangeManager.changeAudioPause(true)
        Assert.assertTrue(AudioModeChangeManager.isAudioModeChangePause())
        RecorderAppCardManager.parseMethod("add_text_mark", "")

        AudioModeChangeManager.changeAudioPause(false)
        for (index in 0..50) {
            list.add(mockk())
        }
        RecorderAppCardManager.parseMethod("add_text_mark", "")
    }
}
