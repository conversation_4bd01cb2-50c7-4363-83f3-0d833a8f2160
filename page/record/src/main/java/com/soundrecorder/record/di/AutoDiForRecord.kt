/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForRecord.kt
 * * Description : AutoDiForRecord
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.record.di

import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.record.RecordApi
import org.koin.dsl.module

object AutoDiForRecord {
    val recordModule = module {
        single<RecordInterface>(createdAtStart = true) {
            RecordApi
        }
    }
}