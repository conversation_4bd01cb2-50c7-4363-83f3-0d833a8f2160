/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopPermissionManager.java
 Description:
 Version: 1.0
 Date: 2023-06-13
 Author: wangyu
 -----------Revision History-----------
 <author> <date> <version> <desc>
 wangyu 2023-06-13 create
 */

package com.soundrecorder.record.picturemark

import android.app.Activity
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultCallback
import androidx.activity.result.ActivityResultLauncher
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.permission.PermissionProxyActivity.Companion.RequestPermissionProxy
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.FunctionOption.putSupportPhotoMarkRecommend
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.record.RecordImagesPermissionTips.mShowReadImagePermissionTips
import com.soundrecorder.record.RecordImagesPermissionTips.showReadVisualUserSelectedPermissionSnackBar

class PopPermissionController(
    private val permissionCallback: IPermissionCallback
) {

    companion object {
        private const val TAG = "PopViewController"
    }

    /**
     * 多任务键/home键退出  下次回来在OnResume时再校验是否需要弹权限提示
     * 解决home键/多任务键按下，立刻收到回调，进入后台之前已经把提示弹出问题
     */
    var mPressHomeBtnNeedShowPermissionTipsNextOnResume = false

    private val mHandler = Handler(Looper.getMainLooper())

    private val mPermissionTipsShownRunnable = object : ParamRunnable {
        var rootView: View? = null

        override fun setRootView(rootView: View?): ParamRunnable {
            this.rootView = rootView
            return this
        }

        override fun run() {
            showReadImagePermissionTips(rootView)
        }
    }

    private var mRequestPermission: ActivityResultLauncher<Array<String>>? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    /**
     * 注册权限申请
     */
    fun registerImagesPermission(activity: ComponentActivity, controller: PopViewController) {
        mRequestPermission = activity.registerForActivityResult(
            RequestPermissionProxy(), ActivityResultCallback<Int?> { result ->
                checkReadImagePermission(activity, controller, result)
            })
    }

    /**
     * 检查权限提示是否需要弹出
     */
    fun checkReadImagePermissionTips() {
        /**
         * 该校验正在录制中目的有两个
         * 一、保证权限提示在录制界面且开始录制后弹出
         * 二、希望权限提示在静音提示之后
         * 此处使用isAlreadyRecording 低概率可能有小bug   真实场景用户很难复现
         */
        if (false == recorderViewModelApi?.isAlreadyRecording()) {
            DebugUtil.d(TAG, "current is not recording return")
            return
        }
        if (!mShowReadImagePermissionTips) {
            return
        }
        if (!PermissionUtils.hasOnlyReadVisualUserSelectedPermission()) {
            return
        }
        mPressHomeBtnNeedShowPermissionTipsNextOnResume = true
    }

    /**
     * 弹出权限提示
     */
    fun launchImagesPermissionTips(rootView: View?) {
        if ((mShowReadImagePermissionTips) && (mPressHomeBtnNeedShowPermissionTipsNextOnResume)) {
            mHandler.removeCallbacks(mPermissionTipsShownRunnable)
            mHandler.post(mPermissionTipsShownRunnable.setRootView(rootView))
        }
    }

    /**
     * release
     */
    fun release() {
        mHandler.removeCallbacksAndMessages(null)
        mRequestPermission = null
    }

    /**
     * 弹出照片与视频权限提示
     * permissionCallback.isForeground()
     * 确保弹出权限提示是在录制界面显示中，防止权限提示用户无法看见
     */
    private fun showReadImagePermissionTips(rootView: View?) {
        if (rootView == null) {
            DebugUtil.d(TAG, "wangyu rootView is null return")
            return
        }
        if ((permissionCallback.isForeground()) && (mShowReadImagePermissionTips)) {
            showReadVisualUserSelectedPermissionSnackBar(rootView.context, rootView,
                View.OnClickListener {
                    PermissionUtils.goToAppSettingConfigurePermissions(rootView.context as? Activity,
                        arrayListOf(PermissionUtils.READ_IMAGE_PERMISSION()))
                })
            mPressHomeBtnNeedShowPermissionTipsNextOnResume = false
            mShowReadImagePermissionTips = false
        } else if ((!mPressHomeBtnNeedShowPermissionTipsNextOnResume) && (mShowReadImagePermissionTips)) {
            mPressHomeBtnNeedShowPermissionTipsNextOnResume = true
        }
    }

    /**
     * 处理权限授予回调
     */
    private fun checkReadImagePermission(
        activity: Activity,
        controller: PopViewController,
        resultCode: Int?
    ) {
        if (PermissionUtils.hasReadImagesPermission()) {
            //拒绝权限无需重复再设置false
            putSupportPhotoMarkRecommend(true)
            //解决权限授予后首次无法使用图片标记问题
            controller.updateBackgroundValueFalse()
            ToastManager.showShortToast(
                activity,
                com.soundrecorder.common.R.string.images_permission_record_open_toast
            )
        } else {
            //注意  执行此方法PermissionUtils.hasReadVisualUserSelectedPermission()原则上为true,以后需要再此处处理其他权限，得注意
            ToastManager.showShortToast(
                activity,
                com.soundrecorder.common.R.string.images_permission_record_turned_on_toast
            )
        }
    }
}