<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.cardlist.COUICardListSelectedItemLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/dp16"
    android:padding="@dimen/dp16"
    android:orientation="vertical"
    app:couiCardListHorizontalMargin="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_play_setting_play_speed"
            style="@style/couiTextHeadlineXS"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/play_speed"
            android:textColor="?attr/couiColorLabelPrimary"
            app:layout_constraintBottom_toBottomOf="@id/tv_play_setting_speed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_play_setting_speed" />

        <TextView
            android:id="@+id/tv_play_setting_speed"
            style="@style/couiTextDescription"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="?attr/couiColorLabelPrimary"
            app:layout_constraintBottom_toBottomOf="@id/tv_play_setting_play_speed"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.coui.appcompat.seekbar.COUISectionSeekBar
            android:id="@+id/section_seek_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_play_setting_play_speed" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.coui.appcompat.cardlist.COUICardListSelectedItemLayout>
