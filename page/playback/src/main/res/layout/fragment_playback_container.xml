<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/coui_color_white">

        <include
            android:id="@+id/color_load_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toBottomOf="@id/appbar_layout"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="gone"
            layout="@layout/loading_animation_view"/>

        <FrameLayout
            android:id="@+id/body"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tab_layout">

            <FrameLayout
                android:id="@+id/fl_convert_audio_container"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewpager"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:importantForAccessibility="no" />

            <include
                android:id="@+id/buttonPanel"
                layout="@layout/activity_playback_button_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_convert_search_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appbar_layout" />

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:elevation="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:id="@+id/tool_bar_frame_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.coui.appcompat.toolbar.COUIToolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="@dimen/recorder_toolbar_height" />

            </LinearLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <com.coui.appcompat.segmentbutton.COUISegmentButtonLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp36"
            android:layout_gravity="center"
            android:visibility="gone"
            android:layout_marginTop="@dimen/dp12"
            android:layout_marginBottom="@dimen/dp12"
            android:layout_marginStart="@dimen/play_btn_control_marginHorizontal"
            android:layout_marginEnd="@dimen/play_btn_control_marginHorizontal"
            style="@style/SegmentButton.Tiny"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appbar_layout"
            app:layout_constraintBottom_toTopOf="@id/body"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>