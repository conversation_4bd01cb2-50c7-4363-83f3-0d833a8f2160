/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - PlaySettingFooterPreference
Description: 录音播放设置弹窗底部文案.
Version: 1.0
Date : 2024/1/16
Author: W9012748
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
W9012748  2024/1/16     1.0      create this file
*/
package com.soundrecorder.playback.view

import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.seekbar.COUISectionSeekBar
import com.coui.appcompat.seekbar.COUISeekBar
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.playback.R

class PlaySettingFooterPreference : COUIPreference {

    companion object {
        private const val TAG = "PlaySettingFooterPreference"
    }

    private var onBindOrClickListener: OnBindOrClickListener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    override fun onBindViewHolder(holder: PreferenceViewHolder?) {
        super.onBindViewHolder(holder)
        val playSeed = holder?.itemView?.findViewById<TextView>(R.id.tv_play_setting_speed)
        val sectionSeekBar = holder?.itemView?.findViewById<COUISectionSeekBar>(R.id.section_seek_bar)
        playSeed?.let {
            sectionSeekBar?.let { seekBar ->
                seekBar.max = NumberConstant.NUM_3
                seekBar.setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
                    override fun onProgressChanged(p0: COUISeekBar?, p1: Int, p2: Boolean) {
                        DebugUtil.d(TAG, "onProgressChanged $p1")
                        onBindOrClickListener?.onProgressChanged(p1)
                    }

                    override fun onStartTrackingTouch(p0: COUISeekBar?) {}

                    override fun onStopTrackingTouch(p0: COUISeekBar?) {}
                })
                onBindOrClickListener?.onBind(it, seekBar)
            }
        }
    }

    fun setListener(onBindOrClickListener: OnBindOrClickListener) {
        this.onBindOrClickListener = onBindOrClickListener
    }

    interface OnBindOrClickListener {
        fun onBind(playSeed: TextView, sectionSeekBar: COUISectionSeekBar)
        fun onProgressChanged(progress: Int)
    }
}