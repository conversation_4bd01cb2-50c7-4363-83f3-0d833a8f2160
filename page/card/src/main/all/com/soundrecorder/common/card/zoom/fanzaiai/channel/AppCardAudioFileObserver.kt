/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AppCardAudioFileObserver.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel

import android.content.Intent
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.card.zoom.fanzaiai.channel.processor.AudioProgressSimulator
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.utils.Injector

object AppCardAudioFileObserver {

    private const val TAG = "AppCardAudioFileObserver"

    @Volatile
    private var audioFileInfo: AudioFileInfo = AudioFileInfo()

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    /**
     * 返回值：是否需要更新。判断除time属性，其他相同则不用更新。
     */
    fun updateAudioFileInfo(newestAudio: AudioFileInfo? = DataQueryUtil.getNewestAudioInfo()): Boolean {
        var isNeedUpdate = false
        if (newestAudio == null) {
            DebugUtil.w(TAG, "updateAudioFileInfo audioInfo is null")
            audioFileInfo.reset()
            return false
        }
        if (!audioFileInfo.equalsExcludeTime(newestAudio)) {
            audioFileInfo = DataQueryUtil.processSummaryOrConvertStatus(newestAudio)
            isNeedUpdate = true
        }
        return isNeedUpdate
    }

    fun getAudioFileInfo(): AudioFileInfo {
        return audioFileInfo
    }

    @JvmStatic
    fun genNoteJumpUri(noteData: String?, recordUUID: String?): String {
        runCatching {
            val jumpIntent = Intent()
            jumpIntent.setPackage(AppUtil.getNotesPackageName())
            jumpIntent.setAction("com.oplus.note.action.OPEN_SUMMARY_NOTE")
            jumpIntent.putExtra("speech_log_id", recordUUID)
            jumpIntent.putExtra("note_id", noteData)
            jumpIntent.putExtra("from_package", BaseUtil.getPackageName())
            return jumpIntent.toUri(Intent.URI_ANDROID_APP_SCHEME)
        }.onFailure {
            DebugUtil.e(TAG, "genNoteJumpUri has error=${it.message}")
        }
        return ""
    }

    @JvmStatic
    fun genTextJumpUri(audioFileInfo: AudioFileInfo): String {
        runCatching {
            //PlaybackActivity已废弃，确定跳转PlaybackActivity？
            val jumpIntent = playbackApi?.createPlayBackIntent(BaseApplication.getAppContext())
            jumpIntent?.let {
                it.setPackage(BaseApplication.getAppContext().packageName)
                it.putExtra("jumpFrom", "AppCard")
                it.putExtra("playPath", audioFileInfo.data)
                it.putExtra("recordId", audioFileInfo.mediaId)
                it.putExtra("duration", audioFileInfo.mDuration)
                it.putExtra("autoPlay", false)
                // 文本详情fragment在viewpager的位置
                it.putExtra("selectPos", 1)
                return it.toUri(Intent.URI_INTENT_SCHEME)
            }
        }.onFailure {
            DebugUtil.e(TAG, "genTextJumpUri has error=${it.message}")
        }
        return ""
    }

    /**
     * 获取录音权限的activity
     */
    @JvmStatic
    fun genTransparentActivityJumpUri(clickCode: Int): String {
        runCatching {
            // 与TransparentActivity的ACTION_SUMMARY_CARD相同
            val intent = Intent("oplus.intent.action.com.soundrecorder.SUMMARY_CARD").apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                setPackage(BaseUtil.getPackageName())
                putExtra("type", clickCode)
            }
            return intent.toUri(Intent.URI_ANDROID_APP_SCHEME)
        }.onFailure {
            DebugUtil.e(TAG, "genNoteJumpUri has error=${it.message}")
        }
        return ""
    }

    /**
     * 通道打开的时候，发送初始化数据
     */
    fun onOpenSendData() {
        AudioProgressSimulator.stopSendProgress()
        updateAudioFileInfo()
        AppCardMessageSender.sendMessage(audioFileInfo)
        DebugUtil.d(TAG, "onOpenSendData")
    }
}