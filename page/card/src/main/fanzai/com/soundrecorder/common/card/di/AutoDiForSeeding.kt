/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForSeeding.kt
 * * Description : AutoDiForSeeding
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.common.card.di

import com.soundrecorder.common.card.api.SeedlingApi
import com.soundrecorder.modulerouter.SeedingInterface
import org.koin.dsl.module

object AutoDiForSeeding {
    val seedingModule = module {
        single<SeedingInterface>(createdAtStart = true) {
            SeedlingApi
        }
    }
}