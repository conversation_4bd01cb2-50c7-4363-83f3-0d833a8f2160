/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingSdkApi
 * * Description :  泛在sdk开放api
 * * Version     : 1.0
 * * Date        : 2023/9/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.api

import android.content.Context
import android.os.Handler
import android.os.Looper
import com.oplus.pantanal.seedling.bean.CancelPanelActionConfigEnum
import com.oplus.pantanal.seedling.bean.PanelActionEnum
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.oplus.pantanal.seedling.bean.SeedlingHostEnum
import com.oplus.pantanal.seedling.bean.SeedlingIntent
import com.oplus.pantanal.seedling.bean.SeedlingIntentFlagEnum
import com.oplus.pantanal.seedling.intent.IIntentResultCallBack
import com.oplus.pantanal.seedling.update.SeedlingCardOptions
import com.oplus.pantanal.seedling.util.SeedlingTool
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.common.card.SeedingCardProcess
import com.soundrecorder.common.card.version.OSVersion14
import com.soundrecorder.common.card.version.OSVersion15
import com.soundrecorder.modulerouter.notification.NotificationInterface
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import org.json.JSONObject

object SeedlingSdkApi {

    private const val TAG = "SeedlingSdkApi"
    private const val FROM_UPDATE_SAVE_FILE_SMALL = "clickSaveAndDismiss"
    private const val SEEDLING_ACTION_STATUS_BAR = "pantanal.intent.business.app.system.RECORD"
    private const val KEY_SHOW_RECORD_VIEW = "showG1"
    private const val FLUID_CLOUD_RECORDE_END = "pages/savePage"
    private const val FLUID_CLOUD_RECORDE = "pages/index"

    /*流体云2.0的第一个upk版本号*/
    private const val FLUID_CLOUD_2_CODE = 3000001
    private const val KEY_SHOW_PROCESS_PERCENT = "processPercent"
    private const val SAVE_COMPLEDED_PROCESS = 100
    private const val PROGRESS_LOADING_DURATION_TIME = 900L
    private const val KEY_SHOW_SAVED_LOTTIE = "showSavedLottie"
    private const val KEY_SAVE_RECORD_FROM_WHERE = "saveRecordFromWhere"

    @JvmStatic
    var isShowingSaved = false

    /*标记服务来源，0：默认值，无来源 1：魔方*/
    private const val SERVICE_FROM = "service_from"
    private const val SERVICE_FROM_DEFAULT = 0
    private const val SERVICE_FROM_CUBE_BUTTON = 1

    private val lockScreenShowHostMapLocal by lazy {
        if (FeatureOption.isEnableCardFluidEntry()) {
            mapOf(SeedlingHostEnum.StatusBar to false, SeedlingHostEnum.Notification to true)
        } else {
            mapOf(SeedlingHostEnum.StatusBar to true, SeedlingHostEnum.Notification to false)
        }
    }

    private val showHostMapLocal by lazy {
        if (FeatureOption.isEnableCardFluidEntry()) {
            mutableMapOf(SeedlingHostEnum.StatusBar to true, SeedlingHostEnum.Notification to true)
        } else {
            mutableMapOf(SeedlingHostEnum.StatusBar to true, SeedlingHostEnum.Notification to false)
        }
    }

    private val notificationApi by lazy {
        Injector.injectFactory<NotificationInterface>()
    }

    private val currentId by lazy {
        notificationApi?.getNotificationIdByModeAndPage(
            notificationApi?.getNotificationMode(false), NotificationUtils.NOTIFICATION_PAGE_RECORD
        )
    }

    val osVersion by lazy {
        if (BaseUtil.isAndroidVOrLater) {
            OSVersion15()
        } else {
            OSVersion14()
        }
    }

    private val mainHandler by lazy { Handler(Looper.getMainLooper()) }

    @Volatile
    private var isSupportSystemSend: Boolean? = null

    @Volatile
    private var isSupportFluidCloud: Boolean? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    fun initSupportFluidCardCallback(context: Context, callback: (Boolean?, Boolean) -> Unit) {
        if (isSupportFluidCloud == null) {
            SeedlingTool.isSupportFluidCloud(context) {
                DebugUtil.d(TAG, "isSupportFluidCloud callback result = $it", true)
                callback.invoke(isSupportFluidCloud, it)
                isSupportFluidCloud = it
            }
        }
    }

    @JvmStatic
    fun isSupportFluidCloud(context: Context): Boolean {
        if (!FeatureOption.isEnableFluidEntry()) {
            return false
        }
        // 跨进程调用，耗时操作
        return isSupportFluidCloud ?: (SeedlingTool.isSupportFluidCloud(context).also { isSupportFluidCloud = it })
    }

    @JvmStatic
    fun isSupportSystemSendIntent(context: Context): Boolean {
        if (BaseUtil.isEXP()) {
            return false
        }

        if (FeatureOption.hasDisableSystemSeeding()) {
            return false
        }

        // 跨进程调用，耗时操作
        return isSupportSystemSend ?: (SeedlingTool.isSupportSystemSendIntent(context).also { isSupportSystemSend = it })
    }

    /**
     * 发送statusBar的show意图
     * originData放到options中可以优化胶囊出数据慢的问题，但是仅支持14版本
     */
    @JvmStatic
    fun showSeedlingStatusBar(
        originData: String? = null,
        callback: ((Boolean) -> Unit)? = null
    ) {
        kotlin.runCatching {
            DebugUtil.d(TAG, "showSeedlingStatusBar start, originData= $originData", true)
            isShowingSaved = false
            osVersion.resetShowStatusBar()
            val newOriginData = originData?.let { JSONObject(it) }
            //移除老意图再发送
            hideSeedlingStatusBar()
            SeedlingTool.sendSeedling(
                BaseApplication.getAppContext(),
                SeedlingIntent(
                    action = SEEDLING_ACTION_STATUS_BAR,
                    flag = SeedlingIntentFlagEnum.START,
                    options = newOriginData
                ),
                //用来接收决策结果是否成功的回调，如果失败则意味该次决策没有出结果，入口不会收到对应的结果。
                object : IIntentResultCallBack {
                    override fun onIntentResult(action: String, flag: Int, isSuccess: Boolean) {
                        //flag为意图开始结束的标记位，isSuccess为该次意图是否决策成功的标志
                        DebugUtil.d(
                            TAG,
                            "showSeedlingStatusBar callback, action= $action, flag = $flag, result = $isSuccess",
                            true
                        )
                        callback?.invoke(isSuccess)
                    }
                })
        }.onFailure {
            DebugUtil.d(
                TAG,
                "showSeedlingStatusBar: message = ${it.message}"
            )
        }
    }

    @JvmStatic
    fun hideSeedlingStatusBar(callback: ((Boolean) -> Unit)? = null) {
        kotlin.runCatching {
            DebugUtil.d(TAG, "hideSeedlingStatusBar start", true)
            osVersion.resetShowStatusBar()
            SeedlingTool.sendSeedling(
                BaseApplication.getAppContext(),
                SeedlingIntent(action = SEEDLING_ACTION_STATUS_BAR, flag = SeedlingIntentFlagEnum.END),
                //用来接收决策结果是否成功的回调，如果失败则意味该次决策没有出结果，入口不会收到对应的结果。
                object : IIntentResultCallBack {
                    override fun onIntentResult(action: String, flag: Int, isSuccess: Boolean) {
                        //flag为意图开始结束的标记位，isSuccess为该次意图是否决策成功的标志
                        DebugUtil.d(
                            TAG,
                            "hideSeedlingStatusBar callback, action= $action, flag = $flag, result = $isSuccess",
                            true
                        )
                        callback?.invoke(isSuccess)
                    }
                })
        }.onFailure {
            DebugUtil.d(
                TAG,
                "hideSeedlingStatusBar: message = ${it.message}"
            )
        }
    }

    @JvmStatic
    fun updateCardData(card: SeedlingCard?, originData: String?, updateAll: Boolean?) {
        kotlin.runCatching {
            if (card == null) {
                return
            }
            val newOriginData = originData?.let { JSONObject(it) }
            val versionCode = card.upkVersionCode
            var pagesId = FLUID_CLOUD_RECORDE
            //设置流体云面板点击滑动（用户左/右/上/下滑动、点击空白）的目的是“收起面板为胶囊”
            val panelActionConfigMapLocal: Map<PanelActionEnum, CancelPanelActionConfigEnum> =
                mutableMapOf(
                    PanelActionEnum.PANEL_SLIDE to CancelPanelActionConfigEnum.Retract,
                    PanelActionEnum.OUTSIDE_CLICK to CancelPanelActionConfigEnum.Retract
                )
            val extensibleActionMapLocal = mutableMapOf(
                SERVICE_FROM to if (recorderViewModelApi?.isFromCubeButtonOrLockScreen() == true) SERVICE_FROM_CUBE_BUTTON else SERVICE_FROM_DEFAULT,
            )

            if (newOriginData?.optBoolean(KEY_SHOW_RECORD_VIEW, true) == false) {
                if (BaseUtil.isAndroidVOrLater) {
                    if ((newOriginData.optInt(KEY_SHOW_PROCESS_PERCENT) == SAVE_COMPLEDED_PROCESS)
                        && card.serviceId != SeedingCardProcess.RECORD_SMALL_CARD_SERVICE_ID
                    ) {
                        mainHandler.postDelayed({
                            newOriginData.put(KEY_SHOW_SAVED_LOTTIE, false)
                            updateSavedCardData(card, newOriginData, updateAll)
                        }, PROGRESS_LOADING_DURATION_TIME)
                        isShowingSaved = true
                    }
                } else {
                    //当是保存成功状态时，判断当前如果是显示状态栏的流体云卡片不隐藏胶囊，未显示则隐藏
                    showHostMapLocal[SeedlingHostEnum.StatusBar] = osVersion.getShowStatusBar()
                    if (!osVersion.needHideSeedlingCard()) {
                        //systemUI建议如果不展示大卡就不要切換page，直接销卡。
                        recorderViewModelApi?.forceHideRecordStatusBar(
                            FROM_UPDATE_SAVE_FILE_SMALL
                        )
                        return
                    }
                }
                if (versionCode >= FLUID_CLOUD_2_CODE) {
                    pagesId = FLUID_CLOUD_RECORDE_END
                }
            } else {
                //录制状态常显
                showHostMapLocal[SeedlingHostEnum.StatusBar] = true
                if (versionCode >= FLUID_CLOUD_2_CODE) {
                    pagesId = FLUID_CLOUD_RECORDE
                }
            }
            val cardOptions =
                SeedlingCardOptions().apply {
                    grade = SeedlingCardOptions.GRADE_4
                    isMilestone = true
                    notificationIdList = if (currentId != null) arrayListOf(currentId!!) else null
                    lockScreenShowHostMap = lockScreenShowHostMapLocal
                    showHostMap = showHostMapLocal
                    panelActionConfigMap = panelActionConfigMapLocal
                    remindType = SeedlingCardOptions.REMIND_TYPE_NORMAL //默认值，不显示面板。
                    pageId = pagesId
                    extensibleActionMap = extensibleActionMapLocal
                }
            DebugUtil.d(
                TAG,
                "refreshSeedlingData: jsonData = $newOriginData, cardOptions = $cardOptions",
                true
            )
            if (updateAll != false) {
                SeedlingTool.updateAllCardData(
                    card,
                    cardOptions = cardOptions,
                    businessData = newOriginData
                )
            } else {
                SeedlingTool.updateData(
                    card,
                    cardOptions = cardOptions,
                    businessData = newOriginData
                )
            }
        }.onFailure {
            DebugUtil.d(
                TAG,
                "updateAllCardData: message = ${it.message}"
            )
        }
    }

    @JvmStatic
    fun registerResultCallBack() {
        DebugUtil.d(TAG, "registerResultCallBack")
        kotlin.runCatching {
            SeedlingTool.registerResultCallBack(
                BaseApplication.getAppContext(),
                arrayOf(SEEDLING_ACTION_STATUS_BAR)
            )
        }.onFailure { throwable ->
            DebugUtil.d(
                TAG,
                "registerAndTryCatch message = ${throwable.message}"
            )
        }
    }

    @JvmStatic
    fun unRegisterResultCallBack() {
        DebugUtil.d(TAG, "unRegisterResultCallBack")
        kotlin.runCatching {
            SeedlingTool.unRegisterResultCallBack(BaseApplication.getAppContext())
        }.onFailure { throwable ->
            DebugUtil.d(
                TAG,
                "unRegisterResultCallBack message = ${throwable.message}"
            )
        }
    }

    @JvmStatic
    fun updateSavedCardData(card: SeedlingCard, newOriginData: JSONObject, updateAll: Boolean?) {
        kotlin.runCatching {
            val pagesId = FLUID_CLOUD_RECORDE_END
            var tRemindType: Int = SeedlingCardOptions.REMIND_TYPE_STRONG_ALWAYS  //13,显示面板。
            //设置流体云面板点击滑动（用户左/右/上/下滑动、点击空白）的目的是“收起面板为胶囊”
            val panelActionConfigMapLocal: Map<PanelActionEnum, CancelPanelActionConfigEnum> =
                mutableMapOf(
                    PanelActionEnum.PANEL_SLIDE to CancelPanelActionConfigEnum.Retract,
                    PanelActionEnum.OUTSIDE_CLICK to CancelPanelActionConfigEnum.Retract
                )

            val extensibleActionMapLocal: Map<String, Any> = mutableMapOf(
                SERVICE_FROM to if (recorderViewModelApi?.isFromCubeButtonOrLockScreen() == true) SERVICE_FROM_CUBE_BUTTON else SERVICE_FROM_DEFAULT
            )

            if (newOriginData.optInt(KEY_SAVE_RECORD_FROM_WHERE) == MSG_ARG2_SAVE_RECORD_FROM_ACTIVITY) {
                //在录制页点保存，不展示卡片（此时是强提醒卡）。
                tRemindType = SeedlingCardOptions.REMIND_TYPE_NORMAL
            }

            val cardOptions =
                SeedlingCardOptions().apply {
                    grade = SeedlingCardOptions.GRADE_4
                    isMilestone = true
                    notificationIdList = if (currentId != null) arrayListOf(currentId!!) else null
                    lockScreenShowHostMap = lockScreenShowHostMapLocal
                    showHostMap = showHostMapLocal
                    panelActionConfigMap = panelActionConfigMapLocal
                    remindType = tRemindType
                    pageId = pagesId
                    extensibleActionMap = extensibleActionMapLocal
                }
            DebugUtil.d(
                TAG,
                "updateSavedCardData refreshSeedlingData: jsonData = $newOriginData, cardOptions = $cardOptions",
                true
            )
            if (updateAll != false) {
                SeedlingTool.updateAllCardData(
                    card,
                    cardOptions = cardOptions,
                    businessData = newOriginData
                )
            } else {
                SeedlingTool.updateData(
                    card,
                    cardOptions = cardOptions,
                    businessData = newOriginData
                )
            }
        }.onFailure {
            DebugUtil.d(
                TAG,
                "updateSavedCardData: message = ${it.message}"
            )
        }
    }
}