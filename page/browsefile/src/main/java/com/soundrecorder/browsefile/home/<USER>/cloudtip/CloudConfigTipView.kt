/*************************************************************************************************
 * Copyright (C), 2021-2031, OPLUS Mobile Comm Corp., Ltd
 * All rights reserved.
 *
 * File: - CloudConfigTipView.kt
 * Description: 云端配置下发的数据结构
 *
 * Version: 1.0
 * Date: 2024/9/21
 * Author: 80266877
 * TAG:
 * ------------------------------- Revision History: ---------------------------------------------
 * <author>                   <date>        <version>        <desc>
 * -----------------------------------------------------------------------------------------------
 * 80266877  2024/9/21    1.0              build this module
 ************************************************************************************************/
package com.soundrecorder.browsefile.home.view.cloudtip

import android.content.Context
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.cloudtips.CloudOperationResponseData
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.cloudkit.dialog.ICloudUpgradeHelper
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudGuideState
import com.soundrecorder.modulerouter.utils.Injector

/**
 * 显示云同步运营配置内容
 */
class CloudConfigTipView(
    context: Context,
    private val fragment: Fragment,
    private val mData: CloudOperationResponseData.ConfigData
) : FrameLayout(context) {

    private var tipView: COUIDefaultTopTips? = null
    private var mCloudUpgradeHelper: ICloudUpgradeHelper? = null
    private var mAdapter: BrowseAdapter? = null

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    init {
        tipView = COUIDefaultTopTips(context).apply {
            iniView(this)
        }
        addView(tipView)
        layoutParams = LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        updateLayoutParams<LayoutParams> {
            setMargins(
                0,
                context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp8),
                0,
                context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp4)
            )
        }
    }

    fun bindAdapter(adapter: BrowseAdapter?) {
        this.mAdapter = adapter
    }

    private fun iniView(tipView: COUIDefaultTopTips) {
        tipView.setStartIcon(
            ContextCompat.getDrawable(tipView.context, R.drawable.ic_cloudkit_guide_tips),
        )
        tipView.setTipsText(mData.content)
        mData.buttons?.forEach { buttonInfo ->
            if (buttonInfo.index == 0) {
                tipView.setNegativeButton(buttonInfo.content)
                tipView.setNegativeButtonListener {
                    onIgnoreButtonClick()
                }
            }
            if (buttonInfo.index == 1) {
                tipView.setPositiveButton(buttonInfo.content)
                tipView.setPositiveButtonListener {
                    if (mData.remindCategory == CloudOperationResponseData.ConfigData.RemindCategory.PAY) {
                        //拉起半屏支付
                        upgradeCloudSpace()
                    } else {
                        //否则就是打开云同步设置页
                        cloudTipManagerAction?.launchCloudSettingPage(context)
                    }
                    mAdapter?.hideCloudConfigHeader()
                }
            }
            //埋点上报点击事件
            CloudStaticsUtil.addCloudConfigClickEvent(mData.remindConfigId, buttonInfo.content, buttonInfo.index.toString(), mData.content)
        }

        //上传曝光埋点
        CloudStaticsUtil.cloudConfigExposureEvent(mData.remindConfigId, mData.content)
    }

    /**
     * 升级云空间引导
     */
    private fun upgradeCloudSpace() {
        if (mCloudUpgradeHelper == null) {
            mCloudUpgradeHelper = cloudKitApi?.newCloudUpgradeHelper()
        }
        mCloudUpgradeHelper?.upgradeCloudSpace(fragment)
    }

    private fun onIgnoreButtonClick() {
        //存储本次时间，忽略后1天以内不能再展示
        PrefUtil.putLong(
            context,
            PrefUtil.KEY_CLOUD_CONFIG_IGNORE_TIME,
            System.currentTimeMillis()
        )
        mAdapter?.hideCloudConfigHeader()
    }

    private fun updateSPValueWhenClickIgnore() {
        when (PrefUtil.getInt(
            BaseApplication.getAppContext(),
            PrefUtil.KEY_GUIDE_STATE,
            CloudGuideState.GUIDE_STATE_DEFAULT
        )) {
            CloudGuideState.GUIDE_STATE_DEFAULT -> {
                PrefUtil.putInt(
                    context,
                    PrefUtil.KEY_GUIDE_STATE,
                    CloudGuideState.GUIDE_STATE_IGNORE
                )
                PrefUtil.putLong(
                    context,
                    PrefUtil.KEY_GUIDE_IGNORE_TIME,
                    System.currentTimeMillis()
                )
            }

            CloudGuideState.GUIDE_STATE_IGNORE -> {
                PrefUtil.putInt(
                    context,
                    PrefUtil.KEY_GUIDE_STATE,
                    CloudGuideState.GUIDE_STATE_IGNORE_AGAIN
                )
            }
        }
    }
}
