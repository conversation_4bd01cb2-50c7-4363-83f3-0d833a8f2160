/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BrowseWavePlayController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.item.item

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.net.Uri
import androidx.lifecycle.map
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.base.PlayerHelperCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.task.AppTaskUtil
import com.soundrecorder.modulerouter.notification.NotificationInterface
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.player.WavePlayerController
import com.soundrecorder.player.status.PlayStatus

class BrowseWavePlayController(
    private var playerHelperCallback: PlayerHelperCallback?,
) : WavePlayerController(playerHelperCallback) {

    var mTaskId: Int? = null

    override val TAG = "BrowseWavePlayController"

    private var mNotificationModel: NotificationModel? = null
        get() {
            if (field == null) {
                field = NotificationModel().also {
                    it.playName = playerHelperCallback?.getPlayerName()
                    it.playStatus = playerState.map { playStatus ->
                        when (playStatus) {
                            PlayStatus.PLAYER_STATE_PLAYING,
                            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> NotificationModel.RECORD_STATUS_PLAYING

                            else -> NotificationModel.RECORD_STATUS_PAUSE
                        }
                    }
                    it.curTime = currentTimeMillis
                    it.playDuration = getDuration()
                    it.canJumpIntent = AppTaskUtil.mMapIsFromOtherApp[mTaskId] != true
                }
            }
            return field
        }
    private var mNotificationReceiver: BroadcastReceiver? = null

    @Volatile
    var hasInit: Boolean = false
        private set

    @Volatile
    var fastPlayMediaUri: Uri? = null

    private val notificationApi by lazy {
        Injector.injectFactory<NotificationInterface>()
    }

    override fun doInit() {
        super.doInit()
        hasInit = true
    }

    override fun doPlayBtnClick() {
        if (mIsTouchSeekbar.value == true) {
            DebugUtil.d(TAG, "doPlayBtnClick return by mIsTouchSeekbar is true")
            return
        }
        DebugUtil.i(TAG, "doPlayBtnClick, hasInit=$hasInit,playerState =${playerState.value}")
        if (!hasInit) {
            // 避免上一各音頻播放完成，立即點擊下一個播放，會先執行onRelease再執行doPlay，導致沒有播放器沒有init
            doInit()
        }
        checkNeedSetPlayUri()
        super.doPlayBtnClick()
    }

    private fun checkNeedSetPlayUri() {
        if (fastPlayMediaUri != null && fastPlayMediaUri != getPlayUri()) {
            setPlayUri(fastPlayMediaUri)
            playerHelperCallback?.let {
                setDuration(it.getDuration())
            }
            DebugUtil.i(TAG, "checkSetPlayUri, setPlayUri,$fastPlayMediaUri")
        }
    }

    override fun onPlayStateChange(playState: Int) {

        when (playState) {
            PlayStatus.PLAYER_STATE_PLAYING -> showNotification()
            PlayStatus.PLAYER_STATE_HALTON -> {
                removeNotification()
                if (hasInit && fastPlayMediaUri == getPlayUri()) {
                    // 正常播放完成，主动释放
                    DebugUtil.d(TAG, "onPlayStateChange halton release")
                    onRelease(false)
                }
            }
        }
    }

    override fun onTimerTick(timeTickMillis: Long) {
        setOCurrentTimeMillis(timeTickMillis)
    }

    override fun doContinuePlay() {
        super.doContinuePlay()
        BuryingPoint.addRecordPlayState(RecorderUserAction.VALUE_RECORD_PLAY_BROWSEFILE)
    }

    override fun doPausePlay() {
        super.doPausePlay()
        BuryingPoint.addRecordPlayState(RecorderUserAction.VALUE_RECORD_PLAY_PAUSE_BROWSEFILE)
    }

    override fun onPlayError(extra: Int) {
        val isAACError = dealAACErrorSeekToDuration(extra)
        if (!isAACError) {
            releasePlay()
            runToast(com.soundrecorder.common.R.string.player_error)
        }
        super.onPlayError(extra)
    }

    private fun runToast(resId: Int) {
        val context = BaseApplication.getAppContext()
        val toastMsg = context.getString(resId)
        ToastManager.showShortToast(context, toastMsg)
    }

    override fun getTimerPeriod(): Long {
        return 100
    }

    override fun releasePlay() {
        super.releasePlay()
        // 及时改变状态，让UI更新，否则UI会有延迟
        setPlayerStatus(PlayStatus.PLAYER_STATE_HALTON)
        currentTimeMillis.postValueSafe(0)
        mIsTouchSeekbar.postValueSafe(false)
        isReplay.postValueSafe(false)
        playStateBeforeTouchSeekBar = PlayStatus.PLAYER_STATE_HALTON
    }

    override fun doOnRelease(releaseThread: Boolean) {
        super.doOnRelease(releaseThread)
        unRegisterNotificationReceiver()
        mNotificationModel = null
        hasInit = false
        if (!releaseThread) {
            if (currentTimeMillis.value != 0L) {
                currentTimeMillis.postValue(0L)
            }
            if (playerState.value == PlayStatus.PLAYER_STATE_HALTON) {
                // 音频A播放完成，置为init，避免再次播放A，又走一遍release，导致ui异常
                playerState.postValueSafe(PlayStatus.PLAYER_STATE_INIT)
            }
        }
    }

    private fun showNotification() {
        if (notificationApi == null) {
            return
        }
        mNotificationModel?.playDuration = getDuration()
        notificationApi?.showNotification(
            getNotificationMode(),
            NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY,
            mNotificationModel
        )
        registerNotificationReceiver()
    }

    private fun removeNotification() {
        if (notificationApi == null) {
            return
        }

        notificationApi?.cancelNotification(
            getNotificationMode(),
            NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
        )
    }

    private fun getNotificationMode(): Int {
        return notificationApi?.getNotificationMode(AppTaskUtil.mMapIsFromOtherApp[mTaskId] == true) ?: NotificationUtils.NOTIFICATION_MODE_COMMON
    }

    private fun registerNotificationReceiver() {
        if (AppTaskUtil.mMapIsFromOtherApp[mTaskId] == true) {
            DebugUtil.d(TAG, "registerNotificationReceiver: isFromOtherApp")
            return
        }

        if (mNotificationReceiver == null) {
            mNotificationReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    val notificationId =
                        intent?.getIntExtra(NotificationUtils.KEY_NOTIFICATION_TYPE, -1) ?: -1
                    val currentId = notificationApi?.getNotificationIdByModeAndPage(
                        getNotificationMode(), NotificationUtils.NOTIFICATION_PAGE_FAST_PLAY
                    )
                    if (notificationId != currentId) {
                        return
                    }

                    when (intent?.action) {
                        NotificationUtils.PLAY_STATUS_CHANGED_ACTION -> {
                            val isPlaying = isWholePlaying()
                            playBtnClick()
                            BuryingPoint.addPlayBtnClickFromNotification(
                                notificationApi?.isLockScreen() ?: false,
                                isPlaying,
                                NotificationUtils.NOTIFICATION_PAGE_PLAY_BACK
                            )
                        }
                    }
                }
            }
            BaseApplication.getAppContext().registerReceiverCompat(
                mNotificationReceiver,
                notificationApi?.getIntentFilter(), RECEIVER_NOT_EXPORTED
            )
        }
    }

    private fun unRegisterNotificationReceiver() {
        if (mNotificationReceiver != null) {
            BaseApplication.getAppContext().unregisterReceiver(mNotificationReceiver)
            mNotificationReceiver = null
        }
    }
}