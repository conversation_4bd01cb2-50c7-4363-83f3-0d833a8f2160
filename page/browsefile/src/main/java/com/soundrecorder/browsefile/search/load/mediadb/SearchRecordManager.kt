/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.mediadb

import android.database.Cursor
import android.provider.MediaStore
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.load.LoadRecordDataHelper
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.browsefile.search.load.SearchRepository
import com.soundrecorder.browsefile.search.load.SearchResultWrapper
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector

/**
 * search result from record media db
 */
object SearchRecordManager {

    const val TAG = "SearchRecordManager"

    private val searchHandlerDataFactory by lazy {
        SearchHandlerDataFactory()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    fun startQueryWithSearch(searchValues: MutableMap<String, String>): SearchResultWrapper {
        /*search by mediaLib need filter space*/
        val keyword = searchValues[SearchRepository.KEY_SEARCH_WORD]?.replace(" ", "")
        //val supportFilter = searchValues[SearchRepository.KEY_SUPPORT_FILTER]
        var dataList: MutableList<ItemSearchViewModel>? = null
        if (!keyword.isNullOrEmpty()) {
            //val filter = supportFilter?.toIntOrNull() ?: RecordModeConstant.BUCKET_VALUE_ALL
            val filter = GroupInfo.INT_DEFAULT_ALL
            val originalCursor = queryOriginRecord(filter)
            dataList = getDataListFromCursor(keyword, originalCursor, filter)
            SearchSortUtils.doSortSearchBeans(dataList)
        }

        return if (dataList != null) {
            SearchResultWrapper(dataList, null, dataList.size)
        } else {
            SearchResultWrapper()
        }
    }

    private fun queryOriginRecord(filter: Int): Cursor? {
        var whereClause =
            CursorHelper.getAllRecordForFilterAndQueryWhereClause(
                BaseApplication.getAppContext(), filter)
        var args = CursorHelper.getsAcceptableAudioTypes()
        // 当前正在录制的音频，过滤该条数据
        val fileBeingRecorded = recorderViewModelApi?.getFileBeingRecorded()
        if (!TextUtils.isEmpty(fileBeingRecorded)) {
            whereClause += " and " + MediaStore.Audio.Media.DATA + " != ?"
            val argsList: MutableList<String> = args.toMutableList()
            argsList.add(fileBeingRecorded!!)
            args = argsList.toTypedArray()
        }
        return try {
            BaseApplication.getAppContext().contentResolver.query(
                MediaDBUtils.BASE_URI, CursorHelper.getProjection(),
                whereClause, args,
                MediaStore.Audio.Media.DATE_MODIFIED + " DESC"
            )
        } catch (e: Exception) {
            DebugUtil.d(TAG, "Exception thrown during queryOriginRecord: ${e.message}")
            null
        }
    }

    private fun getDataListFromCursor(
        searchValue: String,
        originalCursor: Cursor?,
        supportFilter: Int
    ): ArrayList<ItemSearchViewModel>? {
        return try {
            val listCount = originalCursor?.use {
                LoadRecordDataHelper.addCompleteFlag(it, supportFilter)
            }

            val absSubMessageBean = AbsSubMessageBean().also {
                it.searchValue = searchValue
                it.cursor = listCount?.cursor
            }

            searchHandlerDataFactory.convertData(absSubMessageBean)
        } catch (e: Exception) {
            DebugUtil.d(TAG, "getDataListFromCursor error is ${e.message}")
            null
        }
    }
}