/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertInfo
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.load

import android.content.Intent
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_CONVERT_STATUS
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_STATUS
import java.util.concurrent.ConcurrentHashMap

object ConvertingInfo {
    private const val TAG = "ConvertingInfo"

    private val cacheConvertingIds = mutableListOf<Long>()
    private var cacheSmartNamingIds = mutableListOf<Long>()
    private var cacheSmartNameResult = ConcurrentHashMap<Long, String>()

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun onConvertStatusChanged(intent: Intent?) {
        var recordId = -1L
        var display = false
        try {
            recordId = intent?.getLongExtra(KEY_NOTIFY_RECORD_ID, -1L) ?: -1
            display = intent?.getBooleanExtra(KEY_NOTIFY_CONVERT_STATUS, false) ?: false
        } catch (e: Exception) {
            DebugUtil.e(TAG, "notifyConvertStatusUpdate get id , display.", e)
        }
        DebugUtil.d(TAG, "notifyConvertStatusUpdate recordId:$recordId, display:$display")
        if (display) {
            if (!cacheConvertingIds.contains(recordId)) {
                cacheConvertingIds.add(recordId)
            }
        } else {
            cacheConvertingIds.remove(recordId)
        }
    }

    fun isConverting(mediaId: Long): Boolean {
        return cacheConvertingIds.contains(mediaId)
    }

    fun removeConvertMediaId(mediaId: Long) {
        if (cacheConvertingIds.contains(mediaId)) {
            DebugUtil.d(TAG, "removeConvertMediaId, mediaId:$mediaId")
            cacheConvertingIds.remove(mediaId)
        }
    }

    @JvmStatic
    fun clearConvertingInfo() {
        cacheConvertingIds.clear()
        cacheSmartNamingIds.clear()
        cacheSmartNameResult.clear()
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun onSmartNameStatusChanged(intent: Intent?) {
        var recordId = -1L
        var display = false
        try {
            recordId = intent?.getLongExtra(KEY_NOTIFY_RECORD_ID, -1L) ?: -1
            display = intent?.getBooleanExtra(KEY_NOTIFY_SMART_NAME_STATUS, false) ?: false
        } catch (e: Exception) {
            DebugUtil.e(TAG, "onSmartNameStatusChanged get id , display.", e)
        }
        DebugUtil.d(TAG, "onSmartNameStatusChanged recordId:$recordId, display:$display")
        removeConvertMediaId(recordId)
        if (display) {
            if (!cacheSmartNamingIds.contains(recordId)) {
                cacheSmartNamingIds.add(recordId)
            }
        } else {
            cacheSmartNamingIds.remove(recordId)
        }
    }

    fun isSmartNaming(mediaId: Long): Boolean {
        return cacheSmartNamingIds.contains(mediaId)
    }

    fun removeSmartNameMediaId(recordId: Long) {
        if (cacheSmartNamingIds.contains(recordId)) {
            DebugUtil.d(TAG, "removeSmartNameMediaId:$recordId")
            cacheSmartNamingIds.remove(recordId)
        }
    }

    fun addSmartNameResult(mediaId: Long, resultName: String) {
        if (!cacheSmartNameResult.containsKey(mediaId)) {
            DebugUtil.d(TAG, "addSmartNameResult, mediaId:$mediaId, resultName:$resultName")
            cacheSmartNameResult[mediaId] = resultName
            //cacheSmartNameResult.remove(mediaId)
        }
    }

    fun removeSmartNameResult(mediaId: Long) {
        if (cacheSmartNameResult.containsKey(mediaId)) {
            DebugUtil.d(TAG, "removeSmartNameResult:$mediaId")
            cacheSmartNameResult.remove(mediaId)
        }
    }

    fun isContainsSmartNameResult(mediaId: Long): Boolean {
        return cacheSmartNameResult.containsKey(mediaId)
    }

    fun getSmartNameResult(mediaId: Long): String? {
        if (isContainsSmartNameResult(mediaId)) {
            return cacheSmartNameResult[mediaId]
        }
        return null
    }
}