/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseModel.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.load

import android.annotation.SuppressLint
import android.database.Cursor
import android.provider.MediaStore
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.constant.RecordConstant.RECYCLE_STORAGE_TIME
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.RecordModeUtil.cleanRelativePath
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.launch
import java.io.File
import java.util.concurrent.CopyOnWriteArrayList

class BrowseModel : AbsModel<ItemBrowseRecordViewModel, GroupInfo>() {

    private val TAG = "BrowseModel"

    var browseListCount: BrowseListCount? = null
    private var callerRecordList: CopyOnWriteArrayList<Record>? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    private val convertSupportAction by lazy {
        Injector.injectFactory<ConvertSupportAction>()
    }

    companion object {
        private const val HANDLE_RECYCLE_BIN_DIRTY_DATA_KEY = "handle_recycle_bin_dirty_data"
        private const val HANDLE_RECYCLE_BIN_DIRTY_DATA_MAX_COUNT = 10
    }

    override fun query(group: GroupInfo, isGroupingByContact: Boolean, name: String?, job: Job?) {
        DebugUtil.d(
            TAG,
            "query group = ${group.mGroupName}, isGroupingByContact:$isGroupingByContact"
        )
        var whereClause =
            CursorHelper.getAllRecordForFilterAndQueryWhereClause(BaseApplication.getAppContext())
        var selectionArg = CursorHelper.getsAcceptableAudioTypes()
        val orderClause =
            "${MediaStore.Audio.Media.DATE_MODIFIED} DESC,${MediaStore.Audio.Media.TITLE} DESC"
        // 当前正在录制的音频，过滤该条数据
        val fileBeingRecorded = recorderViewModelApi?.getFileBeingRecorded()
        if (!fileBeingRecorded.isNullOrBlank()) {
            whereClause += " and " + MediaStore.Audio.Media.DATA + " != ?"
            val argsList: MutableList<String> = selectionArg.toMutableList()
            argsList.add(fileBeingRecorded)
            selectionArg = argsList.toTypedArray<String>()
        }
        var listCount: BrowseListCount? = null
        if (group.isRecentlyDeleteGroup()) {
            //回收站列表，查询record表
            recycleRecordData(group, job)
        } else if (group.isCustomGroup()) {
            getRecordsByGroup(group, job)
        } else {
            kotlin.runCatching {
                BaseApplication.getAppContext().contentResolver.query(
                    MediaDBUtils.BASE_URI, CursorHelper.getProjection(),
                    whereClause, selectionArg, orderClause
                )?.use {
                    DebugUtil.d(TAG, "query media total count: ${it.count}")
                    listCount = LoadRecordDataHelper.addCompleteFlag(it, group.mGroupType, job)
                }
            }.onFailure {
                DebugUtil.e(TAG, "query error: $it")
            }
            onRecordersFetched(listCount, group, isGroupingByContact, name, job)
        }
    }

    private fun getRecordsByGroup(group: GroupInfo, job: Job?) {
        val orderClause = "$COLUMN_NAME_DATE_MODIFIED DESC, $COLUMN_NAME_DISPLAY_NAME DESC"
        //自定义分组查询，过滤掉已经被删除的记录
        val section = "${DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID} = ? " +
                "AND ${DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE} != ?"
        val args = arrayOf(group.mId.toString(), RecordConstant.RECORD_DELETED.toString())
        val projection = DatabaseConstant.getDistinctRecordProjection(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA)
        val resolver = BaseApplication.getAppContext().contentResolver
        val cursor = resolver.query(
            DatabaseConstant.RecordUri.RECORD_CONTENT_URI,
            projection,
            section,
            args,
            orderClause
        )
        if (browseListCount == null) {
            browseListCount = BrowseListCount()
        }
        val summaryMap = getSummaryResultMap()
        val covertTextMap = LoadRecordDataHelper.getConvertDbMap()
        val data = ArrayList<ItemBrowseRecordViewModel>()
        var hasSummaryItem = false
        cursor?.use {
            while (it.moveToNext()) {
                //job可能会被取消，触发抛出CancellationException
                job?.ensureActive()
                kotlin.runCatching {
                    //判断文件是否存在，不存在则不显示
                    val dataIndex =
                        cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA)
                    if (dataIndex >= 0) {
                        val filePath = cursor.getString(dataIndex)
                        if (!File(filePath).exists()) {
                            return@runCatching
                        }
                    }

                    val item = ItemBrowseRecordViewModel()
                    queryCursorRecordInfo(cursor, item)
                    item.groupId = group.mId
                    item.groupUuid = group.mUuId

                    var isConvertCompleted = false
                    try {
                        val completeStatus = covertTextMap[item.mediaId]
                        isConvertCompleted = (completeStatus == ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE)
                    } catch (e: IllegalArgumentException) {
                        DebugUtil.e(TAG, "get convert completed status error.", e)
                    }
                    item.convertCompleted = isConvertCompleted
                    if (isConvertCompleted) {
                        item.converting = false
                    } else {
                        item.converting = ConvertingInfo.isConverting(item.mediaId)
                    }
                    item.smartNaming = ConvertingInfo.isSmartNaming(item.mediaId)
                    summaryMap[item.mediaId.toString()]?.let {
                        item.noteId = it.noteId
                        item.recordUUID = it.recordUUID
                        hasSummaryItem = true
                    }

                    data.add(item)
                }.onFailure {
                    DebugUtil.e(TAG, "query getRecordsByGroup error, ${it.message}")
                }
            }
        }
        DebugUtil.d(TAG, "${group.mGroupName}: ShowCount group count = ${group.mGroupCount}"
                    + ", cursor size = ${cursor?.count}, data size= ${data.size} ")
        browseListCount?.cursor = null
        notifyDataReadyCompleted(data, group, null, job)
        // 列表中有查看摘要数据，统计一次埋点
        if (hasSummaryItem) {
            SummaryStaticUtil.addShowViewSummaryEvent(SummaryStaticUtil.EVENT_FROM_MAIN)
        }
    }

    private fun queryCursorRecordInfo(
        cursor: Cursor,
        item: ItemBrowseRecordViewModel
    ) {
        val displayNameIndex = cursor.getColumnIndex(COLUMN_NAME_DISPLAY_NAME)
        if (displayNameIndex >= 0) {
            item.displayName = cursor.getString(displayNameIndex)
        }
        item.title = item.displayName.title()
        val dataIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA)
        if (dataIndex >= 0) {
            item.data = cursor.getString(dataIndex)
            /*原有逻辑：非回收站的文件，要通过媒体库id判断文件是否存在。
             所以在查询自定义分组的文件时，再次查询媒体库id
             */
            item.mediaId = MediaDBUtils.queryIdByData(item.data)
        }
        val relativePathIndex =
            cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH)
        if (relativePathIndex >= 0) {
            item.relativePath =
                cursor.getString(relativePathIndex).cleanRelativePath()
        }
        val dateModifiedIndex = cursor.getColumnIndex(COLUMN_NAME_DATE_MODIFIED)
        if (dateModifiedIndex >= 0) {
            item.dateModified = cursor.getLong(dateModifiedIndex)
        }

        val durationIndex =
            cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION)
        if (durationIndex >= 0) {
            item.mDuration = cursor.getLong(durationIndex)
        }
        val sizeIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE)
        if (sizeIndex >= 0) {
            item.size = cursor.getLong(sizeIndex)
        }
        val mimeTypeIndex =
            cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE)
        if (mimeTypeIndex >= 0) {
            item.mimeType = cursor.getString(mimeTypeIndex)
        }
    }

    /**
     * 查询回收站的数据
     */
    @SuppressLint("Range")
    private fun recycleRecordData(group: GroupInfo, job: Job?) {
        val orderClause = "$COLUMN_NAME_DATE_MODIFIED DESC, $COLUMN_NAME_DISPLAY_NAME DESC"
        val section = "${DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_RECYCLE} = ?"
        val args = arrayOf("1")
        val resolver = BaseApplication.getAppContext().contentResolver
        val projection = DatabaseConstant.getDistinctRecordProjection(DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH)
        val cursor = resolver.query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, projection, section, args, orderClause)
        if (browseListCount == null) {
            browseListCount = BrowseListCount()
        }
        browseListCount?.recycleCount = 0

        val data = ArrayList<ItemBrowseRecordViewModel>()
        val overTimeData = ArrayList<Record>() //查询到超过30天的数据，进行批量删除
        val currentTime = System.currentTimeMillis()
        val dirtyData = ArrayList<Record>()
        val startTime0 = System.currentTimeMillis()
        cursor?.use {
            while (cursor.moveToNext()) {
                //job可能会被取消，触发抛出CancellationException
                job?.ensureActive()
                kotlin.runCatching {
                    val deleteTime =
                        cursor.getLong(cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME))
                    val gapTime = currentTime - deleteTime
                    if (gapTime >= RECYCLE_STORAGE_TIME) {
                        overTimeData.add(Record(cursor, Record.TYPE_FROM_RECORD))
                        return@runCatching
                    }
                    //判断文件是否存在，不存在则不显示
                    val dataIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH)
                    if (dataIndex >= 0) {
                        val filePath = cursor.getString(dataIndex)
                        if (!File(filePath).exists()) {
                            dirtyData.add(Record(cursor, Record.TYPE_FROM_RECORD))
                            return@runCatching
                        }
                    }

                    val item = ItemBrowseRecordViewModel()
                    queryCursorRecycleRecordInfo(item, cursor, group)
                    if (item.isRecycle) {
                        browseListCount?.addRecycleCount()
                    }
                    data.add(item)
                }.onFailure {
                    DebugUtil.e(TAG, "query recycleRecordData error, ${it.message}")
                }
            }
        }
        DebugUtil.d(TAG, "${group.mGroupName}: ShowCount group count = ${group.mGroupCount}"
                    + ", cursor count= ${cursor?.count}, data size= ${data.size} "
        )
        DebugUtil.d(TAG, "recycleRecordData count = ${cursor?.count}, data = $data")
        notifyDataReadyCompleted(data, group, null, job)
        updateOverTimeAndDirtyInRecycleBin(overTimeData, dirtyData)
        DebugUtil.d(TAG, "recycleRecordData: Total: cost = ${System.currentTimeMillis() - startTime0}")
    }

    private fun queryCursorRecycleRecordInfo(
        item: ItemBrowseRecordViewModel,
        cursor: Cursor,
        group: GroupInfo
    ) {
        val displayNameIndex = cursor.getColumnIndex(COLUMN_NAME_DISPLAY_NAME)
        if (displayNameIndex > 0) {
            item.displayName = cursor.getString(displayNameIndex)
        }
        item.title = item.displayName.title()
        val dataIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA)
        if (dataIndex >= 0) {
            item.data = cursor.getString(dataIndex)
        }
        val relativePathIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH)
        if (relativePathIndex >= 0) {
            item.relativePath = cursor.getString(relativePathIndex).cleanRelativePath()
        }
        val dateModifiedIndex = cursor.getColumnIndex(COLUMN_NAME_DATE_MODIFIED)
        if (dateModifiedIndex >= 0) {
            item.dateModified = cursor.getLong(dateModifiedIndex)
        }
        val mediaIdIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_ID)
        if (mediaIdIndex >= 0) {
            item.mediaId = cursor.getLong(mediaIdIndex)
        }
        val durationIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION)
        if (durationIndex >= 0) {
            item.mDuration = cursor.getLong(durationIndex)
        }
        val sizeIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE)
        if (sizeIndex >= 0) {
            item.size = cursor.getLong(sizeIndex)
        }
        val mimeTypeIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE)
        if (mimeTypeIndex >= 0) {
            item.mimeType = cursor.getString(mimeTypeIndex)
        }
        val uuidIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID)
        if (uuidIndex >= 0) {
            item.recordUUID = cursor.getString(uuidIndex)
        }
        item.isRecycle = true //前面查询条件已经确定了，此值必定为true
        val recyclePathIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_RECYCLE_PATH)
        if (recyclePathIndex >= 0) {
            item.recyclePath = cursor.getString(recyclePathIndex)
        }
        val deleteTimeIndex = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE_TIME)
        if (deleteTimeIndex >= 0) {
            item.deleteTime = cursor.getLong(deleteTimeIndex)
        }
        val groupIdIndex =
            cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID)
        if (groupIdIndex >= 0) {
            item.groupId = cursor.getInt(groupIdIndex)
        }
        val groupUuidIndex =
            cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID)
        if (groupUuidIndex >= 0) {
            item.groupUuid = cursor.getString(groupUuidIndex)
        }
    }

    private fun updateOverTimeAndDirtyInRecycleBin(
        overTimeData: ArrayList<Record>,
        dirtyData: ArrayList<Record>
    ) {
        if (overTimeData.isNotEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                FileDealUtil.deleteOverTimeRecycleRecord(
                    BaseApplication.getAppContext(),
                    overTimeData
                )
            }
        }
        if (dirtyData.isNotEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                FileDealUtil.updateDirtyDataInRecycleBin(dirtyData)
            }
        }
    }

    /**
     * 分组表记录的信息在App首次运行或者清除数据后启动，
     * 不能获取到准确的分组文件数量，该方法用于处理此问题
     */
    private fun checkGroupCountForDefaultGroupFromMedia(
        listCount: BrowseListCount?
    ) {
        GroupInfoManager.getInstance(BaseApplication.getAppContext())
            .setTotalMediaCount(listCount?.allCount ?: 0)

        DebugUtil.d(TAG, "checkGroupCountForDefaultGroupFromMedia")
        val groupCountMap = HashMap<Int, Int>()
        groupCountMap[GroupInfo.INT_DEFAULT_ALL] = listCount?.allCount ?: 0
        groupCountMap[GroupInfo.INT_DEFAULT_CALLING] = listCount?.callCount ?: 0
        groupCountMap[GroupInfo.INT_DEFAULT_COMMON] = listCount?.commonCount ?: 0
        GroupInfoManager.getInstance(BaseApplication.getAppContext())
            .refreshDefaultGroupInfoListFromMedia(groupCountMap)
    }

    private fun checkFileExistsInRecycleBin(filesInRecycleBin: List<File>?, fileName: String): Boolean {
        filesInRecycleBin?.forEach {
            if (it.name.equals(fileName)) {
                return true
            }
        }
        return false
    }

    /**
     * @param job 传入的job可能会被取消，触发抛出CancellationException，注意异常捕获问题
     */
    @Suppress("LongMethod")
    fun onRecordersFetched(listCount: BrowseListCount?, group: GroupInfo, isGroupingByContact: Boolean, name: String? = null, job: Job?) {
        DebugUtil.d(TAG, "onRecordersFetched, group=${group.mGroupName}")
        browseListCount = listCount
        browseListCount?.callerNameCount = 0

        checkGroupCountForDefaultGroupFromMedia(listCount)

        if (isGroupingByContact && listCount?.dbRecordsList?.isNotEmpty() == true) {
            if (callerRecordList == null) {
                callerRecordList = CopyOnWriteArrayList()
            }
            callerRecordList?.clear()
            listCount.dbRecordsList?.forEach {
                //job可能会被取消，触发抛出CancellationException
                job?.ensureActive()
                if (it.callAvatarColor != null || it.callerName != null) {
                    callerRecordList?.add(it)
                }
            }
            //recordCallerAvatarList = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).getCallerAndColorRecordList()
            DebugUtil.d(TAG, "onRecordersFetched, recordCallerAvatarList.size:${callerRecordList?.size}")
        }
        val summaryMap = getSummaryResultMap()
        val data = ArrayList<ItemBrowseRecordViewModel>()
        var hasSummaryItem = false
        DebugUtil.d(TAG, "Query data...")
        listCount?.cursor?.use { cursor ->
            if (cursor.moveToFirst()) {
                val columnIndexDisplayName =
                    cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DISPLAY_NAME)
                val columnIndexData = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
                val columnIndexRelativePath =
                    cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.RELATIVE_PATH)
                val columnIndexDateModified =
                    cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED)
                val columnIndexId = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
                val columnIndexDuration =
                    cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
                val columnIndexOwnerPackageName =
                    cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.OWNER_PACKAGE_NAME)
                val columnIndexSize = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)
                val columnIndexMimeType =
                    cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.MIME_TYPE)

                /*是否显示转文本标识，支持转文本就行，非主栈保持原有逻辑，有T标识，进入无文本tab*/
                val supportConvert = convertSupportAction?.isSupportConvert(true)
                var item: ItemBrowseRecordViewModel
                do {
                    //job可能会被取消，触发抛出CancellationException
                    job?.ensureActive()

                    item = ItemBrowseRecordViewModel()
                    item.displayName = cursor.getString(columnIndexDisplayName)
                    item.title = item.displayName.title()
                    item.data = cursor.getString(columnIndexData)
                    item.relativePath =
                        cursor.getString(columnIndexRelativePath).cleanRelativePath()
                    item.dateModified = cursor.getLong(columnIndexDateModified)
                    item.mediaId = cursor.getLong(columnIndexId)
                    item.mDuration = cursor.getLong(columnIndexDuration)
                    item.ownerPackageName = cursor.getString(columnIndexOwnerPackageName)
                    item.size = cursor.getLong(columnIndexSize)
                    item.mimeType = cursor.getString(columnIndexMimeType)
                    if (supportConvert == true) {
                        setConvertCompletedStatus(cursor, item)
                    }

                    //“全部录音”分组的信息不要设置给item,否则数据库会记录异常的分组id和uuid
                    if (!group.isAllGroup()) {
                        item.groupId = group.mId
                        item.groupUuid = group.mUuId
                    }

                    item.isGroupingParent = false
                    setCallerNameAndAvatar(group, isGroupingByContact, callerRecordList, item)
                    //单个联系人列表的查询满足该条件，过滤显示
                    if (group.isCallingGroup()) {
                        val record = listCount.dbRecordsList?.find { it.data == item.data }
                        record?.let {
                            item.callerName = it.callerName
                            if (record.originalName != null && item.originalName == null) {
                                item.originalName = record.originalName
                            }
                        }
                    }
                    //设置联系人名称和计数
                    setCallerName(item, group, name)
                    hasSummaryItem = hasSummary(summaryMap, item)
                    data.add(item)
                } while (cursor.moveToNext())
            }
        }
        DebugUtil.d(TAG, "${group.mGroupName}: ShowCount group count = ${group.mGroupCount}"
                + ", cursor size = ${listCount?.cursor?.count}, data size= ${data.size} ")
        listCount?.cursor?.close()
        listCount?.cursor = null
        listCount?.dbRecordsList = null
        DebugUtil.d(TAG, "notifyDataReadyCompleted, data=${data.size}, group=${group.mGroupName}, name=$name, job=$job")
        notifyDataReadyCompleted(data, group, name, job)
        // 列表中有查看摘要数据，统计一次埋点
        if (hasSummaryItem) {
            SummaryStaticUtil.addShowViewSummaryEvent(SummaryStaticUtil.EVENT_FROM_MAIN)
        }
    }

    private fun setCallerNameAndAvatar(
        group: GroupInfo,
        isGroupingByContact: Boolean,
        recordAvatarList: MutableList<Record>?,
        item: ItemBrowseRecordViewModel
    ) {
        if (group.isCallingGroup() && isGroupingByContact) {
            recordAvatarList?.forEach { dbRecord ->
                if (item.displayName == dbRecord.displayName) {
                    item.avatarColor = dbRecord.callAvatarColor
                    if (dbRecord.callerName != null) {
                        item.callerName = dbRecord.callerName
                    }
                    if (dbRecord.originalName != null) {
                        item.originalName = dbRecord.originalName
                    }
                }
            }
        }
    }

    /**
     * Split the contact name according to displayName and set the call recording contact name
     * @param group
     * @param item
     */
    private fun setCallerName(
        item: ItemBrowseRecordViewModel,
        group: GroupInfo,
        name: String?
    ) {
        if (item.callerName == null && group.isCallingGroup() && !item.displayName.isNullOrEmpty()) {
            // 可能耗时。
            item.callerName = RecorderDBUtil.getCallerName(item.getRecord(), item.mediaId)
        }
        if (item.originalName == null) {
            item.originalName = item.displayName
        }
        if (name != null && item.callerName == name) {
            browseListCount?.addCallerNameCount()
        }
    }

    private fun hasSummary(
        summaryMap: Map<String, NoteData>,
        item: ItemBrowseRecordViewModel
    ): Boolean {
        var hasSummaryItem1 = false
        summaryMap[item.mediaId.toString()]?.let {
            item.noteId = it.noteId
            item.recordUUID = it.recordUUID
            hasSummaryItem1 = true
        }
        return hasSummaryItem1
    }

    private fun setConvertCompletedStatus(cursor: Cursor, item: ItemBrowseRecordViewModel) {
        var isConvertCompleted = false
        //var avatarColor: String? = null
        try {
            val columnIndexCompleteStatus = cursor.getColumnIndexOrThrow(
                DatabaseConstant.ConvertColumn.COMPLETE_STATUS
            )
            val completeStatus = cursor.getInt(columnIndexCompleteStatus)
            isConvertCompleted = (completeStatus == ConvertDbUtil.CONVERT_COMP_STATUS_COMPLETE)
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "get convert completed status error.", e)
        }
        item.convertCompleted = isConvertCompleted
        if (isConvertCompleted) {
            item.converting = false
        } else {
            item.converting = ConvertingInfo.isConverting(item.mediaId)
        }
        item.smartNaming = ConvertingInfo.isSmartNaming(item.mediaId)
    }

    private fun getSummaryResultMap(): Map<String, NoteData> {
        val resultMap = hashMapOf<String, NoteData>()
        if (FeatureOption.supportRecordSummaryFunction()) {
            NoteDbUtils.queryAllNotes().forEach {
                resultMap[it.mediaId] = it
            }
        }
        return resultMap
    }
}