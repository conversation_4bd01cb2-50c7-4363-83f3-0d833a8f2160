/***********************************************************
 ** Copyright (C), 2010-2024, Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: - ForNoteUtil.kt
 ** Description: Annotation method that needs to be mapped.
 ** Version: 1.0
 ** Date : 2024/03/21
 ** Author: 80351967
 **
 ** ---------------------Revision History: ---------------------
 ** <author>    <data>    <version >    <desc>
 ** xxx@TAG    2024/03/21    1.0    build this file
 ****************************************************************/
package com.soundrecorder.browsefile.search.utils

import android.content.pm.PackageManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.browsefile.search.load.center.CenterDbConstant.CENTER_DMP_PKG_NAME
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector

object ForNoteUtil {
    const val TAG = "ForNoteUtil"
    private const val SUPPORT_SUMMARY_NEUTRON_VERSION: Long = 14001011

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val isSupportSummary: Boolean by lazy {
        // 初始化代码
        FeatureOption.supportRecordSummaryFunction() && summaryApi?.isNoteSupport(BaseApplication.getAppContext()) == true
    }

    @JvmStatic
    fun isSupportNeutronVersion(): Boolean {
        return try {
            val packageInfo = BaseApplication.getAppContext().packageManager.getPackageInfo(
                CENTER_DMP_PKG_NAME,
                0
            )
            val currentVersionCode = packageInfo.longVersionCode
            DebugUtil.d(
                TAG,
                "currentVersionCode:$currentVersionCode  isSupportNeutronVersion:${currentVersionCode >= SUPPORT_SUMMARY_NEUTRON_VERSION}"
            )
            currentVersionCode >= SUPPORT_SUMMARY_NEUTRON_VERSION
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    @JvmStatic
    fun isSupportSummarySearch(): Boolean {
        //中子通过版本号判断是否支持；便签通过metadata值判断是否支持
        DebugUtil.d(TAG, "isSupportSummarySearch isSupportSummary:$isSupportSummary")
        return isSupportSummary && (isSupportNeutronVersion().also {
            DebugUtil.d(TAG, "isSupportSummarySearch isSupportNeutronVersion:$it")
        })
    }

    @JvmStatic
    private fun assignmentNoteIDAndUUID(itemSearchViewModel: ItemSearchViewModel): Pair<String?, String?> {
        val noteData = NoteDbUtils.queryNoteByMediaId(itemSearchViewModel.mediaId.toString())
        itemSearchViewModel.noteId = noteData?.noteId
        itemSearchViewModel.recordUUID = noteData?.recordUUID
        return Pair(itemSearchViewModel.noteId, itemSearchViewModel.recordUUID)
    }

    /**
     * @param summaryList 便签匹配到关键词的结果数据,已经带了callId、noteId数据
     * @param neuTronList 录音搜素匹配到的结果数据(自有or中子)
     */
    @JvmStatic
    fun mergeSummaryAndNeutronList(
        summaryList: MutableList<ItemSearchViewModel>,
        neuTronList: MutableList<ItemSearchViewModel>
    ): MutableList<ItemSearchViewModel> {
        val resultList =
            if (summaryList.isEmpty()) {
                neuTronList.forEach { item -> item.isMatchTitle = true }
                neuTronList
            } else if (neuTronList.isEmpty() && (!FeatureOption.supportInnerRecordSummaryFunction())) {
                summaryList.forEach { item -> item.isMatchSummary = true }
                summaryList
            } else {
                summaryList.forEach { item -> item.isMatchSummary = true }
                neuTronList.forEach { item -> item.isMatchTitle = true }
                val resultMap = LinkedHashMap<Long, ItemSearchViewModel>()
                val summaryMediaIDHashMap = summaryList.associateBy { it.mediaId }
                /** 遍历中子返回的结果，如果中子的返回的结果中在便签返回的列表中，那么直接把该项添加到resultList列表中*/
                neuTronList.forEach {
                    summaryMediaIDHashMap[it.mediaId]?.let { item ->
                        DebugUtil.e(TAG, "Match summary media id:${it.mediaId}")
                        DebugUtil.e(TAG, "Match summary summary:${item.summaryText}")
                        it.summaryText = item.summaryText
                        item.summaryColorIndex.let { summaryColorIndex ->
                            it.summaryColorIndex = summaryColorIndex
                        }
                        it.noteId = item.noteId
                        it.recordUUID = item.recordUUID
                        it.isMatchTitle = true
                        it.isMatchSummary = true
                        resultMap[it.mediaId] = it
                    }
                }
                //summaryList、neuTronList的剩余项添加到resultList,保证加入的顺序为命中摘要+名称、命中摘要、命中名称
                val iteratorSummary = summaryList.iterator()
                while (iteratorSummary.hasNext()) {
                    val item = iteratorSummary.next()
                    if (!resultMap.contains(item.mediaId)) {
                        item.isMatchSummary = true
                        resultMap[item.mediaId] = item
                        addRecordInfoToSearchItem(item.mediaId, item)
                    }
                }
                val iteratorNeuTron = neuTronList.iterator()
                while (iteratorNeuTron.hasNext()) {
                    val item = iteratorNeuTron.next()
                    if (!resultMap.contains(item.mediaId)) {
                        item.isMatchTitle = true
                        resultMap[item.mediaId] = item
                    }
                }

                resultMap.values.toMutableList()
            }

        /*是否有查看摘要的数据存在：默认便签有结果数据就算有*/
        var viewSummaryFlag = summaryList.isNotEmpty()
        for (item in resultList) {
            if (item.noteId.isNullOrEmpty() || item.recordUUID.isNullOrEmpty()) {
                assignmentNoteIDAndUUID(item)
            }
            if (!viewSummaryFlag && !item.noteId.isNullOrBlank()) {
                viewSummaryFlag = true
            }
        }
        /*若有查看摘要的数据，则统计一次*/
        if (viewSummaryFlag) {
            SummaryStaticUtil.addShowViewSummaryEvent(SummaryStaticUtil.EVENT_FROM_SEARCH)
        }
        return resultList
    }

    @JvmStatic
    fun mergeInnerSummaryAndOuterSummary(
        innerSummaryList: MutableList<ItemSearchViewModel>,
        oldSummaryList: MutableList<ItemSearchViewModel>
    ): MutableList<ItemSearchViewModel> {
        val resultList =
            if (innerSummaryList.isEmpty()) {
                // 没有摘要返回媒体
                oldSummaryList
            } else if (oldSummaryList.isEmpty()) {
                innerSummaryList
            } else {
                val resultMap = LinkedHashMap<Long, ItemSearchViewModel>()
                val matchTitleResultMap = LinkedHashMap<Long, ItemSearchViewModel>()
                val matchSummaryResultMap = LinkedHashMap<Long, ItemSearchViewModel>()
                // 当前media还没有通先用uuid
                val summaryMediaIDHashMap = innerSummaryList.associateBy { it.mediaId }

                // 命中摘要+名称
                for (oldSummeryItem in oldSummaryList) {
                    // 中子记录中既有摘要就直接插入数据
                    if (oldSummeryItem.isMatchSummary && oldSummeryItem.isMatchTitle) {
                        resultMap[oldSummeryItem.mediaId] = oldSummeryItem
                    } else if (oldSummeryItem.isMatchTitle) {
                        val innerSummaryResult = summaryMediaIDHashMap[oldSummeryItem.mediaId]
                        if (innerSummaryResult == null || (!innerSummaryResult.isMatchSummary)) {
                            matchTitleResultMap[oldSummeryItem.mediaId] = oldSummeryItem
                        } else {
                            oldSummeryItem.summaryText = innerSummaryResult.summaryText
                            oldSummeryItem.summaryColorIndex = innerSummaryResult.summaryColorIndex
                            oldSummeryItem.noteId = innerSummaryResult.noteId
                            oldSummeryItem.recordUUID = innerSummaryResult.recordUUID
                            oldSummeryItem.isMatchTitle = true
                            oldSummeryItem.isMatchSummary = true
                            resultMap[oldSummeryItem.mediaId] = oldSummeryItem
                        }
                    } else if (oldSummeryItem.isMatchSummary) {
                        matchSummaryResultMap[oldSummeryItem.mediaId] = oldSummeryItem
                    } else {
                        DebugUtil.e(TAG, "Search result not match title and summary:${oldSummeryItem.mediaId}")
                    }
                }

                for (innerSummeryItem in innerSummaryList) {
                    // 中子记录中既有摘要就直接插入数据
                    if (!((!innerSummeryItem.isMatchSummary) || resultMap[innerSummeryItem.mediaId] != null
                        || matchSummaryResultMap[innerSummeryItem.mediaId] != null)) {
                        matchSummaryResultMap[innerSummeryItem.mediaId] = innerSummeryItem
                    }
                }

                resultMap.values.toMutableList().apply { addAll(matchSummaryResultMap.values.toMutableList()) }
                    .apply { addAll(matchTitleResultMap.values.toMutableList()) }
            }
        return resultList
    }

    /**
     * 内部摘要和外部摘要合并并补充媒体信息
     */
    @JvmStatic
    fun mergeNoteAndInnerSummary(
        innerSummaryList: MutableList<ItemSearchViewModel>,
        oldSummaryList: MutableList<ItemSearchViewModel>
    ): MutableList<ItemSearchViewModel> {
        if (innerSummaryList.isEmpty()) return oldSummaryList
        if (oldSummaryList.isEmpty()) return innerSummaryList
        val mergeSummaryItemMap = innerSummaryList.associateBy { it.mediaId }.toMutableMap()
        for (noteSummaryItem in oldSummaryList) {
            if (mergeSummaryItemMap[noteSummaryItem.mediaId] == null) {
                mergeSummaryItemMap[noteSummaryItem.mediaId] = noteSummaryItem
            }
        }
        return mergeSummaryItemMap.values.toMutableList()
    }

    @JvmStatic
    fun addRecordInfoToSearchItem(media: Long, itemSearchViewModel: ItemSearchViewModel) {
        val record = MediaDBUtils.getRecordFromMediaByUriId(MediaDBUtils.genUri(media))
        DebugUtil.i(TAG, "addRecordInfoToSearchItem mediaId:$media  record:${record?.displayName}")
        itemSearchViewModel.title = record?.displayName ?: ""
        itemSearchViewModel.mDuration = record?.duration ?: 0
    }
}


