package com.soundrecorder.browsefile.home.view.cloudtip

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class TipStatusGuideObserver(val lifecycleOwner: LifecycleOwner) : DefaultLifecycleObserver {

    private val TAG = "TipStatusGuideObserver"
    private var mCloudListener: ICloudSwitchChangeListener? = null
    private var globalStateChangeCallback: ICloudGlobalStateCallBack? = null
    private var browseAdapter: BrowseAdapter? = null
    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }
    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    init {
        registerCloudLister()
        registerGlobalStateCallback()
    }

    fun bindAdapter(browseAdapter: BrowseAdapter) {
        this.browseAdapter = browseAdapter
    }

    override fun onDestroy(owner: LifecycleOwner) {
        release()
        owner.lifecycle.removeObserver(this)
    }

    private fun removeGuideView() {
        DebugUtil.i(TAG, "<<< removeGuideView")
        browseAdapter?.setHeader(BrowseAdapter.TYPE_HEADER_CLOUD, null)
    }

    /**
     * 监听云同步开关状态变化
     */
    private fun registerCloudLister() {
        if (mCloudListener == null) {
            mCloudListener = object : ICloudSwitchChangeListener {
                override fun onToggle(isInitValue: Boolean, open: Boolean) {
                    DebugUtil.i(TAG, "onCloudSwitchChange open is $open cloud on ${cloudTipManagerAction?.isCloudSwitchOn()}")
                    if (open) {
                        removeGuideView()
                    }
                }
            }
        }
        cloudTipManagerAction?.registerCloudSwitchChangeListener(mCloudListener)
    }

    private fun registerGlobalStateCallback() {
        if (globalStateChangeCallback == null) {
            globalStateChangeCallback = object : ICloudGlobalStateCallBack {
                override fun onSuccess(changed: Boolean, support: Boolean, state: String?) {
                    if (changed && !support) {
                        lifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                            runCatching {
                                browseAdapter?.getHeadeView(BrowseAdapter.TYPE_HEADER_CLOUD_CONFIG)?.let {
                                    browseAdapter?.setHeader(BrowseAdapter.TYPE_HEADER_CLOUD_CONFIG, null)
                                }
                                browseAdapter?.getHeadeView(BrowseAdapter.TYPE_HEADER_CLOUD)?.let {
                                    if (it is CloudGuideTipView) {
                                        removeGuideView()
                                    }
                                }
                            }.onFailure {
                                DebugUtil.e(TAG, "global state success,remove guide view error $it")
                            }
                        }
                    }
                }
            }
        }
        globalStateChangeCallback?.let {
            cloudKitApi?.registerGlobalStateCallback(it)
        }
    }

    private fun release() {
        if (mCloudListener != null) {
            cloudTipManagerAction?.unregisterCloudSwitchChangeListener(mCloudListener)
            mCloudListener = null
        }
        globalStateChangeCallback?.let {
            cloudKitApi?.unregisterGlobalStateCallback(it)
        }
        globalStateChangeCallback = null
        browseAdapter = null
    }
}