package com.soundrecorder.browsefile.search.load.center.filechange

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.*
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector

object CenterFileChangeObserver {
    /**
     * same with it
     * @see RecorderRestorePlugin.LOCAL_ACTION_MOVE_RESTORE_COMPLETED
     */
    private const val ACTION_MOVE_FINISHED = "action.move.restore.completed"
    private const val TAG = "CenterFileChangeObserver"


    /**
     * 标记是否为冷启动，默认true
     * whether fist lunch app per time
     * true: default value, first enter app
     * false: not first enter app
     * */
    @Volatile
    private var mIsFirstEnter = true

    private var mHandlerThread: HandlerThread? = null
    private var mDmpWorkHandler: DmpWorkHandler? = null
    private var mMoveCompleteReceiver: BroadcastReceiver? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    /**
     * 该方法每次启动执行一次即可
     * 第一步：初始化中子服务、执行录音兜底建立索引
     * 1.获取媒体库所有数据
     * 2.初始化中子搜索服务+获取中子索引、检索版本号
     * 3.本地记录键值对（绝对路径-媒体库ID）
     * 4.录音兜底：计算出新增删除文件，调用中子 update、delete同步数据
     */
    fun initDmpSearch(): Boolean {
        DebugUtil.i(TAG, "initDmpSearch isFirstEnter $mIsFirstEnter")
        if (!mIsFirstEnter) {
            return false
        }
        // 记录冷启动值后，立即置为false，避免多次调用
        mIsFirstEnter = false
        if (!CenterDbUtils.isCenterSearchUsable()) {
            DebugUtil.i(TAG, "initDmpSearch failure for dmp not enalbe")
            return false
        }
        // 冷启动 isFirstEnter= true： 触发中子初始化 + 录音兜底
        initDmpWorkHander()
        // 初始化中子搜索服务
        mDmpWorkHandler?.sendEmptyMessage(DmpWorkHandler.MSG_WHAT_INIT_DMP)
        // 触发录音兜底
        triggerRecorderFullCompare(true)
        return true
    }

    /**
     * 触发录音兜底
     */
    fun triggerRecorderFullCompare(callDmpSyncWhenEmpty: Boolean = false, delayMill: Long = 0) {
        DebugUtil.i(TAG, "triggerRecorderFullCompare")
        initDmpWorkHander()
        if (callDmpSyncWhenEmpty) {
            mDmpWorkHandler?.let {
                val message = it.obtainMessage(DmpWorkHandler.MSG_WHAT_RECORDER_FULL_COMPARE)
                message.obj = callDmpSyncWhenEmpty
                it.sendMessage(message)
            }
        } else {
            mDmpWorkHandler?.let {
                if (!it.hasMessages(DmpWorkHandler.MSG_WHAT_RECORDER_FULL_COMPARE)) {
                    it.sendEmptyMessageDelayed(DmpWorkHandler.MSG_WHAT_RECORDER_FULL_COMPARE, delayMill)
                }
            }
        }
    }

    /**
     * 检查本地update 无mediaID的数据（收到文件update/create，但是没有查到媒体库的文件）
     */
    fun checkUnSyncDirtyUpdateData() {
        // 这里如果中子可用，mDmpWorkHandler就不可能为null，就不单独判断中子是否可用了
        // 若中子从可用变成无用，handler中有数据为empty拦截判断
        mDmpWorkHandler?.syncDirtyDataToDmp()
    }

    /**
     * 监听文件变更
     * receive onFileObserve event
     */
    fun onFileChange(event: Int, path: String?, allPath: String) {
        // 检查是否满足文件变更同步中子条件
        if (checkEventCanCallDmp(event, allPath) && CenterDbUtils.isCenterSearchUsableByFeature()) {
            DebugUtil.i(TAG, "fileChange event $event: path = $path")
            initDmpWorkHander()
            when (event) {
                /*remove file-dir*/
                FileObserver.DELETE_SELF, FileObserver.MOVE_SELF -> {
                    mDmpWorkHandler?.let {
                        val msg = it.obtainMessage(DmpWorkHandler.MSG_WHAT_FILE_DIR_DELETE)
                        msg.obj = allPath
                        it.sendMessage(msg)
                    }
                }
                /*remove file*/
                FileObserver.MOVED_FROM, FileObserver.DELETE -> {
                    mDmpWorkHandler?.let {
                        val msg = it.obtainMessage(DmpWorkHandler.MSG_WHAT_FILE_DELETE)
                        msg.obj = allPath
                        it.sendMessage(msg)
                    }
                }
                /*add or update file*/
                FileObserver.CREATE, FileObserver.MOVED_TO, FileObserver.MODIFY -> {
                    mDmpWorkHandler?.let {
                        val msg = it.obtainMessage(DmpWorkHandler.MSG_WHAT_FILE_UPDATE)
                        msg.obj = allPath
                        it.sendMessage(msg)
                    }
                }
            }
        }
    }

    /**
     * 用于录音内部文件操作变更数据及时同步，如：
     * 1. 重命名-媒体库ID不变
     * 2.一加wav/aac转换格式成功 - onfileobserve拦截转换中的状态，so转换完成后手动调用一次
     * 3.转文本成功
     */
    fun fileUpdateChangeSuccess(mediaId: Long, allPath: String? = null) {
        if (CenterDbUtils.isCenterSearchUsableByFeature()) {
            DebugUtil.i(TAG, "innerFileUpdateChanged...")
            initDmpWorkHander()
            mDmpWorkHandler?.fileUpdateChangeSuccess(mediaId, allPath)
        }
    }

    fun release() {
        DebugUtil.i(TAG, "release...")
        mDmpWorkHandler?.release()
        mDmpWorkHandler = null
        mHandlerThread?.quitSafely()
        mHandlerThread = null
        mIsFirstEnter = true
        mMoveCompleteReceiver?.let {
            LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).unregisterReceiver(it)
        }
        mMoveCompleteReceiver = null
    }

    @Synchronized
    private fun initDmpWorkHander() {
        if (mDmpWorkHandler == null) {
            mHandlerThread = HandlerThread("handler_thread").apply {
                this.start()
                mDmpWorkHandler = DmpWorkHandler(this.looper)
            }
            CoroutineUtils.doInMain {
                registerMoveCompleteReceiver()
            }
        }
    }

    private fun registerMoveCompleteReceiver() {
        if (mMoveCompleteReceiver == null) {
            mMoveCompleteReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    // 搬家数据恢复完成，通知中子全量同步一次
                    mDmpWorkHandler?.sendEmptyMessage(DmpWorkHandler.MSG_WHAT_DMP_FULL_COMPARE)
                }
            }.apply {
                val intentFilter = IntentFilter()
                intentFilter.addAction(ACTION_MOVE_FINISHED)
                LocalBroadcastManager.getInstance(BaseApplication.getAppContext())
                    .registerReceiver(this, intentFilter)
            }
        }
    }

    /**
     * 仅文件变更事件
     * 且不是过滤录音临时文件的变更
     */
    private fun checkEventCanCallDmp(event: Int, allPath: String): Boolean {
        val isDelete = event in arrayOf(FileObserver.MOVED_FROM,
            FileObserver.DELETE,
            FileObserver.MOVE_SELF,
            FileObserver.DELETE_SELF)
        val isInsertOrUpdate = event in arrayOf(FileObserver.CREATE, FileObserver.MOVED_TO, FileObserver.MODIFY)
        val isDeleteDir = event in arrayOf(FileObserver.MOVE_SELF, FileObserver.DELETE_SELF)

        if (!isDelete && !isInsertOrUpdate && !isDeleteDir) {
            return false
        }

        /**
         * 拦截录制临时文件
         * to intercept recording file change
         * */
        if (allPath.equals(recorderViewModelApi?.getFileBeingRecorded(), true)) {
            return false
        }

        return true
    }
}