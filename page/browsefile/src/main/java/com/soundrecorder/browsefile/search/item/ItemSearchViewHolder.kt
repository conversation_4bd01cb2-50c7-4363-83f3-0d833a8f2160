/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : ItemBrowseView.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.search.item

import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.TypefaceSpan
import android.view.View
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.ItemSearchBinding
import com.soundrecorder.browsefile.home.item.AbsItemBrowseViewHolder
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel


open class ItemSearchViewHolder(
    viewBinding: ItemSearchBinding,
    mBrowseViewListener: IBrowseViewHolderListener?
) :
    AbsItemBrowseViewHolder<ItemSearchBinding, ItemSearchViewModel>(
        viewBinding,
        mBrowseViewListener,
        false
    ) {
    companion object {
        private const val TAG = "ItemSearchViewHolder"
    }

    override var logTag: String = "ItemSearchView"
    private val searchColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary, 0)
    private val infoPrefixColor = COUIContextUtil.getColor(context, R.color.item_record_title_color)
    private val sizeProgressText = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp12)
    private val sizeSearchResultText = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp14)
    private val sizeSP14 = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp14)
    private val sizeSP16 = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp16)
    init {
        mItemRecordInfo = mBinding.itemInfo
        itemSummaryAndPlay = mBinding.itemSummaryAndPlay
        mItemPlayArea = itemSummaryAndPlay.itemPlayArea
        mItemPlayInfo = mBinding.itemPlayInfo
        mCardView = mBinding.clickScaleCardLayout
        initBasicView()
    }

    override fun <D> setViewModel(data: D) {
        mItemViewModel = data as ItemSearchViewModel
        taskId = mItemViewModel.taskId
    }

    override fun initBasicView() {
        super.initBasicView()
        initSummaryItem()
    }

    override fun bindView(isLastPosition: Boolean?) {
        super.bindView(isLastPosition)
        setSummaryButtonVisible(false)
    }

    private fun initSummaryItem() {
        mBinding.includeTextInfo.groupSummaryInfo.referencedIds.forEach {
            mBinding.includeTextInfo.summaryInfoRoot.findViewById<View>(it)?.setOnClickListener {
                if (ClickUtils.isQuickClick()) {
                    DebugUtil.i(logTag, "initSummaryItem isQuickClick")
                    return@setOnClickListener
                }
                browseViewListener?.onClickSummaryItem(mItemViewModel)
            }
        }

        mBinding.includeTextInfo.groupConvertInfo.referencedIds.forEach {
            mBinding.includeTextInfo.summaryInfoRoot.findViewById<View>(it)?.setOnClickListener {
                if (ClickUtils.isQuickClick()) {
                    DebugUtil.i(logTag, "convert click isQuickClick")
                    return@setOnClickListener
                }
                browseViewListener?.onClickConvertItem(mItemViewModel)
            }
        }
    }

    override fun onRootViewLongClick(v: View): Boolean {
        return false
    }

    override fun updateItemInfo() {
        mBinding.itemRecord = mItemViewModel
        mItemRecordInfo.recordTitle.text = getRecordTitle(mItemViewModel)
        setFont()
        if (mItemViewModel.content.isNullOrEmpty() && mItemViewModel.summaryText.isNullOrEmpty()) {
            // 搜索结果无转文本、无摘要内容
            mBinding.includeTextInfo.summaryInfoRoot.isVisible = false
            /*item底部space，无摘要转文本信息则显示*/
            mBinding.playSpace.isVisible = true
        } else {
            // 搜索结果有摘要or转文本内容
            mBinding.includeTextInfo.summaryInfoRoot.isVisible = true
            /*item底部space，有摘要or转文本信息则隐藏，使用的是摘要or文本自身高度*/
            mBinding.playSpace.isVisible = false
            // 摘要和转文本分割线，两个内容都存在才显示
            mBinding.includeTextInfo.dividerLineConvertTop.isGone = mItemViewModel.content.isNullOrEmpty()
                    || mItemViewModel.summaryText.isNullOrEmpty()
            setSummaryVisibleState()
            setConvertTextView()
        }
    }


    private fun setFont() {
        setTextToMaxH3(mBinding.itemInfo.recordTitle, sizeSP16)
        setTextToMaxH3(mBinding.itemInfo.recordDateModified, sizeSP14)
        setTextToMaxH3(mBinding.itemInfo.tvDurationLine, sizeSP14)
        setTextToMaxH3(mBinding.itemInfo.recordDuration, sizeSP14)
        setTextToMaxH3(mBinding.includeTextInfo.summaryText, sizeSearchResultText)
        setTextToMaxH3(mBinding.includeTextInfo.convertText, sizeSearchResultText)
        setTextToMaxH3(mBinding.itemPlayInfo.getPlayProgress(), sizeProgressText)
        setTextToMaxH3(mBinding.itemPlayInfo.getPlayTotalDuration(), sizeProgressText)
    }

    private fun setSummaryVisibleState() {
        if (mItemViewModel.summaryText().isNullOrEmpty() || mItemViewModel.noteId.isNullOrEmpty()) {
            mBinding.includeTextInfo.groupSummaryInfo.isVisible = false
        } else {
            mBinding.includeTextInfo.groupSummaryInfo.isVisible = true
            mItemViewModel.summaryText?.let {
                mBinding.includeTextInfo.summaryText.text =
                        getConvertText(context.getString(com.soundrecorder.common.R.string.summary_with_colon), it, mItemViewModel.summaryColorIndex)
            }
        }
    }

    private fun setConvertTextView() {
        if (mItemViewModel.content.isNullOrEmpty()) {
            mBinding.includeTextInfo.convertText.visibility = View.GONE
            mBinding.includeTextInfo.ivConvertMore.visibility = View.GONE
        } else {
            mBinding.includeTextInfo.convertText.text =
                getConvertText(
                    context.getString(com.soundrecorder.common.R.string.convert_with_colon),
                    mItemViewModel.content!!, mItemViewModel.contentColorIndex
                )
            mBinding.includeTextInfo.convertText.isVisible = true
            mBinding.includeTextInfo.ivConvertMore.isVisible = true
        }
    }

    override fun checkAudioInvalid(): Boolean {
        if (!mItemViewModel.isFileExist()) {
            ToastManager.showShortToast(
                BaseApplication.getAppContext(),
                com.soundrecorder.common.R.string.record_file_not_exist
            )
            return true
        }
        return false
    }

    private fun getRecordTitle(searchViewModel: ItemSearchViewModel): SpannableStringBuilder {
        val name: String = searchViewModel.title ?: searchViewModel.displayName.title() ?: ""

        val nameLength = name.length
        val spannableStringBuilder = SpannableStringBuilder(name)
        val colorIndex: ArrayList<Int> = searchViewModel.titleColorIndex

        if (searchViewModel.allContains) {
            if (colorIndex[1] >= nameLength) {
                colorIndex[1] = nameLength
            }
            if (colorIndex.size > 1 && colorIndex[1] > colorIndex[0]) {
                spannableStringBuilder.setSpan(
                    ForegroundColorSpan(searchColor),
                    colorIndex[0],
                    colorIndex[1],
                    Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        } else if (searchViewModel.isFromCenter) {
            setContentTextColorForCenter(spannableStringBuilder, 0, nameLength, colorIndex)
        } else {
            setContentTextColor(spannableStringBuilder, nameLength, colorIndex)
        }

        return spannableStringBuilder
    }

    /**
     * mark the covered keyword in convert text
     */
    private fun getConvertText(
        prefixContent: String,
        convertContent: String,
        contentColorIndex: ArrayList<Int>?
    ): SpannableStringBuilder {
        val spannableStringBuilder = SpannableStringBuilder(prefixContent + convertContent)
        setPrefixTextSpan(spannableStringBuilder, 0, prefixContent.length)
        setContentTextColorForCenter(
            spannableStringBuilder,
            prefixContent.length,
            spannableStringBuilder.length,
            contentColorIndex ?: arrayListOf()
        )
        return spannableStringBuilder
    }

    private fun setPrefixTextSpan(spanBuilder: SpannableStringBuilder, startIndex: Int, endIndex: Int): SpannableStringBuilder {
        spanBuilder.setSpan(
                ForegroundColorSpan(infoPrefixColor),
                startIndex,
                endIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        spanBuilder.setSpan(TypefaceSpan("sans-serif-condensed-medium"),
                startIndex, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        return spanBuilder
    }

    private fun setContentTextColor(
        spannableStringBuilder: SpannableStringBuilder,
        totalLength: Int,
        colorIndex: ArrayList<Int>
    ) {
        for (index in colorIndex) {
            var startIndex = index
            var endIndex = index + 1
            if (startIndex >= totalLength) {
                startIndex = totalLength
            }
            if (endIndex >= totalLength) {
                endIndex = totalLength
            }
            spannableStringBuilder.setSpan(
                ForegroundColorSpan(searchColor),
                startIndex,
                endIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }

    private fun setContentTextColorForCenter(
        spannableStringBuilder: SpannableStringBuilder,
        startX: Int,
        totalLength: Int,
        colorIndex: ArrayList<Int>
    ) {
        for (index in 0..colorIndex.size step (2)) {
            if (index + 2 > colorIndex.size) {
                break
            }
            var startIndex = colorIndex[index] + startX
            var endIndex = colorIndex[index + 1] + startX

            if (startIndex >= totalLength) {
                startIndex = totalLength
            }
            if (endIndex >= totalLength) {
                endIndex = totalLength
            }
            spannableStringBuilder.setSpan(
                ForegroundColorSpan(searchColor),
                startIndex,
                endIndex,
                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
    }

    override fun onViewRecycled() {
        super.onViewRecycled()
        mBinding.clickScaleCardLayout.onRelease()
    }
}