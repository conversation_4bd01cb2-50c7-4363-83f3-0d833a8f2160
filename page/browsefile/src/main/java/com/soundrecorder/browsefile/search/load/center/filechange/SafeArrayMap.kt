/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SafeArrayMap
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.filechange

import androidx.collection.ArrayMap
import androidx.collection.SimpleArrayMap

class SafeArrayMap<KEY, VALUE> : ArrayMap<KEY, VALUE>() {

    @Synchronized
    override fun put(key: KEY, value: VALUE): VALUE? {
        return super.put(key, value)
    }

    @Synchronized
    override fun putAll(array: SimpleArrayMap<out KEY, out VALUE>) {
        super.putAll(array)
    }

    @Suppress("TooGenericExceptionCaught")
    override fun get(key: KEY?): VALUE? {
        return try {
            super.get(key)
        } catch (e: Exception) {
            null
        }
    }

    @Synchronized
    override fun clear() {
        super.clear()
    }

    @Synchronized
    override fun removeAll(collection: Collection<*>): Boolean {
        return super.removeAll(collection)
    }
}