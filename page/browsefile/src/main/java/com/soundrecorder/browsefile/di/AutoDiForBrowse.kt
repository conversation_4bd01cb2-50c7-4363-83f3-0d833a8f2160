/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForBrowse.kt
 * * Description : AutoDiForBrowse
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.browsefile.di

import com.soundrecorder.browsefile.api.BrowseFileApi
import com.soundrecorder.modulerouter.BrowseFileInterface
import org.koin.dsl.module

object AutoDiForBrowse {
    val browseModule = module {
        single<BrowseFileInterface>(createdAtStart = true) {
            BrowseFileApi
        }
    }
}