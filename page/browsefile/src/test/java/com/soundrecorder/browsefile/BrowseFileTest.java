/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : BrowseFileTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/9/2
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/9/2, LI Kun, create
 ************************************************************/

package com.soundrecorder.browsefile;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import static org.robolectric.Shadows.shadowOf;

import android.os.Build;
import android.os.SystemClock;
import android.view.MenuItem;

import androidx.fragment.app.Fragment;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.browsefile.home.BrowseFragment;
import com.soundrecorder.browsefile.shadows.ShadowCOUIContextUtil;
import com.soundrecorder.browsefile.shadows.ShadowCOUIDarkModeUtil;
import com.soundrecorder.browsefile.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.browsefile.shadows.ShadowColorDarkModeUtil;
import com.soundrecorder.browsefile.shadows.ShadowCursorHelper;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowMultiFileObserver;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.browsefile.shadows.ShadowOplusUsbEnvironment;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;

import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {ShadowColorDarkModeUtil.class,
                ShadowCOUIDarkModeUtil.class, ShadowCOUIContextUtil.class,
                ShadowOplusUsbEnvironment.class, ShadowFeatureOption.class,
                ShadowMultiFileObserver.class, ShadowCOUIVersionUtil.class, ShadowOS12FeatureUtil.class})
public class BrowseFileTest {
    private static final String RUN_TOAST = "runToast";
    private static final String CURRENT_MODE = "mCurrentMode";
    private static final String GET_TEXT_FROM_CURRENT_MODE = "getTextFromCurrentMode";
    private static final String ACTION_SET_RINGTONE = "oplus.intent.action.settings.RINGTONE_SETTINGS";
    private static final int TWO_SENCONDS = 2000;
    private static long mTime = 2000L;
    private ActivityController<BrowseFile> mController;
    private BrowseFile mActivity;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(BrowseFile.class);
    }

    @Test
    public void should_initView_when_onCreate() throws Exception {
        mActivity = mController.get();
        List<Fragment> fragments = mActivity.getSupportFragmentManager().getFragments();
        assertEquals(0, fragments.size());
        mController.create().resume();
        fragments = mActivity.getSupportFragmentManager().getFragments();
        assertEquals(1, fragments.size());
        assertTrue(fragments.get(0) instanceof BrowseFragment);
    }

    @Test
    @Ignore
    @Config(sdk = Build.VERSION_CODES.S, shadows = ShadowCursorHelper.class)
    public void should_popDialog_when_menuItemSelectedSearch() {
        SystemClock.setCurrentTimeMillis(mTime += TWO_SENCONDS);
        BrowseFile browseFile = mController.create().start().resume().get();
        MenuItem mockMenuItem = mock(MenuItem.class);
        Mockito.when(mockMenuItem.getItemId()).thenReturn(R.id.item_search);
        browseFile.onOptionsItemSelected(mockMenuItem);
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNull(dialog);
        mController.stop().destroy();
    }

    @Test
    @Ignore
    @Config(sdk = Build.VERSION_CODES.S, shadows = ShadowCursorHelper.class)
    public void should_popDialog_when_onclicktoolbar_title() {
        SystemClock.setCurrentTimeMillis(mTime += TWO_SENCONDS);
        ShadowDialog dialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNull(dialog);
        mController.stop().destroy();
    }

    @After
    public void tearDown() {
        mController = null;
    }
}
