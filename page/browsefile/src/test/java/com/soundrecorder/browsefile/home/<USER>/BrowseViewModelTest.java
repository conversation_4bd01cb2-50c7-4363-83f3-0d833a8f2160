/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/01/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.browsefile.home.load;

import static com.soundrecorder.common.db.GroupInfoDbUtil.genDefaultAllGroupInfo;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;

import android.content.Context;
import android.os.Build;

import androidx.lifecycle.MutableLiveData;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.refresh.BounceLayout;
import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.home.BrowseFragment;
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel;
import com.soundrecorder.browsefile.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.browsefile.shadows.ShadowOplusUsbEnvironment;
import com.soundrecorder.browsefile.shadows.ShadowPermissionUtils;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.GroupInfoDbUtil;
import com.soundrecorder.common.fileobserve.ObserverController;
import com.soundrecorder.common.utils.RecordModeUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class,
        ShadowPermissionUtils.class, ShadowOplusUsbEnvironment.class, ShadowCOUIVersionUtil.class})
public class BrowseViewModelTest {

    private Context mContext;
    private BrowseViewModel mBrowseViewModel;
    private BrowseFragment mFragment;
    private ActivityController<BrowseFile> mController;
    private BrowseFile mActivity;

    @Before
    public void setUp() throws Exception {
        mContext = ApplicationProvider.getApplicationContext();
        mBrowseViewModel = new BrowseViewModel();
        mController = Robolectric.buildActivity(BrowseFile.class);
        mActivity = mController.create().resume().get();
        mFragment = (BrowseFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
    }

    @After
    public void tearDown() {
        mContext = null;
        mBrowseViewModel = null;
        mController.destroy();
        mController = null;
        mActivity = null;
        mFragment = null;
    }

    @Test
    public void should_initFile() {
        mBrowseViewModel.setCheckedMoveRecordFile(true);
        Assert.assertTrue(mBrowseViewModel.isCheckedMoveRecordFile());
        mBrowseViewModel.setFirstVisiblePosition(10);
        Assert.assertTrue(mBrowseViewModel.getFirstVisiblePosition() == 10);
        mBrowseViewModel.setDelayStartRecorder(true);
        Assert.assertTrue(mBrowseViewModel.isDelayStartRecorder());
        ObserverController controller = Mockito.spy(new ObserverController());
        mBrowseViewModel.setMObserverController(controller);
        Assert.assertNotNull(mBrowseViewModel.getMObserverController());
        mBrowseViewModel.setShowSearchUseMaxHeight(true);
        Assert.assertTrue(mBrowseViewModel.getShowSearchUseMaxHeight());
        mBrowseViewModel.setInitPush(true);
        Assert.assertTrue(mBrowseViewModel.isInitPush());
    }

    @Test
    public void should_refresh() {
        mBrowseViewModel.setInitPush(false);
        mBrowseViewModel.setCheckedMoveRecordFile(false);
        mBrowseViewModel.refresh(genDefaultAllGroupInfo(), false, null);
        mBrowseViewModel.setCheckedMoveRecordFile(true);
        mBrowseViewModel.refresh(genDefaultAllGroupInfo(), false, null);
        Assert.assertTrue(mBrowseViewModel.isInitPush());
        Assert.assertTrue(mBrowseViewModel.isCheckedMoveRecordFile());
    }

    @Test
    public void should_getShowRecordCount() {
        MutableLiveData liveData = new MutableLiveData();
        liveData.setValue(genDefaultAllGroupInfo());
        mBrowseViewModel.setCurrentGroup(liveData);
        Assert.assertTrue(mBrowseViewModel.getShowRecordCount() == 0);
        liveData.setValue(GroupInfoDbUtil.genDefaultCallGroupInfo());
        mBrowseViewModel.setCurrentGroup(liveData);
        Assert.assertTrue(mBrowseViewModel.getShowRecordCount() == 0);
    }

    @Test
    public void should_onClickStartRecorderActivity() {
        MockedStatic<RecordModeUtil> recordModeUtilMockedStatic = mockStatic(RecordModeUtil.class);
        recordModeUtilMockedStatic.when(() -> RecordModeUtil.isSupportMultiRecordMode(any())).thenReturn(true);
        mBrowseViewModel.onClickStartRecorderActivity(mFragment, false, false, false);
        mBrowseViewModel.onClickStartRecorderActivity(mFragment, true, true, true);
    }

    @Test
    public void should_releasePlayer() {
        mBrowseViewModel.releasePlayer();
    }

    @Test
    public void should_toggleEditMode() {
        int taskId = 998;
        MutableLiveData<Boolean> editData = new MutableLiveData<>(false);
        ItemBrowseRecordViewModel.Companion.getLiveEditMode().put(taskId, editData);
        mBrowseViewModel.setTaskId(taskId);
        mBrowseViewModel.toggleEditMode(false);
        Assert.assertTrue(ItemBrowseRecordViewModel.Companion.getLiveEditMode().get(taskId).getValue());

        mBrowseViewModel.toggleEditMode(false);
        Assert.assertFalse(ItemBrowseRecordViewModel.Companion.getLiveEditMode().get(taskId).getValue());

    }

    @Test
    public void should_selectAllOrNone() {
        mBrowseViewModel.selectAllOrNone();
    }

    @Test
    public void should_isPlaying() {
        Assert.assertTrue(!mBrowseViewModel.isPlaying());
    }

    @Test
    public void should_pausePlay() {
        mBrowseViewModel.pausePlay();
    }

    @Test
    public void should_replay() {
        mBrowseViewModel.replay();
    }

    @Test
    public void should_refreshEnable() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        mBrowseViewModel.refreshEnable(bounceLayout);
        int taskId = 1;
        ItemBrowseRecordViewModel.Companion.getLiveEditMode().put(taskId, new MutableLiveData(true));
        mBrowseViewModel.setTaskId(taskId);
        mBrowseViewModel.refreshEnable(bounceLayout);
    }

    @Test
    public void should_correctSelectRecordInEditMode() throws Exception {
        List<ItemBrowseRecordViewModel> dataList = new ArrayList<>();
        ConcurrentHashMap<Long, Record> selectMap = new ConcurrentHashMap<>();
        ItemBrowseRecordViewModel item1 = new ItemBrowseRecordViewModel();
        item1.setMediaId(1L);
        ItemBrowseRecordViewModel item2 = new ItemBrowseRecordViewModel();
        item2.setMediaId(2L);
        ItemBrowseRecordViewModel item3 = new ItemBrowseRecordViewModel();
        item3.setMediaId(3L);

        int taskId = 1;
        mBrowseViewModel.setTaskId(taskId);
        // 非编辑模式
        ItemBrowseRecordViewModel.Companion.getLiveEditMode().put(taskId, new MutableLiveData(false));
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().put(taskId, new MutableLiveData<>());
        dataList.add(item1);
        Whitebox.invokeMethod(mBrowseViewModel, "correctSelectRecordInEditMode", dataList);
        Assert.assertNull(ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).getValue());

        // 编辑模式下
        ItemBrowseRecordViewModel.Companion.getLiveEditMode().put(taskId, new MutableLiveData(true));
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().put(taskId, new MutableLiveData<>());

        // data 1.3， selectMap 1.2
        dataList.clear();
        selectMap.clear();
        dataList.add(item1);
        dataList.add(item3);
        selectMap.put(1L, item1.getRecord());
        selectMap.put(2L, item2.getRecord());
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).setValue(selectMap);
        Whitebox.invokeMethod(mBrowseViewModel, "correctSelectRecordInEditMode", dataList);
        Assert.assertEquals(1, ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).getValue().size());
        Assert.assertNotNull(ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).getValue().get(1L));
        // data 1， selectMap 1
        dataList.clear();
        selectMap.clear();
        dataList.add(item1);
        selectMap.put(1L, item1.getRecord());
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).setValue(selectMap);
        Whitebox.invokeMethod(mBrowseViewModel, "correctSelectRecordInEditMode", dataList);
        Assert.assertEquals(1, ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).getValue().size());

        // data 1， selectMap empty
        dataList.clear();
        selectMap.clear();
        dataList.add(item1);
        ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).setValue(selectMap);
        Whitebox.invokeMethod(mBrowseViewModel, "correctSelectRecordInEditMode", dataList);
        Assert.assertEquals(0, ItemBrowseRecordViewModel.Companion.getLiveSelectedMap().get(taskId).getValue().size());
    }

    @Test
    public void should_correct_when_findPositionByMediaId() {
        int mediaId = mBrowseViewModel.findPositionByMediaId(null);
        Assert.assertEquals(-1, mediaId);

        mediaId = mBrowseViewModel.findPositionByMediaId(0L);
        Assert.assertEquals(-1, mediaId);

        ItemBrowseRecordViewModel item = new ItemBrowseRecordViewModel();
        item.setMediaId(1L);
        mBrowseViewModel.getLiveDataList().setValue(List.of(item));
        mediaId = mBrowseViewModel.findPositionByMediaId(1L);
        Assert.assertEquals(0, mediaId);
    }

    @Test
    public void should_notNull_when_createModel() {
        Assert.assertNotNull(mBrowseViewModel.createModel());
    }

    @Test
    public void should_notNull_when_onDataReadyCompleted() {
        List<ItemBrowseRecordViewModel> dataList = new ArrayList<>();
        ItemBrowseRecordViewModel item = new ItemBrowseRecordViewModel();
        dataList.add(item);
        item = new ItemBrowseRecordViewModel();
        dataList.add(item);
        mBrowseViewModel.onDataReadyCompleted(mBrowseViewModel.createModel(), dataList, genDefaultAllGroupInfo(), null);
        Assert.assertTrue(mBrowseViewModel.getLiveDataList().getValue().size() > 0);
    }

    @Test
    public void should_correct_when_release() {
        String playPath = "1.mp3";
        String dataPath = "storage/0/music/standard record/1.mp3";
        mBrowseViewModel.release(playPath, dataPath, playPath, dataPath);
        Assert.assertNotNull(mBrowseViewModel.getFileChange().getValue());
    }

    @Test
    public void should_correct_when_exitEditMode() {
        int taskId = 999;
        MutableLiveData<Boolean> editData = new MutableLiveData<>(false);
        ItemBrowseRecordViewModel.Companion.getLiveEditMode().put(taskId, editData);
        mBrowseViewModel.setTaskId(taskId);

        mBrowseViewModel.exitEditMode();
        Assert.assertFalse(ItemBrowseRecordViewModel.Companion.getLiveEditMode().get(taskId).getValue());

        ItemBrowseRecordViewModel.Companion.getLiveEditMode().get(taskId).setValue(true);
        mBrowseViewModel.exitEditMode();
        Assert.assertFalse(ItemBrowseRecordViewModel.Companion.getLiveEditMode().get(taskId).getValue());
    }

    @Test
    public void should_correct_when_startFileObserver(){
        mBrowseViewModel.startFileObserver((event, path, allPath) -> {
            // do nothing
        });
        Assert.assertNotNull(mBrowseViewModel.getMObserverController());
    }

    @Test
    public void should_correct_when_stopFileObserver(){
        mBrowseViewModel.startFileObserver((event, path, allPath) -> {
            // do nothing
        });
        Assert.assertNotNull(mBrowseViewModel.getMObserverController());

        mBrowseViewModel.stopObserver();
        Assert.assertNull(mBrowseViewModel.getMObserverController());
    }

    @Test
    public void should_correct_when_onClear() {
        mBrowseViewModel.startFileObserver((event, path, allPath) -> {
            // do nothing
        });
        Assert.assertNotNull(mBrowseViewModel.getMObserverController());

        mBrowseViewModel.onCleared();
        Assert.assertNull(mBrowseViewModel.getMObserverController());
    }
}
