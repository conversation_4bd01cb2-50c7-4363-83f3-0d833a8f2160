package com.soundrecorder.browsefile.home.item;

import android.content.Context;
import android.os.Build;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.lifecycle.LifecycleKt;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.MutableLiveData;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.utils.WindowType;
import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.home.view.QuestionnaireGuideTipView;
import com.soundrecorder.browsefile.home.view.cloudtip.CloudGuideTipView;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.browsefile.shadows.ShadowOplusUsbEnvironment;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.StartPlayModel;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class BrowseAdapterTest {
    private Context mContext;
    private BrowseFile mActivity;


    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mActivity = Robolectric.buildActivity(BrowseFile.class).get();
    }

    @After
    public void clear() {
        mContext = null;
        mActivity = null;
    }


    @Test
    public void assert_not_null_when_create() {
        BrowseAdapter adapter = createAdapter();

        Assert.assertNotNull(adapter);
        Assert.assertNotNull(Whitebox.getInternalState(adapter, "mPlaceHolderView"));
    }

    private BrowseAdapter createAdapter() {
        LifecycleOwner lifecycleOwner = () -> mActivity.getLifecycle();
        return new BrowseAdapter(lifecycleOwner, mContext, new IBrowseViewHolderListener() {
            @Override
            public void onCallGroupMoreClick(@NonNull View view, @NonNull ItemBrowseRecordViewModel data) {
                // do nothing
            }

            @Override
            public void showSummaryTip(@Nullable View anchorView) {
                // do nothing
            }

            @Override
            public void onClickSummaryItem(@NonNull ItemBrowseRecordViewModel data) {
                // do nothing
            }

            @Override
            public void onClickConvertItem(@NonNull ItemBrowseRecordViewModel data) {
                // do nothing
            }

            @Override
            public void onClickSummaryIcon(@NonNull ItemBrowseRecordViewModel data) {
                // do nothing
            }

            @Override
            public void onLongClickItem(@NonNull View view, @NonNull ItemBrowseRecordViewModel data) {
                // do nothing
            }

            @Override
            public boolean canShowAddAnimator(@NonNull ItemBrowseRecordViewModel data) {
                return false;
            }

            @Override
            public boolean isCurrentPlay(@NonNull ItemBrowseRecordViewModel data) {
                return false;
            }

            @Override
            public void onClickItem(@NonNull ItemBrowseRecordViewModel data) {
            }

            @Nullable
            @Override
            public FastPlayHelper getPlayerController() {
                return null;
            }

            @NonNull
            @Override
            public MutableLiveData<WindowType> getWindowType() {
                return new MutableLiveData<>();
            }

            @NonNull
            @Override
            public MutableLiveData<StartPlayModel> getPlayLiveData() {
                return new MutableLiveData<>();
            }
        });
    }

    @Test
    public void assert_equals_when_setData() {
        BrowseAdapter adapter = createAdapter();
        List<ItemBrowseRecordViewModel> mockedList = new ArrayList<>();
        mockedList.add(new ItemBrowseRecordViewModel());
        mockedList.add(new ItemBrowseRecordViewModel());
        adapter.setData(mockedList, false);

        Assert.assertEquals(mockedList.size(), adapter.getRealItemCount());
    }

    @Test
    public void assert_equals_when_setFooter() {
        BrowseAdapter adapter = createAdapter();
        adapter.setFooter(10);
        Assert.assertEquals(10, adapter.getFooter().getLayoutParams().height);
    }

    @Test
    public void assert_equals_when_setPlaceHolder() {
        BrowseAdapter adapter = createAdapter();
        adapter.setPlaceHolder(10);
        View placeHolder = Whitebox.getInternalState(adapter, "mPlaceHolderView");
        Assert.assertEquals(10, placeHolder.getLayoutParams().height);
    }

    @Test
    public void assert_not_null_when_setHeader() {
        BrowseAdapter adapter = createAdapter();
        CloudGuideTipView headerView = new CloudGuideTipView(mActivity);
        headerView.bindAdapter(adapter);
        adapter.setHeader(BrowseAdapter.TYPE_HEADER_CLOUD, headerView);
        Map<Integer, View> singleHeader = Whitebox.getInternalState(adapter, "mSingHeader");
        Assert.assertNotNull(singleHeader.get(BrowseAdapter.TYPE_HEADER_CLOUD));
    }

    @Test
    public void assert_equals_when_getQuestionHeadCdpView() {
        BrowseAdapter adapter = createAdapter();
        Assert.assertNull(adapter.getHeadeView(BrowseAdapter.TYPE_HEADER_QUESTION));
        QuestionnaireGuideTipView headerView = new QuestionnaireGuideTipView(mActivity, LifecycleKt.getCoroutineScope(mActivity.getLifecycle()), null, null);
        headerView.addView(new CardView(mActivity));
        adapter.setHeader(BrowseAdapter.TYPE_HEADER_QUESTION, headerView);
        Assert.assertNotNull(adapter.getHeadeView(BrowseAdapter.TYPE_HEADER_QUESTION));

        headerView = new QuestionnaireGuideTipView(mActivity, LifecycleKt.getCoroutineScope(mActivity.getLifecycle()), null, null);
        adapter.setHeader(BrowseAdapter.TYPE_HEADER_QUESTION, headerView);
        Assert.assertNotNull(adapter.getHeadeView(BrowseAdapter.TYPE_HEADER_QUESTION));

        adapter.setHeader(BrowseAdapter.TYPE_HEADER_QUESTION, null);
        Assert.assertNull(adapter.getHeadeView(BrowseAdapter.TYPE_HEADER_QUESTION));
    }

    @Test
    public void assert_equals_when_deleteItems() {
        BrowseAdapter adapter = createAdapter();
        List<ItemBrowseRecordViewModel> mockedList = new ArrayList<>();
        ItemBrowseRecordViewModel model = new ItemBrowseRecordViewModel();
        model.setMediaId(1);
        mockedList.add(model);
        adapter.setData(mockedList, false);
        Assert.assertTrue(adapter.isContentNotEmpty());

        ArrayList<String> deleteIdList = new ArrayList<>();
        Record record = new Record();
        record.setId(1);
        deleteIdList.add(String.valueOf(record.getId()));
        adapter.deleteItems(deleteIdList, null);
        Assert.assertEquals(0, adapter.getRealItemCount());
    }

    @Test
    public void assert_equals_when_getItemCount() {
        BrowseAdapter adapter = createAdapter();
        Assert.assertEquals(2, adapter.getItemCount());
    }

    @Test
    public void assert_equals_when_getRealPosInViewType() {
        BrowseAdapter adapter = createAdapter();
        adapter.setHeader(BrowseAdapter.TYPE_HEADER_QUESTION,
                new QuestionnaireGuideTipView(mActivity, LifecycleKt.getCoroutineScope(mActivity.getLifecycle()), null, null));
        int position = -1;
        Assert.assertEquals(position, adapter.getRealPosInViewType(position));

        position = 0;
        Assert.assertEquals(0, adapter.getRealPosInViewType(position));

        position = 1;
        Assert.assertEquals(0, adapter.getRealPosInViewType(position));

        position = 2;
        Assert.assertEquals(0, adapter.getRealPosInViewType(position));
    }

    @Test
    public void assert_equals_when_getItemViewType() {
        BrowseAdapter adapter = createAdapter();
        CloudGuideTipView headerView = new CloudGuideTipView(mActivity);
        headerView.bindAdapter(adapter);
        adapter.setHeader(BrowseAdapter.TYPE_HEADER_CLOUD, headerView);
        List<ItemBrowseRecordViewModel> mockedList = new ArrayList<>();
        mockedList.add(new ItemBrowseRecordViewModel());
        mockedList.add(new ItemBrowseRecordViewModel());
        adapter.setData(mockedList, false);

        int position = 0;
        Assert.assertEquals(BrowseAdapter.TYPE_PLACEHOLDER, adapter.getItemViewType(position));

        position = 4;
        Assert.assertEquals(BrowseAdapter.TYPE_FOOTER, adapter.getItemViewType(position));

        position = 2;
        Assert.assertEquals(BrowseAdapter.TYPE_BROWSE, adapter.getItemViewType(position));

        position = 1;
        Assert.assertEquals(BrowseAdapter.TYPE_HEADER_CLOUD, adapter.getItemViewType(position));
        adapter.setHeader(BrowseAdapter.TYPE_HEADER_QUESTION,
                new QuestionnaireGuideTipView(mActivity, LifecycleKt.getCoroutineScope(mActivity.getLifecycle()), null, null));
        Assert.assertEquals(BrowseAdapter.TYPE_HEADER_QUESTION, adapter.getItemViewType(position));
    }

    @Test
    public void assert_equals_when_hideHeader() {
        BrowseAdapter adapter = createAdapter();
        adapter.hideCloudHeader();

        View headerView = new QuestionnaireGuideTipView(mActivity, LifecycleKt.getCoroutineScope(mActivity.getLifecycle()), null, null);
        adapter.setHeader(BrowseAdapter.TYPE_HEADER_QUESTION, headerView);
        adapter.hideCloudHeader();
        Assert.assertTrue(adapter.isHeaderNotEmpty());

        adapter.hideQuestionHeader(null);
        Assert.assertTrue(adapter.isHeaderNotEmpty());
    }

}